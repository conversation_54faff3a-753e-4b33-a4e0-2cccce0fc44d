Factory.register('invoicesComponent', function(sb){

	var //adminURL = 'https://api.voltz.software/_repos/_production/pagoda/_coredev/',
		comps = {},
		domObj = null,
		tableUI = {},
		balanceUI = {},
		contactUI = {},
		dueDate = moment(),
		invoiceList = [],
		invoicesTotal = 0,
		objectId = 0,
		mainContactId = 0,
		paymentCompletion = {},
		price = {},
		priceTotal = 0,
		setupOptions = {
			settingsView:true,
			simple:false,
			skipTemplates:false,
			tableTitle:{
				title:'<i class="fa fa-usd"></i> Invoices',
				size:'large'
			},
			mainContactId:0
		},
		systemSettings = {
			navigation:true,
			homeScreenView:false
		};

	var balanced = false;

	// new ui for the proposals project tool
	function applyPaymentTemplate(obj, template, doneCallback) {

		function deleteInvoices(currentInvoices, callback, count) {

			function deleteInvoice(id, callback) {

				sb.data.db.obj.erase('invoices', id, function(done){

					var currentInv = _.findWhere(currentInvoices, {id: id})

					if ( currentInv.payments ){

						sb.data.db.obj.erase('payments', currentInv.payments, function(resp){ });

					}

					callback(true);

				});

			}

			if(currentInvoices[count]){

				deleteInvoice(currentInvoices[count].id, function(done){

					count++;

					deleteInvoices(currentInvoices, callback, count);

				});

			}else{

				callback(true);

			}

		}

		if (balanced === false) {

			var menuId = obj.menu;
			if(obj.menu.id){
				menuId = obj.menu.id;
			}

			sb.data.db.obj.getWhere('invoices', {related_object:obj.id}, function(currentInvoices) {

				deleteInvoices(currentInvoices, function(done) {

					sb.data.db.obj.update('proposals', {id:obj.id, invoice_template:template.id}, function(updateProposal) {

						doneCallback();

					});

				});

			});

		} else {

			doneCallback();

		}

	}

	function createCollectionUI(dom, state, draw){

		sb.notify({
            type: 'show-collection',
            data: {
                domObj: dom,
                state: state,
                objectType: 'invoices',
                singleView: false,
                actions: {
                    view: false,
                    create: function(ui, obj_info, onComplete) {

						sb.data.db.obj.getBlueprint(obj_info.object_bp_type, function(bp){

							createInvoice.call(state, bp, ui, state, function(dom){

								dom.patch();

							}, function(newObj){

								if(state.onUpdate){
									state.onUpdate(newObj);
								}

								onComplete(newObj);

							});

						}, 1);

                    },
                    template:{
	                    id:'template',
	                    name:'Apply Template',
	                    label:'Apply Template',
	                    color:'blue',
	                    icon:'dollar sign',
	                    view:function(ui, obj_info, onComplete){

		                    chooseInvoiceTemplate.call(state, ui, 'proposals', state.onUpdate, onComplete);

	                    }
                    }
                },
                fields: {
                    name: {
                        title: 'Name'
                    },
                    status: {
                        title: 'Status',
                        view: function(dom, obj) {

	                        var color = 'yellow';
	                        var icon = '<i class="certificate icon"></i>';

	                        if(obj.balance == 0){
		                        color = 'green';
		                        icon = '<i class="check icon"></i>';
	                        }

							dom.makeNode('text', 'div', {css:'ui '+ color +' label', text:icon +' Due '+ moment(obj.due_date).local().format('M/D/YYYY')});

                        }
                    },
/*
                    invoice_type_list: {
                        title: 'Type',
                        view: function(dom, obj) {

                            var typeString = 'Not selected';
                            if(obj.invoice_type_list){
	                            typeString = obj.invoice_type_list.invoice_type;
                            }

							dom.makeNode('amount', 'div', {text:typeString});

                        }
                    },
*/
                    due_date: {
                        title: 'Payment Date',
                        view: function(dom, obj) {

							dom.makeNode('amount', 'div', {css:'', text:'Due '+ moment(obj.due_date).local().format('M/D/YYYY')});

                        }
                    },
                    amount: {
                        title: 'Total Amount',
                        view: function(dom, obj) {

							dom.makeNode('amount', 'div', {css:'ui right aligned', text:'Total: <b>$'+ (obj.amount/100).formatMoney(2) +'</b>'});

                        }
                    },
                    paid: {
                        title: 'Total Paid',
                        view: function(dom, obj) {

							dom.makeNode('amount', 'div', {css:'ui right aligned', text:'<span style:"text-align:left !important;">Paid:</span> <b>$'+ (obj.paid/100).formatMoney(2) +'</b>'});

                        }
                    },
                    balance: {
                        title: 'Balance',
                        view: function(dom, obj) {

							dom.makeNode('amount', 'div', {css:'ui right aligned bold', text:'Due: <b>$'+ (obj.balance/100).formatMoney(2) +'</b>'});

                        }
                    },
                    locked: {
                        title: 'Locked',
                        view: function(dom, obj) {

                            var typeString = '<i class="lock open icon"></i>';
                            if(obj.locked === 'locked'){
	                            typeString = '<i class="lock icon"></i>';
                            }

							dom.makeNode('amount', 'div', {css:'ui right aligned bold', text:typeString});

                        }
                    }
                },
                groupings: {
					type: 'invoices'
                },
                selectedView: 'table',
                where: {
	                related_object:state.objectId,
					childObjs: {
						name: true,
						date_created: true,
						invoice_type_list: true,
						amount: true,
						locked: true,
						balance:true,
						paid:true,
						due_date:true
					}
				}
            }
        });

	}

	function createPaymentsCollectionUI(dom, state, options){

		var collectionSetup = {
                domObj: dom,
                state: state,
                objectType: 'payments',
                singleView: {
                    view: function(ui, obj, draw) {

					sb.notify({
						type: 'app-navigate-to',
						data: {
							type: 'object',
							object: obj
						}
					});

                    }
                },
                actions: {
                    view: true,
                    template:{
	                    id:'template',
	                    name:'Apply Template',
	                    color:'blue',
	                    icon:'dollar sign',
	                    view:function(ui, obj_info, onComplete){

		                    chooseInvoiceTemplate.call(state, ui, 'proposals', state.onUpdate, onComplete);

	                    }
                    }
                },
                fields: {
                    txn: {
                        title: 'Transaction ID',
                        view: function(dom, obj) {

							dom.makeNode('amount', 'div', {css:'ui right aligned', text:obj.details.id});

                        }
                    },
                    date_created: {
                        title: 'Payment Date',
                        view: function(dom, obj) {

							dom.makeNode('amount', 'div', {css:'ui right aligned', text:moment(obj.date_created).local().format('M/D/YYYY h:mm a')});

                        }
                    },
                    amount: {
                        title: 'Total Amount',
                        view: function(dom, obj) {

							dom.makeNode('amount', 'div', {css:'ui right aligned', text:'$'+ (obj.amount/100).formatMoney(2)});

                        }
                    }
                },
                groupings: false,
                where: {
	                invoice:{
		                type:'or',
		                values:_.pluck(state.invoices, 'id')
	                },
					childObjs: {
						name: true,
						date_created: true,
						amount: true,
						details:true
					}
				}
            };

		if (options && options.collections) {

			_.map(options.collections, function(val, key){

				if (collectionSetup.hasOwnProperty([key])) {

					collectionSetup[key] = val;

				} else {

					collectionSetup[key] = val;
				}

			});

		}

		sb.notify({
            type: 'show-collection',
            data: collectionSetup
        });

	}

	function createInvoicesCollectionUI(dom, state, options){

		var collectionSetup = {
			domObj: dom
			, state: state
		}

		if (options && options.collections) {

			_.map(options.collections, function(val, key){

				if (collectionSetup.hasOwnProperty([key])) {

					collectionSetup[key] = val;

				} else {

					collectionSetup[key] = val;
				}

			});

		}

		sb.notify({
            type: 'show-collection',
            data: collectionSetup
        });

	}

	function addLineItems(obj, draw){

		this.empty();

		this.makeNode('contBreak', 'div', {text:'<br /><br />'});

		this.makeNode('cont', 'div', {css:'ui grid'});

		this.cont.makeNode('col1', 'column', {w:5});
		this.cont.makeNode('col2', 'column', {w:11, css:''});

		this.cont.makeNode('finalBreak', 'lineBreak', {});

		this.cont.col1.makeNode('title', 'headerText', {text:'Add a line item', size:'small'});

		this.cont.col1.makeNode('form', 'form', {
			itemName:{
				name:'itemName',
				label:'Item Name',
				type:'text',
				placeholder:'Consulting fee'
			},
			unitPrice:{
				name:'unitPrice',
				label:'Unit Price',
				type:'usd',
				placeholder:300
			},
			quantity:{
				name:'quantity',
				label:'Quantity',
				type:'number',
				value:1
			},
			tax:{
				name:'tax',
				label:'Tax (Enter whole percent ex. 12.5 = 12.5%)',
				type:'number'
			}
		});

		this.cont.col1.makeNode('formBreak', 'div', {text:'<br />'});

		this.cont.col1.makeNode('add', 'div', {tag:'button', text:'Add item', css:'ui green button'})
			.notify('click', {
				type:'invoicesRun',
				data:{
					run:function(dom){

						var formInfo = dom.cont.col1.form.process().fields;

						if(formInfo.itemName.value == ''){
							sb.dom.alerts.alert('Error', 'Please provide an item name.', 'error');
							return;
						}

						dom.cont.col1.add.loading();

						var newItem = {
								name:formInfo.itemName.value,
								amount:+formInfo.unitPrice.value,
								quantity:+formInfo.quantity.value,
								tax_rate:+formInfo.tax.value
							},
							invoiceTotal = 0;

						if(!obj.items){
							obj.items = [];
						}

						obj.items.push(newItem);

						_.each(obj.items, function(item){

							invoiceTotal += (item.amount * item.quantity);

							// add tax
							if(item.tax_rate){

								invoiceTotal += (item.amount * item.quantity) * (item.tax_rate / 100);

							}

						});

						sb.data.db.obj.update('invoices', {id:obj.id, items:obj.items, amount:Math.round(invoiceTotal), balance:(Math.round(invoiceTotal) - obj.paid)}, function(updated){

							var noteObj = {
									type_id: updated.id,
									type: 'invoices',
									note: newItem.name +' added for $'+ (newItem.amount/100).formatMoney(2) +'.',
									record_type:'log',
									author: sb.data.cookie.get('uid'),
									notifyUsers: []
								};

							sb.data.db.obj.create('notes', noteObj, function(newNote){

								buildInvoice.call(dom.cont.col2.cont, updated, true);

								dom.cont.col1.add.loading(false);
								dom.cont.col1.add.text('Added!');

								dom.cont.col1.form.patch();

								setTimeout(function(){

									dom.cont.col1.add.text('Add item');

								}, 1500);

							});

						}, 2);

					}.bind({}, this)
				}
			}, sb.moduleId);

		this.cont.col1.makeNode('cont', 'div', {css:'pda-container'}).makeNode('cont', 'div', {css:''});

		this.cont.col2.makeNode('break', 'lineBreak', {});

		this.cont.col2.makeNode('btns', 'buttonGroup', {css:''});

		this.cont.col2.makeNode('btnBreak', 'div', {text:'<br />'});

		this.cont.col2.makeNode('cont', 'div', {css:'pda-container'}).makeNode('cont', 'div', {css:''});

		this.cont.col2.cont.cont.makeNode('title', 'div', {title:'Create a line item', css:'ui small header'});

		this.cont.col2.cont.cont.makeNode('loader', 'loader', {});

		this.makeNode('modalContainer', 'div', {});

		this.patch();

		var dom = this;

		this.cont.col2.btns.makeNode('delete', 'button', {text:'Delete Invoice', css:'pda-btn-red'}).notify('click', {
			type:'invoicesRun',
			data:{
				run:function(obj){

					sb.dom.alerts.ask({
						title: 'Are you sure?',
						text: ''
					}, function(resp){

						if(resp){

							swal.disableButtons();

							sb.data.db.obj.erase('invoices', obj.id, function(don){

								swal.close();

								window.location.reload();

/*
								sb.notify({
									type:'app-remove-main-navigation-item',
									data:{
										itemId:'invoices',
										viewId:'single-'+obj.id
									}
								});

								sb.notify({
									type:'app-navigate-to',
									data:{
										itemId:'invoices',
										viewId:'table',
										redraw:true
									}
								});
*/

							});

						}

					});

				}.bind(this, obj)
			}
		}, sb.moduleId);

		this.cont.col2.btns.makeNode('edit', 'button', {text:'Edit Details', css:'pda-btn-orange'}).notify('click', {
			type:'invoicesRun',
			data:{
				run:function(obj){

					var modalDom = this;

					this.modalContainer.makeNode('modal', 'modal', {
						onShow:function(){

							modalDom.modalContainer.modal.body.makeNode('title', 'headerText', {text:'Edit Invoice Details', size:'small'});

							modalDom.modalContainer.modal.body.makeNode('break', 'lineBreak', {});

							modalDom.modalContainer.modal.body.makeNode('form', 'form', {
								name:{
									name:'name',
									label:'Invoice Name',
									type:'text',
									value:obj.name,
								},
								due_date:{
									name:'due_date',
									label:'Due Date',
									type:'date',
									dateType:'date',
									value:moment(obj.due_date).startOf('day'),
									dateFormat:'YYYY-MM-DD'
								},
								memo:{
									name:'memo',
									label:'Memo',
									type:'textbox',
									rows:6,
									value:obj.memo
								}
							});

							modalDom.modalContainer.modal.footer.makeNode('btns', 'buttonGroup', {});

							modalDom.modalContainer.modal.footer.btns.makeNode('close', 'button', {text:'Close', css:'pda-btnOutline-red'}).notify('click', {
								type:'invoicesRun',
								data:{
									run:function(){

										this.modalContainer.modal.hide();

									}.bind(modalDom)
								}
							}, sb.moduleId);

							modalDom.modalContainer.modal.footer.btns.makeNode('save', 'button', {text:'Save Changes', css:'pda-btn-green'}).notify('click', {
								type:'invoicesRun',
								data:{
									run:function(obj){

										var formInfo = this.modalContainer.modal.body.form.process();

										if(formInfo.completed == false){
											sb.dom.alerts.alert('Error', 'Please fill out the whole form', 'error');
											return;
										}

										this.modalContainer.modal.footer.btns.save.loading();
										this.modalContainer.modal.footer.btns.close.css.loading();

										var updateObj = {
												id:obj.id,
												due_date:formInfo.fields.due_date.value,
												name:formInfo.fields.name.value,
												memo:formInfo.fields.memo.value
											},
											dom = this;

										sb.data.db.obj.update('invoices', updateObj, function(updated){

											dom.modalContainer.modal.hide(function(hidden){});

											addLineItems.call(dom, updated, draw);

										}, 2);

									}.bind(modalDom, obj)
								}
							}, sb.moduleId);

							modalDom.modalContainer.modal.body.patch();
							modalDom.modalContainer.modal.footer.patch();

						}
					});
					this.modalContainer.patch();
					this.modalContainer.modal.show();

				}.bind(this, obj)
			}
		}, sb.moduleId);

		this.cont.col2.btns.makeNode('save', 'button', {text:'Done', css:'pda-btn-green'}).notify('click', {
			type:'invoicesRun',
			data:{
				run:singleInvoice.bind({}, obj, this, false, draw)
				//run:function(){tableUI.state.show();}
			}
		}, sb.moduleId);

		this.patch();

		buildInvoice.call(this.cont.col2.cont, obj, true);

/*
		sb.data.db.obj.getAll('inventory_billable_categories', function(categories){

			var catOptions = _.map(categories, function(c){
						return {name:c.name, value:c.id};
					}, []);

			catOptions.unshift({name:'Please Choose', value:0});

			dom.cont.col1.cont.cont.makeNode('form', 'form', {
				categories:{
					name:'categories',
					label:'Categories',
					type:'select',
					options:catOptions,
					change:function(field, newValue){

						if(newValue == 0){

							dom.cont.col1.cont.cont.list.empty();
							dom.cont.col1.cont.cont.list.patch();

						}else{

							dom.cont.col1.cont.cont.list.makeNode('tableBreak', 'div', {text:'<br />'});

							dom.cont.col1.cont.cont.list.makeNode('loader', 'loader', {});
							dom.cont.col1.cont.cont.list.patch();

							sb.data.db.obj.getWhere('inventory_billable_groups', {category:+newValue, childObjs:1}, function(items){

								delete dom.cont.col1.cont.cont.list.loader;

								dom.cont.col1.cont.cont.list.makeNode(
									'table',
									'table',
									{
										css: 'table-hover table-condensed',
										columns: {
											name:'Name',
											price:'Price',
											btns:''
										}
									}
								);

								_.each(items, function(item){

									dom.cont.col1.cont.cont.list.table.makeRow('items-'+item.id, [
										item.name,
										'$'+(item.price/100).formatMoney(2),
										''
									]);

									dom.cont.col1.cont.cont.list.table.body['items-'+item.id].btns.makeNode('add', 'button', {text:'<span class="text-center"><i class="fa fa-plus"></i></span>', css:'pda-btn-green'}).notify('click', {
										type:'invoicesRun',
										data:{
											run:function(obj, item){

												dom.cont.col1.cont.cont.list.table.body['items-'+item.id].btns.add.text('<i class="fa fa-circle-o-notch fa-spin"></i>');

												var newItem = {
														name:item.name,
														amount:item.price,
														quantity:1
													},
													invoiceTotal = 0;

												if(!obj.items){
													obj.items = [];
												}

												obj.items.push(newItem);

												_.each(obj.items, function(i){

													if(i.tax_rate){

														if(i.tax_rate > 0){

															invoiceTotal += ((i.amount * (i.tax_rate/100))) + i.amount;

														}else{

															invoiceTotal += i.amount;

														}

													}else{

														invoiceTotal += i.amount;

													}

												});

												sb.data.db.obj.update('invoices', {id:obj.id, items:obj.items, amount:Math.round(invoiceTotal), balance:(Math.round(invoiceTotal) - obj.paid)}, function(updated){

													var noteObj = {
															type_id: updated.id,
															type: 'invoices',
															note: newItem.name +' added for $'+ (newItem.amount/100).formatMoney(2) +'.',
															record_type:'log',
															author: sb.data.cookie.get('uid'),
															notifyUsers: []
														};

													sb.data.db.obj.create('notes', noteObj, function(newNote){

														buildInvoice.call(dom.cont.col2.cont, updated, true);

														dom.cont.col1.cont.cont.list.table.body['items-'+item.id].btns.add.text('<i class="fa fa-plus"></i>');

													});

												}, 2);

											}.bind(dom, obj, item)
										}
									}, sb.moduleId);

								});

								dom.cont.col1.cont.cont.list.patch();

							});

						}

					}
				}
			});

			dom.cont.col1.cont.cont.makeNode('list', 'div', {});

			dom.cont.col1.cont.cont.patch();

		});
*/

	}

	function balanceState(objectType, objectId, contactId, price, dueDate, data){

		function addToInvoice(invoices, balance){

			//this.empty();

			//this.makeNode('break', 'div', {text:'<br />'});

			if(this.cont){
				delete this.cont;
			}

			if(this.col){
				delete this.col;
			}

			this.makeNode('cont', 'div', {css:'ui bottom attached orange message'});

			this.cont.makeNode('title', 'div', {text:'Select an invoice', css:'ui huge header'});

			this.cont.makeNode('titleBreak', 'lineBreak', {});

			this.cont.makeNode('tableCont', 'div', {css:''});

			this.cont.tableCont.makeNode(
				'table',
				'table',
				{
					css: 'table-hover table-condensed orange',
					columns: {
						id: 'Invoice Name',
						type: 'Invoice Type',
						name: 'Current Balance',
						due_date: 'Due Date',
						btns: ''
					}
				}
			);

			_.each(invoices, function(inv){

				this.tableCont.table.makeRow(
					'row-'+inv.id,
					[inv.name, inv.type, '$' + (inv.balance/100).formatMoney(2), moment(inv.due_date).format('M/D/YYYY'), '']
				);

				this.tableCont.table.body['row-'+inv.id].btns.makeNode('select', 'div', {text:'Select and continue <i class="fa fa-arrow-right"></i>', css:'ui blue fluid button'})
					.notify('click', {
						type:'invoicesRun',
						data:{
							run:function(invoice, balance){

								sb.dom.alerts.ask({
									title: 'Are you sure?',
									text: ''
								}, function(resp){

									if(resp){

										swal.disableButtons();

										var totalAmount = 0,
											totalPaid = 0;

										invoice.amount += balance;
										invoice.balance += balance;

										if(!invoice.items){
											invoice.items = [];
										}

										invoice.items.push({
											amount:balance,
											name:'Balance adjustment on '+ moment().format('M/D/YYYY h:mm a'),
											quantity:1
										});

										sb.data.db.obj.update('invoices', invoice, function(newInvoice){

											var noteObj = {
													type_id: newInvoice.id,
													type: 'invoices',
													note: 'Balance adjustment on for $'+ (balance/100).formatMoney(2) +'.',
													record_type:'log',
													author: sb.data.cookie.get('uid'),
													notifyUsers: []
												};

											sb.data.db.obj.create('notes', noteObj, function(newNote){

												if(data.onUpdate){

													data.onUpdate(function(done){

														swal.close();
														tableUI.state.show();

													}, [newInvoice]);

												}else{

													swal.close();
													tableUI.state.show();

												}

											});

										});

									}

								});

							}.bind(this, inv, balance)
						}
					},sb.moduleId);

			}, this.cont);

			this.cont.tableCont.makeNode('btnBreak', 'div', {text:'<br />'});

			this.cont.tableCont.makeNode('btns', 'div', {css:'ui buttons'});

			this.cont.tableCont.btns.makeNode('cancel', 'button', {text:'<i class="fa fa-times"></i> Cancel', css:'pda-btn-red'}).notify('click', {
				type:'invoicesRun',
				data:{
					run:function(){

						balanceUI.state.show(invoices);

					}.bind(this.cont)
				}
			}, sb.moduleId);

			this.patch();

			//this.css('ui raised basic segment');

		}

		function addNewInvoice(invoices, balance){

			//this.makeNode('break', 'div', {text:'<br />'});

			if(this.col){
				delete this.col;
			}

			if(this.cont){
				delete this.cont;
			}

			this.makeNode('col', 'div', {css:'ui bottom attached orange message'})
				.makeNode('cont', 'div', {css:''});

			this.col.cont.makeNode('title', 'div', {text:'Create a new invoice', css:'ui huge header'});

			this.col.cont.makeNode('form', 'form', {
/*
				invoice_type_list:{
					name:'invoice_type_list',
					label:'Invoice Type',
					type:'select',
					options:_.map(bp.invoice_type_list.options, function(v,k){
						return {
								name:v,
								value:k
							};
					}, [])
				},
*/
				name:{
					name:'name',
					label:'Invoice Name',
					type:'text',
					value:'Balance Invoice '+ moment().format('M/D/YYYY')
				},
					amount:{
					name:'amount',
					label:'Amount',
					type:'usd',
					value:balance
				},

				due_date:{
					name:'due_date',
					label:'Due Date',
					type:'date'
				},
				memo:{
					name:'memo',
					label:'Memo',
					type:'textbox',
					rows:6,
					value:'This invoice was created to balance the charge of $'+ (balance/100).formatMoney(2) +'.'
				},
				related_object:{
					name:'related_object',
					type:'hidden',
					value:objectId
				},
				main_contact:{
					name:'main_contact',
					type:'hidden',
					value:mainContactId
				}
			});

			this.col.cont.makeNode('btns', 'div', {css:'ui buttons'});

			this.col.cont.btns.makeNode('save', 'button', {text:'<i class="fa fa-plus"></i> Create Invoice', css:'pda-btn-green'}).notify('click', {
				type:'invoicesRun',
				data:{
					run:function(invoices, balance){

						var formInfo = this.col.cont.form.process(),
							dom = this;

						if(formInfo.completed == false){
							sb.dom.alerts.alert('Error', 'Please fill out the entire form', 'error');
							return;
						}

						dom.col.cont.btns.save.loading();

						var newInvoice = {
								active:'Yes'
							};

						_.each(formInfo.fields, function(f,k){

							newInvoice[k] = f.value;

						});

						newInvoice.items = [
							{
								quantity:1,
								amount:newInvoice.amount,
								name:'Balance adjustment on '+ moment().format('M/D/YYYY h:mm a')
							}
						];

						newInvoice.main_contact = contactId;

						newInvoice.balance = newInvoice.amount;
						newInvoice.locked = 'not-locked';

						sb.data.db.obj.create('invoices', newInvoice, function(newObj){

							var noteObj = {
									type_id: newObj.id,
									type: 'invoices',
									note: 'Invoice created for balance adjustment on for $'+ (newInvoice.balance/100).formatMoney(2) +'.',
									record_type:'log',
									author: sb.data.cookie.get('uid'),
									notifyUsers: []
								};

							sb.data.db.obj.create('notes', noteObj, function(newNote){

								if(data.onUpdate){

									data.onUpdate(function(done){

										tableUI.state.show();

									}, [newObj]);

								}else{

									tableUI.state.show();

								}

							});

						}, 4);

					}.bind(this, invoices, balance)
				}
			}, sb.moduleId);

			this.col.cont.btns.makeNode('cancel', 'button', {text:'<i class="fa fa-times"></i> Cancel', css:'pda-btn-red'}).notify('click', {
				type:'invoicesRun',
				data:{
					run:function(){

						delete this.col;

						this.patch();

					}.bind(this)
				}
			}, sb.moduleId);

			this.patch();

		}

		function build(invoices){

			var totalPrice = 0,
				invoiceTotals = 0;

			if(invoices){
				invoiceTotals = _.reduce(invoices, function(memo, i){ return memo + i.amount; }, 0);
			}

			this.empty();

			this.patch();

			if(!invoices){
				invoices = [];
			}

			if(!_.isEmpty(price)){

				_.each(price, function(p, catId){

					totalPrice += p;

				});

				if(totalPrice != invoiceTotals){

					var balance = totalPrice - invoiceTotals,
						addBtnString = 'Add to an existing invoice',
						createBtnString = 'Balance the invoices',
						alertLine = 'Amount not yet invoiced';

					if(balance < 0){

						addBtnString = 'Subtract from an existing invoice';

					}

					if(balance > 0 && invoices.length > 0){
						alertLine = 'Amount not yet invoiced:';
					}

					if(balance < 0 && invoices.length > 0){
						alertLine = 'Amount needed to credit:';
					}

					if(balance < 0 && invoices.length == 0){
						alertLine = 'Amount needed to credit:';
						createBtnString = 'Create the first invoice';
					}

					this.makeNode('title', 'div', {css:'ui top attached orange icon message'});

					this.title.makeNode('icon', 'div', {tag:'i', css:'exclamation icon'});

					this.title.makeNode('content', 'div', {css:'content'});

					this.title.content.makeNode('header', 'div', {css:'header', text:alertLine +' $'+ (balance/100).formatMoney(2)});
					this.title.content.makeNode('p', 'div', {tag:'p', text:'You can add this amount to an existing invoice or create a new invoice.'});

					this.makeNode('btns', 'div', {css:'ui attached two fluid orange buttons'});

					if(invoices.length > 0){

						this.btns.makeNode('add', 'button', {text:addBtnString, css:''}).notify('click', {
							type:'invoicesRun',
							data:{
								run:addToInvoice.bind(this, invoices, balance)
							}
						}, sb.moduleId);

					}

					if(balance > 0){

						this.btns.makeNode('new', 'button', {text:createBtnString, css:''}).notify('click', {
							type:'invoicesRun',
							data:{
								run:addNewInvoice.bind(this, invoices, balance)
							}
						}, sb.moduleId);

					}

					this.patch();

				}

			}

		}

		function hide(){

			this.empty();

			this.patch();

			//this.css('');

		}

		this.state.show = build.bind(this);
		this.state.hide = hide.bind(this);

	}

	function bankingSetup(dom){

		function accountInfoForm(accountObj){

			function toggleAccountType (fields, selectedValue) {

				accountType = selectedValue;

				switch(selectedValue){

					case 'individual':

						_.map(fields.accountType.options, function(opt){

							if ( opt.name == 'Individual/Sole Proprietor' ){
								opt.selected = true;
							} else if (opt.selected) {
								delete opt.selected;
							}

							return opt;

						});

						fields.fname.type = 'text';
						fields.lname.type = 'text';
						fields.ssn.type = 'text';

						fields.business_name.type = 'hidden';
						fields.business_tax_id.type = 'hidden';

						break;

					case 'company':

						_.map(formArgs.accountType.options, function(opt){

							if ( opt.name == 'Business/Corporation' ){
								opt.selected = true;
							}  else if (opt.selected) {
								delete opt.selected;
							}

							return opt;

						});


						fields.fname.type = 'hidden';
						fields.lname.type = 'hidden';
						fields.ssn.type = 'hidden';

						fields.business_name.type = 'text';
						fields.business_tax_id.type = 'text';

						break;

				}

				dom.body.formCont.formColOne.makeNode('form', 'form', fields);

				dom.body.formCont.formColOne.patch();

			}

			var introText = 'We need to create an account for you to receive funds.',
				saveButtonText = 'Next Step <i class="fa fa-arrow-right"></i>',
				accountType = 'individual';

			var formArgs = {
					accountType:{
						name:'accountType',
						label:'Account Type',
						type:'select',
						options:[
							{
								name:'Individual/Sole Proprietor',
								value:'individual'
							},
							{
								name:'Business/Corporation',
								value:'company'
							}
						],
						change: toggleAccountType,
						value: accountType
					},
					business_name:{
						name:'business_name',
						label:'Business Name',
						type:'hidden'
					},
					business_tax_id:{
						name:'business_tax_id',
						label:'Business Tax Id',
						type:'hidden'
					},
					fname:{
						name:'fname',
						label:'First Name',
						type:'hidden'
					},
					lname:{
						name:'lname',
						label:'Last Name',
						type:'hidden'
					},
					ssn:{
						name:'ssn',
						label:'Social Security#/Personal ID#',
						type:'hidden',
						placeholder:'xxx-xx-xxxx'
					}
				};
			var formArgsTwo = {
					street:{
						name:'street',
						label:'Street Address',
						type:'text'
					},
					city:{
						name:'city',
						label:'City',
						type:'text'
					},
					state:{
						name:'state',
						label:'State',
						type:'text'
					},
					zip:{
						name:'zip',
						label:'Postal Code',
						type:'text'
					},
					statement_descriptor:{
						name:'statement_descriptor',
						label:'CC Statement Descriptor',
						type:'text',
						placeholder:'(What will appear on peoples\' credit card statements)'
					},
					support_email:{
						name:'support_email',
						label:'Support Email',
						type:'text'
					},
					support_phone:{
						name:'support_phone',
						label:'Support Phone',
						type:'text',
						placeholder:'************'
					}
				};

			if(accountObj){

				introText = 'Edit Account Settings';
				saveButtonText = 'Save Changes';

				formArgsTwo.statement_descriptor.value = accountObj.statement_descriptor;
				formArgsTwo.support_email.value = accountObj.support_email;
				formArgsTwo.support_phone.value = accountObj.support_phone;
				formArgsTwo.street.value = accountObj.legal_entity.address.line1 || '';
				formArgsTwo.city.value = accountObj.legal_entity.address.city || '';
				formArgsTwo.state.value = accountObj.legal_entity.address.state || '';
				formArgsTwo.zip.value = accountObj.legal_entity.address.postal_code || '';

				if(accountObj.legal_entity.type == 'company'){

					accountType = 'company';

					_.map(formArgs.accountType.options, function(opt){

						if ( opt.name == 'Business/Corporation' )
							opt.selected = true;

						return opt;

					});

					formArgs.business_name.type = 'text';
					formArgs.business_tax_id.type = 'text';

					if(accountObj.legal_entity.business_tax_id_provided == true){
						formArgs.business_tax_id.placeholder = 'Already Provided';
						formArgs.business_tax_id.value = 'Provided'
					}

					if(accountObj.business_name)
						formArgs.business_name.value = accountObj.business_name;

				}else if(accountObj.legal_entity.type == 'individual'){

					formArgs.fname.type = 'text';
					formArgs.lname.type = 'text';
					formArgs.ssn.type = 'text';

					if(accountObj.legal_entity.first_name)
						formArgs.fname.value = accountObj.legal_entity.first_name;

					if(accountObj.legal_entity.last_name)
						formArgs.lname.value = accountObj.legal_entity.last_name;

				}

			}

			dom.empty();

			dom.makeNode('body', 'div', {css:'small-container'});

			dom.body.makeNode('headerTopSpace', 'lineBreak', {spaces:1});
			dom.body.makeNode('intro', 'headerText', {css:'ui dividing header', text:introText, size:'small'});
			dom.body.makeNode('sp', 'lineBreak', {});
			dom.body.makeNode('formCont', 'container', {});
			dom.body.formCont.makeNode('formColOne', 'column', {w:16});
			dom.body.formCont.makeNode('formColTwo', 'column', {w:16});
			dom.body.makeNode('btnTopSpace', 'lineBreak', {spaces:2});
			dom.body.makeNode('btns', 'buttonGroup', {css:'small-container'});
			dom.body.makeNode('btnBottomSpace', 'lineBreak', {spaces:2});

			dom.body.formCont.formColOne.makeNode('form', 'form', formArgs);

			dom.body.formCont.formColTwo.makeNode('formTwo', 'form', formArgsTwo);

			if(accountObj){

				dom.body.btns.makeNode('cancel', 'button', {css:'ui red basic button', text:'Cancel'}).notify('click', {
					type:'invoicesRun',
					data:{
						run:bankingSetup.bind(dom, dom)
					}
				}, sb.moduleId);

				dom.body.btns.makeNode('erase', 'button', {css:'ui red button', text:'Delete Account'}).notify('click', {
					type:'invoicesRun',
					data:{
						run:eraseAccount.bind(dom, accountObj)
					}
				}, sb.moduleId);

			}

			dom.body.btns.makeNode('next', 'button', {css:'ui green button', text:saveButtonText}).notify('click', {
				type:'invoicesRun',
				data:{
					run:function(){

						processForm.call(dom, accountObj, accountType)

					}
				}
			}, sb.moduleId);

			dom.patch();

			toggleAccountType(dom.body.formCont.formColOne.form.properties.args, accountType);

		}

		function addPayoutAccount(stripeAccountID, backButton){

			var dom = this;

			sb.data.db.controller('getStripeConnectAccount', {
				accountId:stripeAccountID
			}, function(stripeAccount){

				if(stripeAccount == null){

					accountInfoForm.call(dom);

				}else{

					if(stripeAccount.external_accounts.data.length > 0 && !backButton){

						loadAccountSettingsDashboard.call(dom, stripeAccount.id);

					}else{

						var formObj = {
								account_holder_type:{
									name:'account_holder_type',
									label:'Account Type',
									type:'select',
									options:[
										{
											name:'Individual',
											value:'individual'
										},
										{
											name:'Company',
											value:'company'
										}
									]
								},
								account_holder_name:{
									name:'account_holder_name',
									label:'Account Holder Name',
									type:'text'
								},
								account_number:{
									name:'account_number',
									label:'Account Number',
									type:'text'
								},
								routing_number:{
									name:'routing_number',
									label:'Routing Number',
									type:'text'
								},
								country:{
									name:'country',
									type:'hidden',
									value:'US'
								},
								object:{
									name:'object',
									type:'hidden',
									value:'bank_account'
								},
								currency:{
									name:'currency',
									type:'hidden',
									value:'usd'
								},
								default_for_currency:{
									name:'default_for_currency',
									type:'hidden',
									value:true
								}
							};

						if(backButton){

							//formObj.default_for_currency.value = false;

						}

						dom.empty();

						dom.makeNode('body', 'div', {css: 'small-container'});

						dom.body.makeNode('headerSpacer', 'lineBreak', {spaces:1});
						dom.body.makeNode('btns', 'buttonGroup', {css:'pull-right'});
						dom.body.makeNode('title', 'headerText', {css:'ui dividing header', text:'Add a Payout Account'});
						dom.body.makeNode('cont', 'container', {}).makeNode('col', 'column', {w:16});

						if(backButton){

							dom.body.btns.makeNode('back', 'button', {text:'<i class="fa fa-times"></i> Cancel', css:'ui red basic button'}).notify('click', {
								type:'invoicesRun',
								data:{
									run:function(stripeAccount){

										this.body.btns.back.loading();

										loadAccountSettingsDashboard.call(this, stripeAccount.id);

									}.bind(dom, stripeAccount)
								}
							}, sb.moduleId);

						}

						dom.body.btns.makeNode('save', 'button', {text:'Next Step <i class="fa fa-arrow-right"></i>', css:'ui green button'}).notify('click', {
							type:'invoicesRun',
							data:{
								run:function(stripeAccount){

									var dom = this,
										formInfo = dom.body.cont.col.form.process();

									dom.body.btns.save.loading(true);

									var accountObj = {};

									_.each(formInfo.fields, function(f, name){

										accountObj[name] = f.value;

									});

									if(
										_.isEmpty(accountObj.account_holder_name)
										|| _.isEmpty(accountObj.account_number)
										|| _.isEmpty(accountObj.routing_number)
									){

										dom.body.btns.save.loading(false);
										sb.dom.alerts.alert('Error', 'Please fill out the entire form', 'error');
										return;

									}

									sb.data.db.controller('addAccountToConnectAccount', {
										accountId: stripeAccount.id,
										accountObj: accountObj
									}, function(updated){

										if(updated.id){

											loadAccountSettingsDashboard.call(dom, updated.id);

										}else{

											sb.dom.alerts.alert('Error', 'Please verify Account and Routing numbers', 'error');

											addPayoutAccount.call(dom, stripeAccount.id, true);

										}

									}, adminURL + '/api/');

								}.bind(dom, stripeAccount)
							}
						}, sb.moduleId);

						dom.body.cont.col.makeNode('form', 'form', formObj);

						dom.patch();

					}

				}

			});

		}

		function chooseAccountType(instanceObj){

			var dom = this;

			if(instanceObj.stripe_account_id){

				if(instanceObj.stripe_account_type == 'standard'){

					dom.empty();


					dom.makeNode('title', 'div'
						, {
							text:'Your Stripe account is connected. <span style="margin: 0 20px;"><i class="green check circle large icon"></i></span>'
							, css:'ui header'
						}
					);

					dom.makeNode('helper', 'div'
						, {
							text:'To bill clients and handle transfers to your bank account, please log in to your Stripe Account'
						}
					);

					dom.makeNode('logo', 'div', {css:'ui small image', style:'display:inline-block;', tag:'img', src:'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_applications/bento/welcome/stripe.svg'});

					dom.makeNode('disconnect', 'div', {css:'ui red basic button', text:'Disconnect'})
						.notify('click', {
							type:'invoicesRun',
							data:{
								run:function(){

									var dom = this;

									sb.dom.alerts.ask({
										title: 'Are you sure?',
										text: 'You can reconnect your account later if you change your mind.'
									}, function(resp){

										if(resp){

											swal.disableButtons();

											sb.data.db.obj.update('instances', {id:appConfig.id, stripe_account_id:'', stripe_account_type:''}, function(updated){

												swal.close();

												bankingSetup(dom);

											});

										}

									});

								}.bind(dom)
							}
						}, sb.moduleId);

					dom.patch();

				}else{

					accountInfoForm.call(dom);

				}

			}else{

				dom.empty();

				dom.makeNode('titleBreak', 'div', {text:'<br />'});

				//dom.makeNode('title', 'div', {css:'ui large header', text:'Please choose a banking account type.'});

				dom.makeNode('grid', 'div', {css:'ui centered grid'});

				dom.grid.makeNode('col1', 'div', {css:'six wide column'});
				dom.grid.makeNode('col3', 'div', {css:'one wide column'});
				dom.grid.makeNode('col2', 'div', {css:'six wide column'});

				dom.grid.col1.makeNode('card', 'div', {css:'ui basic segment'})
					.makeNode('logo', 'div', {css:'ui small centered image', tag:'img', src:'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_applications/bento/welcome/stripe.svg'});
				dom.grid.col1.card.makeNode('helper', 'div', {text:'Choose this option if you already have your own Stripe account. You\'ll be able to enter your Stripe API token to connect your account. Once connected, your Stripe account will be used to bill your clients. Transfers to your bank account will be handled by Stripe.'});
				dom.grid.col1.card.makeNode('titleBreak', 'div', {text:'<br />'});
				//dom.grid.col1.card.makeNode('link', 'div', {text:'Learn more about Stripe.', tag:'a', href:'https://stripe.com', target:'_blank'});
				//dom.grid.col1.card.makeNode('titleBreak2', 'div', {text:'<br />'});

				///LIVE CLIENT ID
				dom.grid.col1.card.makeNode('connect', 'div', {tag:'a', href:'https://connect.stripe.com/oauth/authorize?response_type=code&client_id=ca_EG1tBKVEZwC8VAgb6GGB06ov3QZyvmRQ&scope=read_write&state='+appConfig.instance})
					.makeNode('button', 'div', {tag:'img', css:'ui small image', src:'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_applications/bento/welcome/stripeconnect.png'});

/*
				///TEST CLIENT ID
				dom.grid.col1.card.makeNode('connect', 'div', {tag:'a', href:'https://connect.stripe.com/oauth/authorize?response_type=code&client_id=ca_EG1taTvIGs75HFo2FOtTgODUbyapCdAF&scope=read_write&state='+appConfig.instance})
					.makeNode('button', 'div', {tag:'img', css:'ui small image', src:'https://pagoda.nyc3.cdn.digitaloceanspaces.com/_applications/bento/welcome/stripeconnect.png'});
*/

				dom.grid.col2.makeNode('card', 'div', {css:'ui center segment'})
					.makeNode('logo', 'div', {css:'ui small centered image', tag:'img', src:'https://bento.infinityhospitality.net/img/logo-white-bg.png'});
				dom.grid.col2.card.makeNode('helper', 'div', {text:'Choose this option if you don\'t already have a merchant/payment processing account for your business. Choosing this option will allow Bento to charge your clients on your behalf. Money will be transferred to the bank account(s) you setup on a 2-day rolling basis.'});
				dom.grid.col2.card.makeNode('titleBreak', 'div', {text:'<br />'});
				dom.grid.col2.card.makeNode('button', 'div', {text:'Use Bento as your processor', css:'ui green centered button'})
					.notify('click', {
						type:'invoicesRun',
						data:{
							run:function(){

								var dom = this;

								accountInfoForm.call(dom);

							}.bind(dom)
						}
					}, sb.moduleId);

				dom.patch();

			}

		}

		function eraseAccount(accountObj){

			var dom = this;

			sb.dom.alerts.ask({
				title: 'Are you sure?',
				text: 'This cannot be undone and you will not be able to receive payments until you setup a new account.'
			}, function(resp){

				if(resp){

					swal.disableButtons();

					sb.data.db.controller('deleteStripeConnectAccountSource', {accountId:accountObj.id}, function(deletedObj){

						if(deletedObj.deleted == true){

							sb.data.db.obj.getAll('instances', function(instances){

								sb.data.db.controller('updateObject&pagodaAPIKey='+ instances[0].instance, {objectType:'instances', objectData:{id:instances[0].id, stripe_account_id:'', stripe_account_type:''}}, function(updatedObj){

									bankingSetup(dom);

									sb.dom.alerts.alert('Success', 'Your account has been deleted.', 'success');

								}, adminURL+'/api/_getAdmin.php?do=');

							});

						}else{

							sb.dom.alerts.alert('Error', 'You must have a zero balance before deleting your account.', 'error');

						}

					});

				}

			});

		}

		function loadAccountSettingsDashboard(stripeAccountID, instance){

			var dom = this;
			sb.data.db.controller('getStripeConnectAccount', {accountId:stripeAccountID}, function(stripeAccount){

				if (stripeAccount && stripeAccount.type == 'standard') {

					chooseAccountType.call(
						dom
						, {
							stripe_account_id: stripeAccount.id
							, stripe_account_type: stripeAccount.type
						}
					);

				} else {

					if(!stripeAccount){

						instance.stripe_account_id = null;

						chooseAccountType.call(dom, instance);

					}else{

						var payoutSchedule = 'No Payout Schedule';
						if(stripeAccount.payout)
							payoutSchedule = stripeAccount.payout_schedule.interval.toUpperCase() +' - '+ stripeAccount.payout_schedule.delay_days +' day rolling basis';
						var entityType = `<small>Business Name:</small> ${stripeAccount.business_name}`;
					     var entityId = `<small>Business ID:</small> Not Provided`;
						var businessName = (!_.isEmpty(stripeAccount.business_name)) ? stripeAccount.business_name : '<b><i>Not provided</i></b>';
						var email = (!_.isEmpty(stripeAccount.support_email)) ? stripeAccount.support_email : '<b><i>Not provided</i></b>';

						var address = stripeAccount.legal_entity.address.line1 || '';
						var city = stripeAccount.legal_entity.address.city || '';
						var stateAbbr = stripeAccount.legal_entity.address.state || '';
						var zipCode = stripeAccount.legal_entity.address.postal_code || '';
						var country = stripeAccount.legal_entity.address.country || '';

						var accountStatus = {
							message: 'Payments & Payouts are disabled.',
							css: 'ui orange message'
						};

						if(stripeAccount.charges_enabled == true){

							if(stripeAccount.payouts_enabled == true){

								accountStatus.message = 'Payments & Payouts enabled.',
								accountStatus.css = 'ui green message';

							}else{

								accountStatus.message = 'Payments are enabled. Payouts are disabled.';
								accountStatus.css = 'ui yellow message'
							}

						}

						if(stripeAccount.legal_entity.type == 'individual'){

							entityType = `<small>Representative Name:</small> ${stripeAccount.legal_entity.first_name} ${stripeAccount.legal_entity.last_name}`;

							if(stripeAccount.legal_entity.personal_id_number_provided == true)
								entityId = '<small>Personal ID / SS#:</small> Provided';
						}else{

							if(stripeAccount.legal_entity.business_tax_id_provided == true)
								entityId = `<small>Business ID:</small> Provided`;
						}

						dom.empty();

						dom.makeNode('body', 'div', {css:'ui grid container'});
						dom.body.makeNode('sp', 'div', {css:'sixteen wide column'});
						dom.body.sp.makeNode('message', 'div', {css: accountStatus.css, text: accountStatus.message});
						dom.body.makeNode('cont', 'div', {css:'sixteen wide column'});
						dom.body.cont.makeNode('info', 'div', {css:'ui basic segment grid'});
						dom.body.cont.info.makeNode('btnhdr', 'div', {css: 'sixteen wide column'});
						dom.body.cont.info.btnhdr.makeNode('edit', 'div', {css:'ui orange button right floated', text:'Edit'}).notify('click', {
							type:'invoicesRun',
							data:{
								run:accountInfoForm.bind(dom, stripeAccount)
							}
						}, sb.moduleId);
						dom.body.cont.info.makeNode('lCol', 'div', {css:'eight wide column'});
						dom.body.cont.info.makeNode('rCol', 'div', {css:'four wide column'});
						dom.body.cont.info.rCol.makeNode('accheader', 'div', {css: 'ui dividing header', text: 'Account Info' });
						dom.body.cont.info.rCol.makeNode('id', 'text', {text:'<small>Account ID:</small> '+ stripeAccount.id});
						dom.body.cont.info.rCol.makeNode('payoutSchedule', 'text', {text:'<small>Payout Schedule:</small> '+ payoutSchedule});
						dom.body.cont.info.rCol.makeNode('statementDescriptor', 'text', {text:'<small>Statement Descriptor:</small> '+ stripeAccount.statement_descriptor});
						dom.body.cont.info.rCol.makeNode('email', 'text', {text:'<small>Support Email:</small> '+ stripeAccount.support_email});
						dom.body.cont.info.rCol.makeNode('phone', 'text', {text:'<small>Support Phone:</small> '+ stripeAccount.support_phone});
						dom.body.cont.info.lCol.makeNode('entheader', 'div', {css: 'ui dividing header', text: 'Legal Entity' });
						dom.body.cont.info.lCol.makeNode('entity', 'text', {text: entityType});
						dom.body.cont.info.lCol.makeNode('entityID', 'text', {text: entityId});
						dom.body.cont.info.lCol.makeNode('addhead', 'div', {css: 'ui dividing header', text:'Address'});
						dom.body.cont.info.lCol.makeNode('address', 'text'
							, {
								text:'<address>'+ address +'<br />'+ city +' '+ stateAbbr +' '+ zipCode +'  '+ country +'</address>'
							}
						);
						dom.body.cont.info.lCol.makeNode('sp', 'lineBreak', {});


						// External Accounts
						dom.body.cont.makeNode('accounts', 'container', {collapse:true, title:'Payout Accounts', css:'small-container ui one column grid'});
						dom.body.cont.accounts.makeNode('btns', 'buttonGroup', {});
						dom.body.cont.accounts.btns.makeNode('add', 'button', {text:'<i class="fa fa-plus"></i> Add Account', css:'ui green button'}).notify('click', {
							type:'invoicesRun',
							data:{
								run:function(stripeAccount){

									this.body.cont.accounts.btns.add.loading();

									addPayoutAccount.call(this, stripeAccount.id, true);

								}.bind(dom, stripeAccount)
							}
						}, sb.moduleId);

						if ( stripeAccount.external_accounts.data.length > 0 ) {
							// print each account
							_.each(stripeAccount.external_accounts.data, function(a, k){

								var cardColor = '';
								if(a.default_for_currency === true){
									cardColor = 'blue ';
								}

								dom.body.cont.accounts.makeNode('accountCard-'+k, 'div', {css:'ui fluid '+ cardColor +'card'});
								dom.body.cont.accounts['accountCard-'+k].makeNode('content', 'div', {css:'content'});
								if(a.default_for_currency === true){
									dom.body.cont.accounts['accountCard-'+k].makeNode('label', 'div', {css:'ui top right attached label blue', text: a.currency.toUpperCase() +' Primary'});
								}
								dom.body.cont.accounts['accountCard-'+k].content.makeNode('header', 'div', {css:'header', text: a.bank_name});
								dom.body.cont.accounts['accountCard-'+k].content.makeNode('info', 'div', {css:'meta'});

								dom.body.cont.accounts['accountCard-'+k].content.info.makeNode('accountNumber', 'div', {tag:'span', text:'Account Number'});
								dom.body.cont.accounts['accountCard-'+k].content.info.makeNode('accountInfo', 'div', {tag:'p', text: '****'+ a.last4});

								dom.body.cont.accounts['accountCard-'+k].content.info.makeNode('fingerprint', 'div', {tag:'span', text:'Fingerprint'});
								dom.body.cont.accounts['accountCard-'+k].content.info.makeNode('fingerprintInfo', 'div', {tag:'p', text: a.fingerprint});

								dom.body.cont.accounts['accountCard-'+k].content.info.makeNode('type', 'div', {tag:'span', text:'Type'});
								dom.body.cont.accounts['accountCard-'+k].content.info.makeNode('typeInfo', 'div', {tag:'p', text: a.account_holder_type.toUpperCase()});

								dom.body.cont.accounts['accountCard-'+k].content.info.makeNode('currency', 'div', {tag:'span', text:'Currency'});
								dom.body.cont.accounts['accountCard-'+k].content.info.makeNode('currencyInfo', 'div', {tag:'p', text: a.currency.toUpperCase()});

								if(a.default_for_currency !== true){

									dom.body.cont.accounts['accountCard-'+k].content.info.makeNode('btns', 'div', {css:'small ui buttons pull-right'});
									dom.body.cont.accounts['accountCard-'+k].content.info.btns.makeNode('defaultStatus', 'button', {css:'ui blue button', text:'Make Default'}).notify('click', {
										type:'invoicesRun',
										data:{
											run:function(stripeAccount, account, button){

												var dom = this;

												button.text('<i class="fa fa-circle-o-notch fa-spin"></i>');
												button.css('pda-btn-primary');

												sb.data.db.controller('updateStripeConnectPayoutAccount', {accountId:stripeAccount.id, payoutAccountId:account.id}, function(stripeAccount){

													loadAccountSettingsDashboard.call(dom, stripeAccount.id);

												});

											}.bind(dom, stripeAccount, a, dom.body.cont.accounts['accountCard-'+k].content.info.btns.defaultStatus)
										}
									}, sb.moduleId);
									dom.body.cont.accounts['accountCard-'+k].content.info.btns.makeNode('or', 'div', {css:'or'});
									dom.body.cont.accounts['accountCard-'+k].content.info.btns.makeNode('erase', 'button', {css:'ui red button', text:'Delete'}).notify('click', {
										type:'invoicesRun',
										data:{
											run:function(stripeAccount, account, button){

												var dom = this,
													question = '';

												if(account.default_for_currency === true){
													question = 'You need to create a new account before you can delete this account. Would you like to continue?';
												}

												sb.dom.alerts.ask({
													title: 'Are you sure?',
													text: question
												}, function(resp){

													if(resp){

														swal.disableButtons();

														// you can't delete an account when; 1. It's the last account for the currency and the currency is the default account current. 2. account.default_for_currency is marked true.
														if(account.default_for_currency === true){

															swal.close();

															addPayoutAccount.call(dom, stripeAccount.id, true);

														}else{

															sb.data.db.controller('deleteStripeConnectPaymentSource', {accountId:stripeAccount.id, sourceId:account.id}, function(stripeAccount){

																loadAccountSettingsDashboard.call(dom, stripeAccount.id);

																swal.close();

															});

														}

													}

												});

											}.bind(dom, stripeAccount, a, dom.body.cont.accounts['accountCard-'+k].content.info.btns.erase)
										}
									}, sb.moduleId);

								}else{

									dom.body.cont.accounts['accountCard-'+k].content.info.makeNode('erase', 'button', {css:'small ui right floated red basic button', text:'Delete'}).notify('click', {
										type:'invoicesRun',
										data:{
											run:function(stripeAccount, account, button){

												var dom = this,
													question = '';

												if(account.default_for_currency === true){
													question = 'You need to create a new account before you can delete this account. Would you like to continue?';
												}

												sb.dom.alerts.ask({
													title: 'Are you sure?',
													text: question
												}, function(resp){

													if(resp){

														swal.disableButtons();

														// you can't delete an account when; 1. It's the last account for the currency and the currency is the default account current. 2. account.default_for_currency is marked true.
														if(account.default_for_currency === true){

															swal.close();

															addPayoutAccount.call(dom, stripeAccount.id, true);

														}else{

															sb.data.db.controller('deleteStripeConnectPaymentSource', {accountId:stripeAccount.id, sourceId:account.id}, function(stripeAccount){

																loadAccountSettingsDashboard.call(dom, stripeAccount.id);

																swal.close();

															});

														}

													}

												});

											}.bind(dom, stripeAccount, a, dom.body.cont.accounts['accountCard-'+k].content.info.erase)
										}
									}, sb.moduleId);

								}

							});

						}

						dom.patch();

					}

				}

			});

		}

		function processForm(accountObj, accountType){

			var formOneData = this.body.formCont.formColOne.form.process(),
			    formTwoData = this.body.formCont.formColTwo.formTwo.process(),
			    dom = this;

			var stripeAccount = {
					business_name:'',
					support_email:'',
					support_phone:'',
					statement_descriptor:'',
					legal_entity:{
						type: '',
						first_name:'',
						last_name:'',
						personal_id_number:'',
						address:{
							line1:'',
							city:'',
							state:'',
							postal_code:'',
							country:'US'
						}
					}
				};

			if(accountType == 'individual'){

				var formSocialSecurity = formOneData.fields.ssn.value.replace(/-/g, '');

				if(
					_.isEmpty(formOneData.fields.fname.value)
					|| _.isEmpty(formOneData.fields.lname.value)
					|| _.isEmpty(formOneData.fields.ssn.value)
				){

					sb.dom.alerts.alert('Error', 'Please fill out the entire form', 'error');
					return;

				}

				if ( formSocialSecurity.length !== 9 ){

					sb.dom.alerts.alert('Error', 'Please verify your Social Security Number.', 'error');
					return;

				}

				delete stripeAccount.business_name;
				delete stripeAccount.legal_entity.business_tax_id;

				stripeAccount.legal_entity.first_name = formOneData.fields.fname.value;
				stripeAccount.legal_entity.last_name = formOneData.fields.lname.value;
				stripeAccount.legal_entity.personal_id_number = formSocialSecurity;

			}else if(accountType == 'company'){

				if(_.isEmpty(formOneData.fields.business_name.value) || _.isEmpty(formOneData.fields.business_tax_id.value)){

					sb.dom.alerts.alert('Error', 'Please fill out the entire form', 'error');
					return;
				}

				delete stripeAccount.legal_entity.personal_id_number;
				delete stripeAccount.legal_entity.first_name;
				delete stripeAccount.legal_entity.last_name;

				stripeAccount.business_name = formOneData.fields.business_name.value;

				if(
					formOneData.fields.business_tax_id.value != 'Provided'
					&& !accountObj.legal_entity.business_tax_id_provided
				){
					stripeAccount.legal_entity.business_tax_id = formOneData.fields.business_tax_id.value;
				}else{
					delete stripeAccount.legal_entity.business_tax_id;
				}

			}

			var formSupportPhone = formTwoData.fields.support_phone.value.replace(/-/g, '');

			if(
				_.isEmpty(formTwoData.fields.statement_descriptor.value)
				|| _.isEmpty(formTwoData.fields.support_email.value)
				|| _.isEmpty(formTwoData.fields.support_phone.value)
			){

				sb.dom.alerts.alert('Error', 'Please fill out the entire form', 'error');
				return;

			}

			if ( formSupportPhone.length !== 10 ){

				sb.dom.alerts.alert('Error', 'Please verify your Support Telephone Number.', 'error');
				return;

			}

			stripeAccount.support_email = formTwoData.fields.support_email.value;

			stripeAccount.support_phone = formSupportPhone;


			stripeAccount.statement_descriptor = formTwoData.fields.statement_descriptor.value;
			stripeAccount.legal_entity.type = accountType;
			stripeAccount.legal_entity.address.line1 = formTwoData.fields.street.value;
			stripeAccount.legal_entity.address.city = formTwoData.fields.city.value;
			stripeAccount.legal_entity.address.state = formTwoData.fields.state.value;
			stripeAccount.legal_entity.address.postal_code = formTwoData.fields.zip.value;

			if(stripeAccount.statement_descriptor.length > 22)
				stripeAccount.statement_descriptor = stripeAccount.statement_descriptor.substring(0, 22);

			dom.body.btns.next.loading(true);

			sb.data.db.controller('getIPAddress', {}, function(ipAddress){

				if(accountObj){

					stripeAccount.id = accountObj.id;

					sb.data.db.controller('updateStripeConnectAccount', {accountInfo:stripeAccount}, function(updatedAccountObj){

						loadAccountSettingsDashboard.call(dom, updatedAccountObj.id);

					});

				}else{

					stripeAccount.type = 'custom';
					stripeAccount.legal_entity.type = accountType;
					stripeAccount.tos_acceptance = {
						date:moment().unix(),
						ip:ipAddress
					};

					sb.data.db.obj.getAll('instances', function(instances){

						//stripeAccount.statement_descriptor = instances[0].systemName;

						sb.data.db.controller('createStripeConnectAccount', {accountInfo:stripeAccount}, function(newAccountObj){

							if (newAccountObj) {

								sb.data.db.controller('updateObject&pagodaAPIKey='+ instances[0].instance
									, {
										objectType:'instances'
										, objectData:{
											id:instances[0].id
											, stripe_account_id:newAccountObj.id
											, stripe_account_type: newAccountObj.type
										}
									}, function(updatedObj){

									addPayoutAccount.call(dom, newAccountObj.id, true);

								}, adminURL+'/api/_getAdmin.php?do=');

							} else {

								sb.dom.alerts.alert('', 'Something went wrong. Please try again.', 'error');

								dom.body.btns.next.loading(false);

							}

						});

					});

				}

			});

		}

		if(!dom){

			this.state = {};

			this.state.newBankAccount = addPayoutAccount;

		}else{

			dom.empty();

			dom.makeNode('loader', 'loader', {});

			dom.patch();

			sb.data.db.obj.getAll('instances', function(instances){

				var instance = instances[0];

				if (+instance.stripe_account_id != 0 || !_.isEmpty(instance.stripe_account_id)) {

					loadAccountSettingsDashboard.call(dom, instance.stripe_account_id, instance)

				} else {

					chooseAccountType.call(dom, instance);

				}

			});

		}

	}

	function billingAddressSetup(dom){

		sb.data.db.obj.getAll('invoice_system', function(settingsList){

			var settings = settingsList[0],
				formArgs = {
					info:{
						name:'info',
						label:'Street',
						type:'text',
					},
					city:{
						name:'city',
						label:'City',
						type:'text',
					},
					state:{
						name:'state',
						label:'State',
						type:'select',
						options:sb.data.stateArray
					},
					zip:{
						name:'zip',
						label:'Zip/Postal Code',
						type:'text',
					},
					country:{
						name:'country',
						label:'Country',
						type:'text',
					},
				};

			if(settings){

				if(settings.billing_address){

					_.each(formArgs, function(field, name){

						field.value = settings.billing_address[name];

					});

				}
			}

			dom.empty();

			dom.makeNode('body', 'div', {css:'small-container'});

			dom.body.makeNode('headerSpace', 'lineBreak', {spaces:1});
			dom.body.makeNode('title', 'headerText', {css:'ui dividing header', text:'What is your billing address?', size:'small'});
			dom.body.makeNode('formCont', 'container', {}).makeNode('formCol', 'column', {w:16});
			dom.body.makeNode('btnTopSpace', 'lineBreak', {spaces:1});
			dom.body.makeNode('updateBtn', 'button', {css:'ui green button', text:'<i class="fa fa-check"></i> Update'});
			dom.body.makeNode('btnBottomSpace', 'lineBreak', {});

			dom.body.formCont.formCol.makeNode('form', 'form', formArgs);

			dom.body.updateBtn.notify('click', {
				type:'invoicesRun',
				data:{
					run:function(settings){

						var 	dom = this,
							formInfo = dom.body.formCont.formCol.form.process();

						if(formInfo.completed == false){
							sb.dom.alerts.alert('', 'Please fill out the whole form', 'error');
							return;
						}

						dom.body.updateBtn.loading();

						var address = {
								street:formInfo.fields.info.value,
								object_type:'invoice_system'
							};

						_.each(formInfo.fields, function(fieldObj, fieldName){

							address[fieldName] = fieldObj.value;

						});

						function updateSettingsAddress(settings, address, callback){

							if(settings){

								address.object_id = settings.id;

								if(settings.billing_address){

									address.id = settings.billing_address.id;

									sb.data.db.obj.update('contact_info', address, function(infoObj){

										sb.data.db.obj.update('invoice_system', {id:settings.id, billing_address:infoObj.id}, function(updatedSettings){

											callback(updatedSettings);

										}, 1);

									});

								}else{

									sb.data.db.obj.create('contact_info', address, function(infoObj){

										sb.data.db.obj.update('invoice_system', {id:settings.id, billing_address:infoObj.id}, function(updatedSettings){

											callback(updatedSettings);

										}, 1);

									});

								}

							}else{

								sb.data.db.obj.create('invoice_system', {}, function(newSettingsObj){

									address.object_id = newSettingsObj.id;

									sb.data.db.obj.create('contact_info', address, function(infoObj){

										sb.data.db.obj.update('invoice_system', {id:newSettingsObj.id, billing_address:infoObj.id}, function(updatedSettings){

											callback(updatedSettings);

										}, 1);

									});

								});

							}

						}

						updateSettingsAddress(settings, address, function(updated){

							dom.body.updateBtn.text('<i class="fa fa-check"></i> Updated!');
							dom.body.updateBtn.loading(false);

							setTimeout(function(){

								billingAddressSetup(dom);

							}, 1000);

						});

					}.bind(dom, settings)
				}

			});

			dom.patch();

			/*
			dom.makeNode('title', 'headerText', {text:'What is your billing address?', size:'small'});

			dom.makeNode('break', 'lineBreak', {});

			dom.makeNode('form', 'form', formArgs);

			dom.makeNode('update', 'button', {text:'<i class="fa fa-check"></i> Update', css:'ui green button'}).notify('click', {
				type:'invoicesRun',
				data:{
					run:function(settings){

						var dom = this,
							formInfo = dom.form.process();

						if(formInfo.completed == false){
							sb.dom.alerts.alert('', 'Please fill out the whole form', 'error');
							return;
						}

						dom.update.loading();

						var address = {
								street:formInfo.fields.info.value,
								object_type:'invoice_system'
							};

						_.each(formInfo.fields, function(fieldObj, fieldName){

							address[fieldName] = fieldObj.value;

						});

						function updateSettingsAddress(settings, address, callback){

							if(settings){

								address.object_id = settings.id;

								if(settings.billing_address){

									address.id = settings.billing_address.id;

									sb.data.db.obj.update('contact_info', address, function(infoObj){

										sb.data.db.obj.update('invoice_system', {id:settings.id, billing_address:infoObj.id}, function(updatedSettings){

											callback(updatedSettings);

										}, 1);

									});

								}else{

									sb.data.db.obj.create('contact_info', address, function(infoObj){

										sb.data.db.obj.update('invoice_system', {id:settings.id, billing_address:infoObj.id}, function(updatedSettings){

											callback(updatedSettings);

										}, 1);

									});

								}

							}else{

								sb.data.db.obj.create('invoice_system', {}, function(newSettingsObj){

									address.object_id = newSettingsObj.id;

									sb.data.db.obj.create('contact_info', address, function(infoObj){

										sb.data.db.obj.update('invoice_system', {id:newSettingsObj.id, billing_address:infoObj.id}, function(updatedSettings){

											callback(updatedSettings);

										}, 1);

									});

								});

							}

						}

						updateSettingsAddress(settings, address, function(updated){

							dom.update.text('<i class="fa fa-check"></i> Updated!');
							dom.update.css('pda-btn-green');

							setTimeout(function(){

								billingAddressSetup(dom);

							}, 1000);

						});

					}.bind(dom, settings, )
				}
			}, sb.moduleId);

			dom.patch();
*/

		}, 1);

	}

	function buildInvoice(obj, edit, draw, domCache){

		var dom = this,
			lineItemEditString = '',
			taxEditString = '';

		if(!dom.title){
			dom.makeNode('loader', 'loader', {});
			dom.patch();
		}

		var lineItems = '',
			subtotal = 0,
			taxRate = 0,
			tax = 0,
			total = 0,
			amountPaid = 0,
			totalDue = 0;

		if(obj.tax_rate){
			taxRate = +obj.tax_rate;
		}

		_.each(obj.payments, function(p){

			amountPaid += p.amount;

		});

		var	billedtoCompany = '<i>No client selected</i>',
			billedtoContact = '<i>No Contact selected</i>';

		if( obj.main_contact != null && obj.main_contact !== false){

			billedtoContact = `${obj.main_contact.fname} ${obj.main_contact.lname}`;

			if( obj.main_contact.company != null && obj.main_contact.company !== false){

				billedtoCompany = obj.main_contact.company.name;

			}

		}

		_.each(obj.items, function(item, k){

			var itemSubtotal = item.amount * item.quantity,
				itemTotal = 0,
				itemTaxRate = 0,
				itemTax = 0;

			if(item.tax_rate){
				itemTaxRate = item.tax_rate;
				itemTax = ((item.amount * item.quantity) * (itemTaxRate/100));
			}

			if(obj.main_contact && item.tax_rate){
				if(!obj.main_contact.company.tax_exempt && item.tax_rate){
					itemTaxRate = item.tax_rate;
					itemTax = ((item.amount * item.quantity) * (itemTaxRate/100));
				}
			}

			itemTotal = itemSubtotal + itemTax;

			if(edit){
				lineItemEditString = '<a data-id="'+ k +'" class="remove-line-item"><i class="fa fa-trash-o" style="color:red; margin:0 5px 0px 0px;"></i></a> <a data-id="'+ k +'" class="edit-line-item-'+ obj.id +'"><i class="fa fa-pencil" style="color:orange; margin:0 10px;"></i></a> ';
				//taxEditString = ' <a data-id="'+ k +'" class="change-tax-rate-'+ obj.id +'"><i class="fa fa-pencil" style="color:orange;"></i></a>';
			}

			if(obj.main_contact){
				if(obj.main_contact.company){

					if(obj.main_contact.company.tax_exempt){

						lineItems +=
							'<tr class="bottom-border">  '  +
								'<td width="" class="edit-line-item-'+ k +'">'+ lineItemEditString +''+ item.name +'</td>  '  +
								'<td width="" style="text-align: right;">$'+ (item.amount/100).formatMoney(2) +'</td>  '  +
								'<td width="" style="text-align: right;">'+ item.quantity +'</td>  '  +
								'<td width="" style="text-align: right;">$'+ ((item.amount/100) * item.quantity).formatMoney(2) +'</td>  '  +
								'<td width="" style="text-align: right;" class="change-tax-rate-'+ k +'" ></td>  '  +
								'<td width="" style="text-align: right;" class="lineTotal-'+ k +'">$'+ (itemTotal/100).formatMoney(2) +'</td>  '  +
							'</tr>';

					}else{

						lineItems +=
							'<tr class="bottom-border">  '  +
								'<td width="" class="edit-line-item-'+ k +'">'+ lineItemEditString +''+ item.name +'</td>  '  +
								'<td width="" style="text-align: right;">$'+ (item.amount/100).formatMoney(2) +'</td>  '  +
								'<td width="" style="text-align: right;">'+ item.quantity +'</td>  '  +
								'<td width="" style="text-align: right;">$'+ ((item.amount/100) * item.quantity).formatMoney(2) +'</td>  '  +
								'<td width="" style="text-align: right;" class="change-tax-rate-'+ k +'" >$'+ (itemTax/100).formatMoney(2) +''+ taxEditString +'</td>  '  +
								'<td width="" style="text-align: right;" class="lineTotal-'+ k +'">$'+ (itemTotal/100).formatMoney(2) +'</td>  '  +
							'</tr>';

					}

				}else{

					lineItems +=
						'<tr class="bottom-border">  '  +
							'<td width="" class="edit-line-item-'+ k +'">'+ lineItemEditString +''+ item.name +'</td>  '  +
							'<td width="" style="text-align: right;">$'+ (item.amount/100).formatMoney(2) +'</td>  '  +
							'<td width="" style="text-align: right;">'+ item.quantity +'</td>  '  +
							'<td width="" style="text-align: right;">$'+ ((item.amount/100) * item.quantity).formatMoney(2) +'</td>  '  +
							'<td width="" style="text-align: right;" class="change-tax-rate-'+ k +'" >$'+ (itemTax/100).formatMoney(2) +''+ taxEditString +'</td>  '  +
							'<td width="" style="text-align: right;" class="lineTotal-'+ k +'">$'+ (itemTotal/100).formatMoney(2) +'</td>  '  +
						'</tr>';

				}

			}else{

				lineItems +=
					'<tr class="bottom-border">  '  +
						'<td width="" class="edit-line-item-'+ k +'">'+ lineItemEditString +''+ item.name +'</td>  '  +
						'<td width="" style="text-align: right;">$'+ (item.amount/100).formatMoney(2) +'</td>  '  +
						'<td width="" style="text-align: right;">'+ item.quantity +'</td>  '  +
						'<td width="" style="text-align: right;">$'+ ((item.amount/100) * item.quantity).formatMoney(2) +'</td>  '  +
						'<td width="" style="text-align: right;" class="change-tax-rate-'+ k +'" >$'+ (itemTax/100).formatMoney(2) +''+ taxEditString +'</td>  '  +
						'<td width="" style="text-align: right;" class="lineTotal-'+ k +'">$'+ (itemTotal/100).formatMoney(2) +'</td>  '  +
					'</tr>';

			}



			subtotal += itemTotal;

		});

		total = obj.amount;

		totalDue = obj.balance;

		if(edit){
			taxEditString = ' - <a style="color:blue;" class="tax-rate"><small>change</small></a>';
		}

		sb.data.db.obj.getAll('tax_rates', function(rates){

			sb.data.db.obj.getAll('invoice_system', function(settingsList){

				getInvoiceImageHTML(obj.invoice_type_list, function(image){

					var settings = settingsList[0],
						billingAddress = '<i>No billing address</i>',
						lineItemTaxHeader = 'Tax';

					if(obj.main_contact){
						if(obj.main_contact.company){
							if(obj.main_contact.company.tax_exempt){ lineItemTaxHeader = ''; }
						}
					}

					if(settings){
						if(settings.billing_address){

							billingAddress = settings.billing_address.street +'<br />'+ settings.billing_address.city +', '+ settings.billing_address.state +' '+ settings.billing_address.zip;

						}
					}

					var paymentStubHTML = '<br /><br /><br /><br /><br /><br />';
					if(totalDue > 0){

						paymentStubHTML = '   	<div style="border-bottom: 2px dotted #7e7e7e;"></div>  '  +
			 '   	  '  +
			 '   	<br /><br />  '  +
			 '   	  '  +
			 '   	<table width="100%">  '  +
			 '   		  '  +
			 '   		<tr>  '  +
			 '   			  '  +
			 '   			<td>  '  +
			 '   				  '  +
			 '   				<h3>PAYMENT STUB</h3>  '  +
			 '   				  '  +
			 '   				<br />  '  +
			 '   				  '  +
			 '   				<p>'+ appConfig.systemName +'<br />'+ billingAddress +'</p>  '  +
			 '   				  '  +
			 '   			</td>  '  +
			 '   			  '  +
			 '   			<td>  '  +
			 '   				  '  +
			 '   				<div style="font-weight:bold;">  '  +
			 '   					<p style="padding:10px;">To pay this invoice online, go to: <a href="https://voltz.software">LINK</a></p>'  +
			 '   				</div>  '  +
			 '   				  '  +
			 '   				<table width="100%">  '  +
			 '   					  '  +
			 '   					<tr>  '  +
			 '     '  +
			 '   						<td>Client</td>  '  +
			 '   						<td style="text-align: right;">'+ billedtoCompany +'</td>  '  +
			 '     '  +
			 '   					</tr>  '  +
			 '     '  +
			 '   					<tr class="bottom-border">  '  +
			 '     '  +
			 '   						<td>Invoice #</td>  '  +
			 '   						<td style="text-align: right;">'+ obj.object_uid +'</td>  '  +
			 '     '  +
			 '   					</tr>  '  +
			 '     '  +
			 '   					<tr class="bottom-border">  '  +
			 '     '  +
			 '   						<td>Invoice Date</td>  '  +
			 '   						<td style="text-align: right;">'+ moment(obj.due_date).format('M/D/YYYY') +'</td>  '  +
			 '     '  +
			 '   					</tr>  '  +
			 '   					  '  +
			 '   					<tr class="bottom-border">  '  +
			 '     '  +
			 '   						<td>Balance Due</td>  '  +
			 '   						<td class="totalDue" style="text-align: right;">$'+ (totalDue/100).formatMoney(2) +'</td>  '  +
			 '     '  +
			 '   					</tr>  '  +
			 '   					  '  +
			 '   					<tr class="bottom-border">  '  +
			 '     '  +
			 '   						<td>Amount Enclosed</td>  '  +
			 '   						<td></td>  '  +
			 '     '  +
			 '   					</tr>  '  +
			 '   					  '  +
			 '   				</table>  '  +
			 '   				  '  +
			 '   			</td>  '  +
			 '   			  '  +
			 '   		</tr>  '  +
			 '   		  '  +
			 '   	</table>  '  +
			 '   	  '  +
			 '   	<br /><br />  ';

					}else{

						var paymentLineItemsHTML = '';

						_.each(obj.payments, function(payment){

							paymentLineItemsHTML +=
			 '   					<tr style="border-top:1px dotted gray;">  '  +
			 '     '  +
			 '   						<td>'+ moment(payment.date_created).format('M/D/YYYY h:mm a') +'</td>  '  +
			 '   						<td style="">$'+ (payment.amount/100).formatMoney(2) +'</td>  '  +
			 '     '  +
			 '   					</tr>  ';

						});

						paymentLineItemsHTML +=
			 '   					<tr style="border-top:1px dotted gray;">  '  +
			 '     '  +
			 '   						<td style="font-weight:bold;">TOTAL PAID</td>  '  +
			 '   						<td style="font-weight:bold;">$'+ (amountPaid/100).formatMoney(2) +'</td>  '  +
			 '     '  +
			 '   					</tr>  ';


						paymentStubHTML =
			'<div style="border-bottom: 2px dotted #7e7e7e;"></div>  '  +
			 '   	<table width="100%">  '  +
			 '   		  '  +
			 '   		<tr>  '  +
			 '   			  '  +
			 '   			<td>  '  +
			 '   				  '  +
			 '   				<h3>PAYMENT HISTORY</h3>  '  +
			 '   				  '  +
			 '   				<br />  '  +
			 '   				  '  +  '   				  '  +
			 '   			</td>  '  +
			 '   			  '  +
			 '   			<td>  '  +
			 '   				<table width="100%">  '  +
			 '   					<tr>  '  +
			 '     '  +
			 '   						<td style="font-weight:bold;">Payment Date</td>  '  +
			 '   						<td style="font-weight:bold;">Amount</td>  '  +
			 '     '  +
			 '   					</tr>  '+
			 '   					  '  +
			paymentLineItemsHTML +
			 '   					  '  +
			 '   				</table>  '  +
			 '   				  '  +
			 '   			</td>  '  +
			 '   			  '  +
			 '   		</tr>  '  +
			 '   		  '  +
			 '   	</table>  '  +
			 '   	  '  +
			 '   	<br /><br />  ';

					}

					dom.empty();

					var segmentCSS = 'ui raised basic segment';
					if(edit === true){
						segmentCSS = 'ui raised orange segment';
					}

					dom.makeNode('text', 'div', {
						css:segmentCSS,
						text:'' +
		 '   <div id="invoice-container" style="width: 100%;">  '  +
		 '		<table width="100%">'+
		 '			<tr>'+
		 '				<td width="50%">'+
		 '   				<p>'+ appConfig.systemName +'<br />'+ billingAddress +'</p>  '  +
		 '				</td>'+
		 '				<td width="50%" style="text-align:right;">'+
		 ' 				  	<h2 id="clickableImage" style="text-align:right; max-height:90px;">'+ image +'</h2>  '  +
		 '				</td>'+
		 '			</tr>'+
		 '		</table>'+
		 '   	  '  +
		 '   	<br /><br />'  +
		 '   	  '  +
		 '   	<table width="100%">  '  +
		 '   	  '  +
		 '   		<tr>  '  +
		 '   			  '  +
		 '   			<td width="50%">'  +
		 '   				'  +
		 '   				<table width="100%">  '  +
		 '   					  '  +
		 '   					<tr>  '  +
		 '   						  '  +
		 '   						<td style="font-weight: bold;">Billed to:</td>  '  +
		 '   						  '  +
		 '   					</tr>  '  +
		 '     '  +
		 '   					<tr>  '  +
		 '   						  '  +
		 '   						<td style="font-size: 16px;">'+ billedtoCompany +'</td>  '  +
		 '   						  '  +
		 '   					</tr>  '  +
		 '     '  +
		 '   					<tr>  '  +
		 '   						  '  +
		 '   						<td>'+ billedtoContact +'</td>  '  +
		 '   						  '  +
		 '   					</tr>  '  +
		 '   					  ' +
		 '   				</table>  '  +
		 '	                    '  +
		 '   			</td>  '  +
		 '   			  '  +
		 '   			<td width="50%">  '  +
		 '   				  '  +
		 '   				<table width="100%">  '  +
		 '   					  '  +
		 '   					<tr>  '  +
		 '   						  '  +
		 '   						<td width="50%">Invoice #</td>  '  +
		 '   						<td style="text-align: right;">'+ obj.object_uid +'</td>  '  +
		 '   						  '  +
		 '   					</tr>  '  +
		 '     '  +
		 '   					<tr>  '  +
		 '   						  '  +
		 '   						<td width="50%">Invoice Date</td>  '  +
		 '   						<td style="text-align: right;">'+ moment(obj.due_date).format('M/D/YYYY') +'</td>  '  +
		 '   						  '  +
		 '   					</tr>  '  +
		 '     '  +
		 '   					<tr class="bold-table-row">  '  +
		 '   						  '  +
		 '   						<td width="50%">Balance Due (USD)</td>  '  +
		 '   						<td style="text-align: right;" class="totalDue">$'+ (totalDue/100).formatMoney(2) +'</td>  '  +
		 '   						  '  +
		 '   					</tr>  '  +
		 '   					  ' +
		 '   				</table>  '  +
		 '   				  '  +
		 '   			</td>  '  +
		 '   			  '  +
		 '   		</tr>  '  +
		 '   		  '  +
		 '   	</table>  '  +
		 '   	  '  +
		 '   	  '  +
		 '   	<br /><br />'  +
		 '   	  '  +
		 paymentStubHTML +
		 '   	  '  +
		 '  </div>  '+
		 ''
					}).listeners.push(function(selector){

						$('#clickableImage').click(buildLogoSelector.bind(dom, obj, edit, draw, domCache, image));

						// practice uploadModal comp
						/*
						$('#clickableImage').click(function(){
							sb.notify({
								type:'show-upload-modal',
								data:{
									domObj:dom,
									obj:obj
								}
							})
						});
						*/

					});

					if(draw){

						draw({
							dom:domCache,
							after:function(dom){

								sb.notify({
									type: 'show-note-list-box',
									data: {
										domObj:dom.info.notes,
										objectIds:[obj.id],
										objectId:obj.id,
										collapse:true
									}
								});

							}
						});

					}else{

						dom.patch();

					}

					if(edit){

						if(obj.main_contact){
							if(!obj.main_contact.company){
								if(!obj.main_contact.company.tax_exempt){

									$('.change-tax-rate-'+obj.id).click(function(){

										var itemId = $(this).data('id'),
											invoiceTotal = 0;

						 				$('.change-tax-rate-'+itemId).webuiPopover({
										    placement:		'left',//values: auto,top,right,bottom,left,top-right,top-left,bottom-right,bottom-left,auto-top,auto-right,auto-bottom,auto-left,horizontal,vertical
										    trigger:		'click',//values:  click,hover,manual(handle events by your self),sticky(always show after popover is created);
										    animation:		'pop', //pop with animation,values: pop,fade (only take effect in the browser which support css3 transition)
										    width: 200,
										    title:			'Tax Rates',//the popover title, if title is set to empty string,title bar will auto hide
										    content:		function(){

											    var optionText = '<option value="0">No Tax</option>';

											    _.each(rates, function(r){

												    if(r.rate == obj.items[itemId].tax_rate){

														optionText += '<option selected="selected" value="'+ r.id +'">'+ r.name +'</option>';

												    }else{

													    optionText += '<option value="'+ r.id +'">'+ r.name +'</option>';

												    }

											    });

											    var html = '<form class="form"><select id="" class="tax-rate-form pda-form pda-form-select pda-form-fullWidth">'+ optionText +'</select></form>';

												return html;

											},//content of the popover,content can be function
										    closeable:		true,//display close button or not
										    type:			'html',//content type, values:'html','iframe','async'
										    dismissible:	true, // if popover can be dismissed by  outside click or escape key
											onShow: function(){

												$('.tax-rate-form').on('change', function(){

													if( +$(this).val() > 0){

														obj.items[itemId].tax_rate = _.where(rates, {id: +$(this).val()})[0].rate;

													}else{

														obj.items[itemId].tax_rate = '0.00';

													}

													var newAmount = 0,
														totalPaid = 0;

													_.each(obj.items, function(i){

														if(i.tax_rate){

															newAmount += (i.amount + (i.amount * (i.tax_rate/100)));

														}else{

															newAmount += i.amount;

														}

													});

													_.each(obj.payments, function(p){

														totalPaid += p.amount;

													});

													obj.amount = newAmount;
													obj.paid = totalPaid;
													obj.balance = newAmount - totalPaid;

													sb.data.db.obj.update('invoices', obj, function(updated){

														var noteObj = {
																type_id: updated.id,
																type: 'invoices',
																note: 'Tax rate changed for item '+ obj.items[itemId].name +'.',
																record_type:'log',
																author: sb.data.cookie.get('uid'),
																notifyUsers: []
															};

														sb.data.db.obj.create('notes', noteObj, function(newNote){

															WebuiPopovers.hideAll();

															buildInvoice.call(dom, updated, true);

														});

													}, 2);

												});

											}

										});

									});

								}
							}
						}

						$('.edit-line-item-'+obj.id).click(function(e){

							var itemId = $(this).data('id');

							$('.edit-line-item-'+itemId).webuiPopover({
								placement:		'bottom',//values: auto,top,right,bottom,left,top-right,top-left,bottom-right,bottom-left,auto-top,auto-right,auto-bottom,auto-left,horizontal,vertical
							    trigger:		'click',//values:  click,hover,manual(handle events by your self),sticky(always show after popover is created);
							    animation:		'pop', //pop with animation,values: pop,fade (only take effect in the browser which support css3 transition)
							    width: 375,
								title:'Enter a new name',
								content:function(){
									return '';
								},
								closeable:		true,//display close button or not
							    type:			'html',//content type, values:'html','iframe','async'
							    dismissible:	true, // if popover can be dismissed by  outside click or escape key
							    onShow:function(){

								    var popDom = sb.dom.make('.webui-popover-content');

									popDom.makeNode('form', 'form', {
										itemName:{
											name:'itemName',
											label:'Item Name',
											type:'text',
											value:obj.items[itemId].name
										}
									});

									popDom.makeNode('button', 'button', {text:'Save', css:'pda-btn-green pda-btn-x-small'});

									popDom.button.notify('click', {
										type:'invoicesRun',
										data:{
											run:function(obj, itemId){

												var popDom = this,
													newName = this.form.process().fields.itemName.value;

												popDom.button.loading();

												obj.items[itemId].name = newName;

												sb.data.db.obj.update('invoices', {id:obj.id, items:obj.items}, function(updatedInvoice){

													WebuiPopovers.hideAll();

													buildInvoice.call(dom, updatedInvoice, true);

												}, 2);

											}.bind(popDom, obj, itemId)
										}
									}, sb.moduleId);

									popDom.build();

							    }
							});

						});

						$('.remove-line-item').click(function(){

							var event = this;

							sb.dom.alerts.ask({
								title: '',
								text: 'Are you sure you want to remove this line item?'
							}, function(resp){

								if(resp){

									swal.disableButtons();

									var itemId = $(event).data('id'),
										invoiceTotal = 0,
										itemToRemove = obj.items[itemId];

									obj.items = _.reject(obj.items, function(item, k){

										return k == itemId;

									});

									_.each(obj.items, function(item){

										invoiceTotal += (item.amount * item.quantity);

										// add tax
										if(item.tax_rate){

											invoiceTotal += (item.amount * item.quantity) * (item.tax_rate / 100);

										}

									});

									if(invoiceTotal == 0){
										invoiceTotal = '0';
									}

									obj.amount = Math.round(+invoiceTotal);
									obj.balance = obj.amount - obj.paid;

									sb.data.db.obj.update('invoices', obj, function(updatedObj){

										var noteObj = {
												type_id: updatedObj.id,
												type: 'invoices',
												note: itemToRemove.name +' removed for $'+ (itemToRemove.amount/100).formatMoney(2) +'.',
												record_type:'log',
												author: sb.data.cookie.get('uid'),
												notifyUsers: []
											};

										sb.data.db.obj.create('notes', noteObj, function(newNote){

											swal.close();

											buildInvoice.call(dom, updatedObj, true);

										});

									}, 4);

								}

							});

						});

					}

				}, true);

			}, 1);

		});

	}

	function buildLogoSelector(obj, edit, draw, domCache, image, addToInvoice, callback){

		var	companyLogos = {
				invoiceLogos:[],
				all:[]
			};

		var mainDom = this;

		function processLogoForm(form, button, callback){

			var formData = form.process().fields;

			if(!formData.company_logo){

				sb.dom.alerts.alert('Please select a file to upload', '', 'error');
				return false;

			}

			button.loading(true);

			//return;

			var objToUpdate = 'instances';
			var idToUpdate = appConfig.id;
			if(addToInvoice){
				objToUpdate = 'invoice_type';
				idToUpdate = obj.id;
			}

			if(objToUpdate == 'invoice_type'){

				sb.data.db.obj.update(objToUpdate, {id:idToUpdate, company_logo:formData.company_logo.value}, function(response){

					if(!addToInvoice){

						sb.data.db.obj.getWhere('users', {id:userObj.id, childObjs:3}, function(updatedUser){

							if(response){

								if(this.hasOwnProperty('modal')) {
									this.modal.hide();
								} else {
									this.hide();
								}

							}else{

								sb.dom.alerts.alert('Error', 'Whoops, Something went wrong. Please try again.', 'error');

							}

						}.bind(this));

					}else{

						if(this.hasOwnProperty('modal')) {
							this.modal.hide();
						} else {
							this.hide();
						}

						sb.dom.alerts.alert('Image saved!', '', 'success');

						callback(true);

					}


				}.bind(this));

			}else{

				if(this.hasOwnProperty('modal')) {
					this.modal.hide();
				} else {
					this.hide();
				}

				sb.dom.alerts.alert('Image saved!', '', 'success');

				callback(true);

			}

		}

		function buildLogoList(logosArray){

			_.each(logosArray, function(logo){

				var logoListCont = this.modal.wrapper.body.col.item.content.logoCont.logoCol.makeNode('card-'+ logo.id, 'div', {style:'margin:0 0 1em 1em!important;'});

				logoListCont.makeNode('card', 'div', {css:'ui card', style:'width:7.5em!important;'});
				logoListCont.card.makeNode('image', 'div', {css:'ui blurring dimmable image'});
				logoListCont.card.image.makeNode('dimmer', 'div', {css:'ui dimmer', listener:{
						type:'dimmer',
						on:'hover'
					}
				});


				logoListCont.card.image.makeNode('logo', 'image', {url:sb.data.files.getURL(logo.company_logo), style:'height:7.5em!important;width:7.5em!important;object-fit:cover!important;'});
				logoListCont.card.image.dimmer.makeNode('content', 'div', {css:'content'});
				logoListCont.card.image.dimmer.content.makeNode('center', 'div', {css:'center'});

				logoListCont.card.image.dimmer.content.center.makeNode('changeImage', 'button', {css:'circular ui inverted blue icon button', text:'<i class="check icon"></i>'}).notify('click', {
					type:'invoicesRun',
					data:{
						run:function(){

							obj.company_logo.id = logo.company_logo.id;

							sb.data.db.obj.update('invoice_type', obj, function(updated){

								mainDom.modal.hide();

								//buildInvoice.call(mainDom, updated, edit, draw, domCache);

								invoiceTypesSetup(mainDom);

							}, 1);

						}.bind(this, obj, edit, draw, domCache)
					}
				}, sb.moduleId);

				logoListCont.card.makeNode('download', 'button', {text:'<span style="font-size:0.8em!important;">'+ logo.company_logo.file_name +'</span>', css:'compact ui bottom attached gray button'}).notify('click', {
					type:'invoicesRun',
					data:{
						run:function(){

							sb.data.files.open(logo.company_logo);

						}
					}
				}, sb.moduleId);

			}.bind(this));

			this.patch();

		}

		function start(obj, logosArray, callback){

			// obj --> invoice_type

			var formArgs = {
					logo:{
						name:'company_logo',
						type:'file-upload',
						label:'Upload New Invoice Logo:'
					}
				},
				currentLogo = _.where(logosArray, {is_primary:'yes'})[0],
				logoString = '';

			if(obj.hasOwnProperty('company_logo')){

				if(obj.company_logo != null && obj.company_logo != undefined && obj.company_logo.hasOwnProperty('id')){

					currentLogo = obj.company_logo;

				}

			}

			if(currentLogo){

				logoString = sb.data.files.getURL(currentLogo);

			}

			this.makeNode('modal', 'modal', {css:'ui modal', size:'medium'});

			this.modal.makeNode('wrapper', 'div', {css:'ui stackable grid container'});
			this.modal.makeNode('contentSpacer', 'lineBreak', {spaces:1});

			this.modal.wrapper.makeNode('header', 'div', {css:'sixteen wide column'});
			this.modal.wrapper.header.makeNode('btnGroup', 'buttonGroup', {css:'small ui right floated buttons'});
			this.modal.wrapper.header.makeNode('title', 'headerText', {css:'ui dividing header', text:'<i class="fa fa-upload"></i> Change Invoice Logo'});

			this.modal.wrapper.makeNode('body', 'div', {css:'sixteen wide column'});
			this.modal.wrapper.body.makeNode('col', 'div', {css:'ui items'});
			this.modal.wrapper.body.col.makeNode('item', 'div', {css:'item'});
			this.modal.wrapper.body.col.item.makeNode('image', 'div', {css:'ui medium bordered rounded image'});
			this.modal.wrapper.body.col.item.makeNode('content', 'div', {css:'ui stackable grid container content'});

			this.modal.wrapper.body.col.item.image.makeNode('profileImage', 'image', {url:logoString});

			if(currentLogo){

				this.modal.wrapper.body.col.item.image.makeNode('header', 'div', {css:'ui small header', text:'<i class="fa fa-file"></i> '+ currentLogo.file_name, style:'margin:0.4em 0.4em 0!important;'});
				this.modal.wrapper.body.col.item.image.makeNode('meta', 'div', {css:'meta', text: '<small>Logo Type: "'+ currentLogo.oid_type +'"</small>', style:'margin:0.2em 0.4em !important;'});

			}

			this.modal.wrapper.body.col.item.content.makeNode('logoCont', 'div', {css:'ui stackable grid container'});
			this.modal.wrapper.body.col.item.content.logoCont.makeNode('logoCol', 'div', {style:'display:flex!important;flex-direction:row!important;flex-wrap:wrap!important;justify-content:flex-end!important;'});

			// Upload New Image Form

			this.modal.wrapper.body.col.item.content.makeNode('formCont', 'container', {css:'ui stackable grid'});
			this.modal.wrapper.body.col.item.content.formCont.makeNode('col', 'div', {css:'sixteen wide column'});
			this.modal.wrapper.body.col.item.content.formCont.col.makeNode('form', 'form', formArgs);

			this.modal.wrapper.header.btnGroup.makeNode('back', 'button', {css:'ui red button', text:'<i class="fa fa-arrow-left"></i> Back'}).notify('click', {
				type:'invoicesRun',
				data:{
					run:function(){

						this.hide();

					}.bind(this.modal)
				}
			});
			this.modal.wrapper.header.btnGroup.makeNode('save', 'button', {css:'ui blue button', text:'<i class="fa fa-check"></i> Save'}).notify('click', {
				type:'invoicesRun',
				data:{
					run:function(callback){

						processLogoForm.call(this, this.modal.wrapper.body.col.item.content.formCont.col.form, this.modal.wrapper.header.btnGroup.save, callback);

					}.bind(this, callback)
				}
			}, sb.moduleId);

			buildLogoList.call(this, logosArray);

			this.modal.patch();
			this.modal.show();

		}

		sb.data.db.obj.getAll('company_logo', function(logos){

			start.call(this, obj, logos, callback);

		}.bind(this), 2);

	}

	function buildUI(domObj, objectType, objectId, contactId){

		ui = sb.dom.make(domObj.selector);

		balanceUI = ui.makeNode('cont', 'div', {});
		balanceUI.state = balanceState;

		tableUI = ui.makeNode('table', 'div', {});
		tableUI.state = tableState;

		ui.build();

	}

	function buildContactUI(domObj){

		//comps.table = sb.createComponent('crud-table');
		//comps.payment = sb.createComponent('paymentMethods');

		ui = sb.dom.make(domObj.selector);

		contactUI = ui.makeNode('contactUI', 'column', {width:12});
		contactUI.state = contactState;

		ui.build();

	}

	function chartOfAccountSettings(dom){

		var companies = [];

		function addAccount(company, account){

			var dom = this,
				formSetup = {
					name:{
						name:'name',
						label:'Account Name',
						type:'string'
					},
					account_id:{
						name:'account_id',
						label:'Account ID',
						type:'string'
					},
					note:{
						name:'note',
						label:'Note',
						type:'string'
					},
					chart_of_accounts_company:{
						type:'hidden',
						name:'chart_of_accounts_company',
						value:company.id
					}
				};

			if(account){

				formSetup.name.value = account.name;
				formSetup.account_id.value = account.account_id;
				formSetup.note.value = account.note;

			}

			dom.empty();

			dom.makeNode('title', 'headerText', {text:'Create a new account for '+ company.name, size:'small'});

			dom.makeNode('formBreak', 'lineBreak', {});

			dom.makeNode('form', 'form', formSetup);

			dom.makeNode('btns', 'buttonGroup', {css:'pull-left'});

			dom.btns.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save', css:'pda-btn-green'}).notify('click', {
				type:'invoicesRun',
				data:{
					run:saveAccount.bind(dom, company, account)
				}
			}, sb.moduleId);

			dom.btns.makeNode('cancel', 'button', {text:'<i class="fa fa-times"></i> Cancel', css:'pda-btnOutline-red'}).notify('click', {
				type:'invoicesRun',
				data:{
					run:function(){

						displayAccounts.call(this, company);

					}.bind(dom)
				}
			}, sb.moduleId);

			dom.patch();

		}

		function addCompany(){

			var dom = this;

			dom.empty();

			dom.makeNode('title', 'headerText', {text:'Create a new company', size:'small'});

			dom.makeNode('form', 'form', {
				name:{
					name:'name',
					label:'Company Name',
					type:'string'
				}
			});

			dom.makeNode('btns', 'buttonGroup', {css:'pull-left'});

			dom.btns.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save', css:'pda-btn-green'}).notify('click', {
				type:'invoicesRun',
				data:{
					run:saveCompany.bind(dom)
				}
			}, sb.moduleId);

			dom.btns.makeNode('cancel', 'button', {text:'<i class="fa fa-times"></i> Cancel', css:'pda-btnOutline-red'}).notify('click', {
				type:'invoicesRun',
				data:{
					run:function(){

						chartOfAccountSettings(this);

					}.bind(dom)
				}
			}, sb.moduleId);

			dom.patch();

		}

		function deleteAccount(account, company){

			var dom = this;

			sb.dom.alerts.ask({
				title: 'Are you sure?',
				text: ''
			}, function(resp){

				if(resp){

					swal.disableButtons();

					sb.data.db.obj.erase('chart_of_accounts', account.id, function(done){

						swal.close();

						displayAccounts.call(dom, company);

					});

				}

			});

		}

		function deleteCompany(company){

			function deleteObjs(objectType, objs, callback, count){

				if(!count){
					count = 0;
				}

				if(objs[count]){

					sb.data.db.obj.erase(objectType, objs[count].id, function(deleted){

						count++;

						deleteObjs(objectType, objs, callback, count);

					});

				}else{

					callback(true);

				}

			}

			var dom = this;

			sb.dom.alerts.ask({
				title: 'Are you sure?',
				text: ''
			}, function(resp){

				if(resp){

					swal.disableButtons();

					sb.data.db.obj.getWhere('chart_of_accounts', {chart_of_accounts_company:company.id}, function(accounts){

						deleteObjs('chart_of_accounts', accounts, function(done){

							sb.data.db.obj.erase('chart_of_accounts_companies', company.id, function(done){

								swal.close();

								chartOfAccountSettings(dom);

							});

						});

					});

				}

			});

		}

		function displayAccounts(company){

			var dom = this;

			dom.empty();

			dom.makeNode('loader', 'loader', {});

			dom.patch();

			sb.data.db.obj.getWhere('chart_of_accounts', {chart_of_accounts_company:company.id}, function(accounts){

				dom.makeNode('title', 'headerText', {text:'Accounts for '+ company.name, size:'x-small'});

				dom.makeNode('addAccount', 'button', {text:'<i class="fa fa-plus"></i> add an account', css:'pda-transparent pda-color-green pda-btn-x-small pull-right'}).notify('click', {
					type:'invoicesRun',
					data:{
						run:addAccount.bind(dom, company)
					}
				}, sb.moduleId);

				dom.makeNode(
					'table',
					'table',
					{
						css: 'table-hover',
						columns: {
							name: 'Name',
							account_id:'Account ID',
							note:'Note',
							btns: ''
						}
					}
				);

				_.each(accounts, function(account){

					dom.table.makeRow(
						'account-'+account.id,
						[account.name, account.account_id, account.note, '']
					);

					dom.table.body['account-'+account.id].btns.makeNode('btns', 'buttonGroup', {});

					dom.table.body['account-'+account.id].btns.btns.makeNode('edit', 'button', {text:'<i class="fa fa-pencil"></i>', css:'pda-btn-x-small pda-btn-orange'}).notify('click', {
						type:'invoicesRun',
						data:{
							run:addAccount.bind(dom, company, account)
						}
					}, sb.moduleId);

					dom.table.body['account-'+account.id].btns.btns.makeNode('delete', 'button', {text:'<i class="fa fa-trash-o"></i>', css:'pda-btn-x-small pda-btn-red'}).notify('click', {
						type:'invoicesRun',
						data:{
							run:deleteAccount.bind(dom, account, company)
						}
					}, sb.moduleId);

				});

				delete dom.loader;

				dom.patch();

			});

		}

		function saveAccount(company, account){

			var dom = this,
				createUpdate = 'create';

			var formInfo = this.form.process();

			if(formInfo.completed == false){
				sb.dom.alerts.alert('Error', 'Please fill out the entre form.', 'error');
				return;
			}

			dom.btns.save.loading();

			var newAccountObj = {};

			_.each(formInfo.fields, function(field, name){

				newAccountObj[name] = field.value;

			});

			if(account){

				newAccountObj.id = account.id;
				createUpdate = 'update';

			}

			sb.data.db.obj[createUpdate]('chart_of_accounts', newAccountObj, function(done){

				displayAccounts.call(dom, company);

			});

		}

		function saveCompany(){

			var dom = this;

			var formInfo = this.form.process();

			if(formInfo.completed == false){
				sb.dom.alerts.alert('Error', 'Please fill out the entre form.', 'error');
				return;
			}

			dom.btns.save.loading();

			var newCompanyObj = {
					name:formInfo.fields.name.value
				};

			sb.data.db.obj.create('chart_of_accounts_companies', newCompanyObj, function(done){

				chartOfAccountSettings(dom);

			});

		}

		dom.empty();

		dom.makeNode('title', 'headerText', {text:'Chart of accounts', size:'small'});

		dom.makeNode('loader', 'loader', {});

		dom.patch();

		sb.data.db.obj.getAll('chart_of_accounts_companies', function(coaComps){

			companies = coaComps;

			dom.makeNode('formBreak', 'lineBreak', {});

			dom.makeNode('compForm', 'form', {
				coaComps:{
					name:'coaComps',
					label:'Chart of accounts companies',
					type:'select',
					options:_.map(coaComps, function(o){

						return {
							name:o.name,
							value:o.id
						};

					}),
					change:function(form, selected){

						displayAccounts.call(dom.list, _.findWhere(companies, {id:+selected}));

					}
				}
			});

			dom.makeNode('btns', 'buttonGroup', {css:'pull-right'});

			dom.btns.makeNode('addCompany', 'button', {text:'<i class="fa fa-plus"></i> add a company', css:'pda-transparent pda-color-green pda-btn-x-small'}).notify('click', {
				type:'invoicesRun',
				data:{
					run:addCompany.bind(dom)
				}
			}, sb.moduleId);

			dom.btns.makeNode('deleteCompany', 'button', {text:'<i class="fa fa-trash-o"></i> delete the company', css:'pda-transparent pda-color-red pda-btn-x-small'}).notify('click', {
				type:'invoicesRun',
				data:{
					run:deleteCompany.bind(dom, companies[0])
				}
			}, sb.moduleId);

			delete dom.loader;

			dom.makeNode('listBreak', 'lineBreak', {});

			dom.makeNode('list', 'container', {css:'pda-container'});

			dom.patch();

			displayAccounts.call(dom.list, coaComps[0], function(done){



			});

		});

	}

	function chooseInvoiceTemplate(domObj, objectType, doneCallback, doneCallback2){

		if(domObj.hasOwnProperty('amount')){
			var dom = objectType;
		}else{
			var dom = domObj;
		}

		var objectId = this.objectId,
			contactId = this.contactId,
			objectType = this.objectType,
			price = this.price,
			dueDate = this.dueDate
			onUpdate = this.onUpdate;

		function createInvoices(obj){

			function deleteInvoices(currentInvoices, callback, count){

				function deleteInvoice(id, callback){

					sb.data.db.obj.erase('invoices', id, function(done){

						callback(true);

					});

				}

				if(currentInvoices[count]){

					deleteInvoice(currentInvoices[count].id, function(done){

						count++;

						deleteInvoices(currentInvoices, callback, count);

					});

				}else{

					callback(true);

				}

			}

			sb.dom.alerts.ask({
				title: 'Are you sure?',
				text: 'This will delete any invoices currently associated with this project.'
			}, function(resp){

				if(resp){

					swal.disableButtons();

					sb.data.db.obj.getWhere('invoices', {related_object:objectId}, function(currentInvoices){

						deleteInvoices(currentInvoices, function(done){

							var invoices = [],
								pricing = Object.assign({}, price),
								hashCode = function(s){
									return s.split("").reduce(function(a,b){a=((a<<5)-a)+b.charCodeAt(0);return a&a},0);
								},
								i = 100,
								dom = this;

							_.each(_.sortBy(obj.templates, 'payment_type'), function(temp){

								var idHash = CryptoJS.MD5( (objectId+i).toString() ).toString(CryptoJS.enc.Hex);

								i++;

								var invoiceObj = {
										active:'Yes',
										amount:0,
										balance:0,
										due_date: moment(),
										invoice_type_list:0,
										name:temp.name,
										template:temp,
										type:objectType,
										type_id:objectId,
										related_object:objectId,
										main_contact:contactId,
										id_hash:idHash,
										items:[],
										locked: 'not-locked'
									},
									catTotals = 0;

								_.each(temp.inventory_billable_categories, function(cat){

									if(pricing[cat.id.toString()]){
										catTotals += pricing[cat.id.toString()];
									}

								});

								if(temp.invoice_type){
									invoiceObj.name = temp.invoice_type.invoice_type;
									invoiceObj.type = temp.invoice_type.invoice_type;
									invoiceObj.invoice_type_list = temp.invoice_type.id;
								}

								// set amount and balance
								switch(temp.payment_type){

									case 'flatRate':

										invoiceObj.amount = +temp.flat_rate;
										invoiceObj.balance = +temp.flat_rate;

										pricing[temp.inventory_billable_categories[0].id.toString()] = pricing[temp.inventory_billable_categories[0].id.toString()] - +temp.flat_rate;

										break;

									case 'percentOfTotal':

										invoiceObj.amount = catTotals * (+temp.percent_of_total / 100);
										invoiceObj.balance = catTotals * (+temp.percent_of_total / 100);

										_.each(temp.inventory_billable_categories, function(cat){

											pricing[cat.id.toString()] = pricing[cat.id.toString()] - (pricing[cat.id.toString()] * (+temp.percent_of_total / 100));

										});

										break;

									case 'remainingBalance':

										var curTotal = 0,
											catInvoices = [];

										if(catTotals % 1 !== 0){
											catTotals = Math.ceil(catTotals);
										}

										invoiceObj.amount = catTotals;
										invoiceObj.balance = catTotals;

										_.each(temp.inventory_billable_categories, function(cat){

											pricing[cat.id.toString()] = pricing[cat.id.toString()] - catTotals;

										});

										break;
								}

								invoiceObj.items.push({
									name:'Invoice amount',
									amount:invoiceObj.amount,
									quantity:1
								});

								// set due date
								switch(temp.before_after){

									case 'after':

										invoiceObj.due_date = moment(dueDate).add(temp.due_date, 'day').format();

										break;

									case 'before':

										invoiceObj.due_date = moment(dueDate).subtract(temp.due_date, 'day').format();

										break;

								}

								invoices.push(invoiceObj);

							});

							sb.data.db.obj.create('invoices', invoices, function(newInvoices){

								setTimeout(function(){

									if(doneCallback2){
										if(_.isFunction(doneCallback2)){
											doneCallback2(true);
										}
									}

									if(onUpdate){

										onUpdate(function(){

											swal.close();

											if(tableUI.state){
												tableUI.state.show();
											}

										}, newInvoices, true);

									}else{

										swal.close();

										if(tableUI.state){
											tableUI.state.show();
										}

									}

								}, 0);

							});

						}, 0);

					});

				}

			});

		}

		dom.makeNode('container', 'div', {css:'ui container segment'});
		dom.makeNode('new', 'div', {css:''});

		dom.container.makeNode('titleCont', 'div', {css:'ui clearing basic segment'});
		dom.container.titleCont.makeNode('create', 'div', {css:'ui green button right floated', text:'Create invoice'})
			.notify('click', {
				type:'invoicesRun',
				data:{
					run:function(dom){

						dom.container.titleCont.create.css('ui green button right floated loading');

						var setup = this;

						sb.data.db.obj.getBlueprint('invoices', function(bp){

							dom.new.css('ui orange segment');

							createInvoice.call(setup, bp, dom.new, {}, function(createDom){

								createDom.patch();

								dom.container.titleCont.create.css('ui green button right floated');

							}, function(created){



							});

						}, 1);

					}.bind(this, dom)
				}
			}, sb.moduleId);

		dom.container.titleCont.makeNode('title', 'div', {text:'Choose a payment template or create an invoice', css:'ui medium left floated header'});

		dom.container.makeNode('tableContainer', 'div', {css:'ui grid'})
			.makeNode('cont', 'div', {css:'sixteen wide column'});

		dom.patch();

		sb.data.db.obj.getAll('payment_schedule_template', function(objs){

			dom.container.tableContainer.cont.makeNode('table', 'table', {
				css: 'ui single line striped basic table',
				clearCSS:true,
				columns: {
					name: 'Template Name',
					total_invoices: 'Invoices that will be created',
					btns: ''
				}
			});

			_.each(objs, function(obj){

				var invoiceDetails = '<i>No invoices associated with this template</i>';

				if(obj.templates.length > 0){
					invoiceDetails = '';
				}

				_.each(obj.templates, function(temp){

					var amount = '',
						categories = '';

					var i = 0;
					_.each(temp.menu_item_category, function(cat){

						if(i > 0){
							categories += ', '+cat.menu_item_category;
						}else{
							categories += ' '+cat.menu_item_category;
						}

						i++;

					});

					switch(temp.payment_type){

						case 'flatRate':

							amount = '$'+(temp.flat_rate/100).toFixed(2);

							break;

						case 'percentOfTotal':

							amount = temp.percent_of_total +'%';

							break;

						case 'remainingBalance':

							amount = 'Remaining balance';

							break;

					}

					var invoiceType = '<i>Type not selected</i>';
					if(temp.invoice_type){
						invoiceType = temp.invoice_type.invoice_type;
					}

					invoiceDetails += '<span class="text-bold">'+ invoiceType +':</span> '+ amount +' of'+' '+ categories +' '+' due '+ temp.due_date +' day(s) '+ temp.before_after +' start<br />';

				});

				dom.container.tableContainer.cont.table.makeRow(
					'template-' + obj.id,
					[obj.name, invoiceDetails, '']
				);

				dom.container.tableContainer.cont.table.body['template-' + obj.id].btns.makeNode('select', 'button', {text:'Select and continue <i class="fa fa-arrow-right"></i>', css:'pda-btn-primary'}).notify('click', {
					type:'invoicesRun',
					data:{
						run:createInvoices.bind(dom, obj)
					}
				}, sb.moduleId);

			});

			//delete dom.container.tableContainer.loader;

			dom.container.tableContainer.patch();

		}, 1);

	}

	function contactState(){

		var invoiceList = [];

		function buildList(contactId){

			this.empty();

			this.makeNode('loader', 'loader', {});

			this.patch();

			var dom = this;

			getData(contactId, function(invoices){

				dom.makeNode('btns', 'buttonGroup', {css:'pull-right'});

				dom.btns.makeNode('new', 'button', {text:'<i class="fa fa-plus"></i> Create New Invoice', css:'pda-btn-green'});

				if(invoices.length == 0){

					dom.makeNode('break', 'lineBreak', {spaces:1});

					dom.makeNode('invoiceCont', 'container', {});

					dom.makeNode('text', 'headerText', {text:'No invoices', size:'x-small', css:'text-center'});

				}else{

					dom.makeNode('title', 'headerText', {text:invoices.length +' total invoices', size:'x-small'});

					dom.makeNode('break', 'lineBreak', {});

					dom.makeNode('invoiceCont', 'container', {});

					_.each(invoices, function(i){

						var total = 0,
							balance = 0,
							containerColor = 'pda-panel-blue pda-background-gray';

						_.each(i.items, function(item){

							if(item.tax_rate){

								total += (+item.amount * (+item.tax_rate/100)) + +item.amount;

							}else{

								total += +item.amount;

							}

						});

						balance = total - i.paid;

						balance = Math.trunc(balance);

						if(balance > 0){

							if(moment(i.due_date) <= moment()){

								containerColor = 'pda-panel-red pda-background-red';

							}else{

								containerColor = 'pda-panel-orange pda-background-orange';

							}

						}else{

							containerColor = 'pda-panel-green pda-background-green';

						}

						dom.invoiceCont.makeNode('invoice-'+i.id, 'container', {css:'pda-container pda-Panel '+containerColor});

						dom.invoiceCont['invoice-'+i.id].makeNode('name', 'headerText', {size:'xx-small', text:'<i class="fa fa-eye"></i> '+ i.name +' due on '+ moment(i.due_date).format('M/D/YYYY') +' current balance: $'+(balance/100).formatMoney(2) +' <small>$'+ (total/100).formatMoney(2) +' (total) - $'+ (i.paid/100).formatMoney(2) +' (amount paid)</small>'});

						dom.invoiceCont['invoice-'+i.id].notify('click', {
							type:'invoicesRun',
							data:{
								run:function(invoice){

									singleInvoice.call({}, invoice, this, true);

								}.bind(dom, i)
							}
						}, sb.moduleId);

					});

				}

				dom.btns.new.notify('click', {
					type:'invoicesRun',
					data:{
						run:function(contactId){

							var dom = this;

							dom.btns.new.loading();

							sb.data.db.obj.getBlueprint('invoices', function(bp){

								createInvoice.call({}, bp, dom.invoiceCont, contactId);

								dom.btns.new.loading(false);

							}, 1);

						}.bind(dom, contactId)
					}
				}, sb.moduleId);

				delete dom.loader;

				dom.patch();

			});

		}

		function getData(contactId, callback, ignoreCache){

			if(invoiceList.length == 0 && !ignoreCache){

				sb.data.db.obj.getWhere('invoices', {main_contact:contactId, childObjs:2}, function(data){

					invoiceList = data;

					callback(data);

				});

			}else{

				callback(invoiceList);

			}

		}

		this.state.show = buildList.bind(this);

	}

	function createHTMLString(invoiceObj, event, contact){

		var ret = '',
			memo = '',
			paymentHistory = '';

		_.each(invoiceObj.payments, function(pymt){

			switch(pymt.transaction_details.paymentType){

				case 'bank':

					var typeLine = 'Bank account ',
						totalLine = '$'+ (pymt.amount/100).formatMoney(2);

					break;

				case 'card':

					var typeLine = 'Credit card ',
						totalLine = '$'+ (pymt.amount/100).formatMoney(2) +' <span style="font-size:10px;">($'+ (pymt.amount/100).formatMoney(2) +' + $'+ (+pymt.transaction_details.ccFee/100).formatMoney(2) +' = <span style="font-weight:bold;">$'+ ( (pymt.amount + +pymt.transaction_details.ccFee)/100).formatMoney(2) +'</span>)</span>';

					break;

			}

			paymentHistory += typeLine + 'payment for '+ totalLine +' made on '+ moment(pymt.date_created).format('MM/DD/YYYY h:mm a') +' - Transaction ID: '+ pymt.id +'<br /><br />';

		});

		if(paymentHistory == ''){
			paymentHistory = 'No payments.';
		}

		memo = invoiceObj.memo;

		if(memo != ''){
			memo = '<p style="font-size:22px;font-weight:bold;">Memo</p>'+ memo +'<br />';
		}

		ret =

		 '   <table style="width:100%; font-size:16px;" cellpadding="10">  '  +
 '   	  '  +
 '   	<tr style="padding:10px;">  '  +
 '   		  '  +
 '   		<td>'+
 '				<h1>Pagoda Dev</h1>'+
 '   			  '  +
 '   			Created: '+ moment(invoiceObj.date_created).format('dddd, MMMM Do YYYY') +' '  +
 '   			<br />  '  +
 '   			Due: '+ moment(invoiceObj.due_date).format('dddd, MMMM Do YYYY') +' '  +
 '   			  '  +
 '			</td>  '  +
 '   		  '  +
 '   		<td style="text-align: right;"></td>  '  +
 '   		  '  +
 '   	</tr>  '  +
 '		</table>'+
 ' <br /><br /> '  +
 '   <table style="width:100%; font-size:16px;" cellpadding="10">  '  +
 '   	<tr>  '  +
 '   		  '  +
 '   		<td>  '  +
 '   			'  +
 '   			'+ appConfig.systemName +
 '   			<br />  '  +
 '   			123456 Sunny Road  '  +
 '   			<br />  '  +
 '   			Sunnyville, TX 12345  '  +
 '   			  '  +
 '   		</td>  '  +
 '   		  '  +
 '   		<td style="text-align: right;">  '  +
 '   			  '  +
 '   			'+ event.event_name +
 '   			<br />  '  +
 				+ contact.fname +' '+ contact.lname +'<br />'+
 '   			123456 Sunny Road  '  +
 '   			<br />  '  +
 '   			Sunnyville, TX 12345  '  +
 '   			  '  +
 '   		</td>  '  +
 '   		  '  +
 '   	</tr>  '  +
 '   	  '  +
 '   </table>  '  +
 ' <br /><br />    '  +
 '   <table style="width:100%; font-size:16px;" cellpadding="10">  '  +
 '   		  '  +
 '   	<tr style="background-color: #e0e0e0; font-weight: bold; padding:10px;">  '  +
 '   		  '  +
 '   		<td style="padding: 5px;vertical-align:top;">  '  +
 '   			  '  +
 '   			Invoice Name  '  +
 '   			  '  +
 '   		</td>  '  +
 '   		  '  +
 '   		<td style="text-align: right; padding: 5px;vertical-align:top;">  '  +
 '   			  '  +
 '   			Price  '  +
 '   			  '  +
 '   		</td>  '  +
 '   		  '  +
 '   	</tr>  '  +
'            <tr class="item">' +
'                <td style="padding: 5px;vertical-align: top;border-bottom: 1px solid #eee;">' +
'                    '+ invoiceObj.name +'' +
'                </td>' +
'                ' +
'                <td style="padding: 5px;vertical-align: top;text-align: right;border-bottom: 1px solid #eee;">' +
'                    $'+ (invoiceObj.amount / 100).formatMoney(2) +'' +
'                </td>' +
'            </tr>' +
'            <tr class="total">' +
'                <td style="padding: 5px;vertical-align: top;border-bottom: 1px solid #eee;"></td>' +
'                ' +
'                <td style="padding: 5px;vertical-align: top;text-align: right;border-top: 2px solid #eee;font-weight: normal;">' +
'                   Total Paid: $'+ (invoiceObj.paid / 100).formatMoney(2) +'' +
'                </td>' +
'            </tr>' +
'            <tr class="total">' +
'                <td style="padding: 5px;vertical-align: top;"></td>' +
'                ' +
'                <td style="padding: 5px;vertical-align: top;text-align: right;border-top: 2px solid #eee;font-weight: bold;">' +
'                   Remaining Balance: $'+ (invoiceObj.balance / 100).formatMoney(2) +'' +
'                </td>' +
'            </tr>' +
 '   	  '  +
 '   </table>  '  +
 '     '  +
 '  <p style="font-size:22px;font-weight:bold;">Payment History</p>'+ paymentHistory +
 ''+ memo +'<br /><br />'+
 '<p style="text-align:right; font-size:10px; color:#b7b7b7;">'+ invoiceObj.id_hash +'</p>';

		return ret;

	}

	function createMergedInvoiceHeaderHTML(setup){

		var	billedtoCompany = '<i>No client selected</i>';
		var billedtoContact = '<i>No Contact selected</i>';
		var billingAddress = '';
		var logoString = '';
		var headCount = '';
		var paymentLink = '';
		var balance = 0;
		var totalPaid = 0;
		var payments = [];
		var htmlString = '';
		var summaryString = '';
		var detailsString = '';

		var lineItems = '',
			subtotal = 0,
			taxRate = 0,
			tax = 0,
			total = 0,
			amountPaid = 0,
			totalDue = 0;

		var	billedtoCompany = '<i>No client selected</i>',
			billedtoContact = '';

		if (setup) {

			if (setup.projectObj) {

				if (setup.projectObj.head_count) {
					headCount = 'Head Count: '+ setup.projectObj.head_count;
				}

				if ( setup.projectObj.main_contact != null && setup.projectObj.main_contact !== false) {

					billedtoContact = `${setup.projectObj.main_contact.fname} ${setup.projectObj.main_contact.lname}`;

					if ( setup.projectObj.main_contact.company != null && setup.projectObj.main_contact.company !== false) {

						billedtoCompany = setup.projectObj.main_contact.company.name;

					}

				}

			}

		}

		if(setup.pageObject){
			if( setup.pageObject.main_contact != null && setup.pageObject.main_contact !== false){

				billedtoContact = `${setup.pageObject.main_contact.fname} ${setup.pageObject.main_contact.lname}`;

				if( setup.pageObject.main_contact.company != null && setup.pageObject.main_contact.company !== false){

					billedtoCompany = setup.pageObject.main_contact.company.name;

				}

			}

		}

		if(setup.logo){
			logoString = sb.data.files.getURL(setup.logo.company_logo);
		}

		_.each(setup.invoices, function(item, k){

			_.each(item.payments, function(p){

				amountPaid += p.amount;

			});

			subtotal += item.amount;

		});

		total = subtotal;

		total = Math.round(total);

		totalDue = total - amountPaid;

		if( setup.client != null && setup.client !== false){

			billedtoCompany = setup.client.name;

			if(setup.client.contact_info){

				if(setup.client.contact_info.length > 0){

					_.each(setup.client.contact_info, function(info){

						if(
							!_.isEmpty(info)
							&& !_.isEmpty(info.type)
							&& info.type.data_type == 'address'
						){

							billedtoCompany += '<br />'+ info.street +'<br />'+ info.city +', '+ info.state +' '+ info.zip;

						}

					});

				}

			}

		}

		if(setup.invoiceSystem){
			if(setup.invoiceSystem.billing_address){

				billingAddress = setup.invoiceSystem.billing_address.street +'<br />'+ setup.invoiceSystem.billing_address.city +', '+ setup.invoiceSystem.billing_address.state +' '+ setup.invoiceSystem.billing_address.zip;

			}
		}

		detailsString = '<table width="100%">';

		detailsString +=
			'<tr style="">'+
				'<td style="padding:5px;"><span style="font-size:x-small; color:#a7a7a7;">INVOICE</span></td>'+
				'<td style="padding:5px; text-align:right;"><span style="font-size:x-small; color:#a7a7a7;">DUE DATE</span></td>'+
				'<td style="padding:5px; text-align:right;"><span style="font-size:x-small; color:#a7a7a7;">TOTAL</span></td>'+
				'<td style="padding:5px; text-align:right;"><span style="font-size:x-small; color:#a7a7a7;">PAYMENT RECEIVED</span></td>'+
				'<td style="padding:5px; text-align:right;"><span style="font-size:x-small; color:#a7a7a7;">BALANCE REMAINING</span></td>'+
			'</tr>';


		_.each(_.sortBy(setup.invoices, 'due_date'), function(inv){

			//paymentLink = '<a id="pay-'+ inv.id +'" href="'+ sb.url +'app/invoices#?&i='+ appConfig.instance +'&pid=" target="_blank" style="text-decoration:underline; font-weight:bold; color:blue;">PAY NOW</a> ';

			if(setup.payNow === true){
				//paymentLink = '<div class="ui mini green compact button payButton" data-id="'+ inv.id +'" data-balance="'+ inv.balance +'">Pay Now</div> ';
			}

			if(setup.payNow === 'noButton'){
				paymentLink = '';
			}

			total += inv.amount;
			balance += inv.balance;
			totalPaid += inv.paid;

			if(inv.balance <= 0){
				paymentLink = '';
			}

			var invoiceName = getInvoiceName(inv);

			payments = payments.concat(inv.payments);

			detailsString +=
				'<tr style="">'+
					'<td style="padding:5px;">'+ paymentLink +''+ invoiceName +'</td>'+
					'<td style="padding:5px; text-align:right;">'+ moment(inv.due_date).local().format('M/D/YYYY') +'</td>'+
					'<td style="padding:5px; text-align:right;">$'+ (inv.amount/100).formatMoney() +'</td>'+
					'<td style="padding:5px; text-align:right;">$'+ (inv.paid/100).formatMoney() +'</td>'+
					'<td style="padding:5px; text-align:right;">$'+ (inv.balance/100).formatMoney() +'</td>'+
				'</tr>';

		});

		detailsString +=
			'<tr style="border-top:1px solid grey;">'+
				'<td style="padding:5px;"><b>Total Amount</b></td>'+
				'<td style="padding:5px; text-align:right;"></td>'+
				'<td style="padding:5px; text-align:right;"><b>$'+ (subtotal/100).formatMoney() +'</b></td>'+
				'<td style="padding:5px; text-align:right;"></td>'+
				'<td style="padding:5px; text-align:right;"></td>'+
			'</tr>';
		detailsString +=
			'<tr style="border-top:1px solid grey;">'+
				'<td style="padding:5px;"><b>Total Paid</b></td>'+
				'<td style="padding:5px; text-align:right;"></td>'+
				'<td style="padding:5px; text-align:right;"></td>'+
				'<td style="padding:5px; text-align:right;"><b>$'+ (totalPaid/100).formatMoney() +'</b></td>'+
				'<td style="padding:5px; text-align:right;"></td>'+
			'</tr>';
		detailsString +=
			'<tr style="border-top:1px solid grey;">'+
				'<td style="padding:5px;"><b>Account Balance</b></td>'+
				'<td style="padding:5px; text-align:right;"></td>'+
				'<td style="padding:5px; text-align:right;"></td>'+
				'<td style="padding:5px; text-align:right;"></td>'+
				'<td style="padding:5px; text-align:right;"><b>$'+ (balance/100).formatMoney() +'</b></td>'+
			'</tr>';

		detailsString += '</table>';

		summaryString = '<table width="100%">';
		summaryString +=
			'<tr style="border-bottom:1px solid grey;">'+
				'<td style="padding:5px;">Invoiced</td>'+
				'<td style="padding:5px; text-align:right;">$'+ (subtotal/100).formatMoney() +'</td>'+
			'</tr>';
		summaryString +=
			'<tr style="border-bottom:1px solid grey;">'+
				'<td style="padding:5px;">Payments</td>'+
				'<td style="padding:5px; text-align:right;">$'+ (totalPaid/100).formatMoney() +'</td>'+
			'</tr>';
		summaryString +=
			'<tr style="">'+
				'<td style="padding:5px;"><b>Account Balance</b></td>'+
				'<td style="padding:5px; text-align:right;"><b>$'+ (balance/100).formatMoney() +'</b></td>'+
			'</tr>';

		summaryString += '</table>';


		htmlString += '<div id="bentoDocumentEditorPreviewer" style="max-width:800px; display:block; margin:0 auto;">';

		//htmlString += '<div style="float:right;" class="enter-payment-button ui green button">Manage Payments</div>';

		//htmlString += '<div style="float:right;" class="ui button pdf-statement-download">Download PDF</div><br /><br /><br />';

		htmlString += '' +
		 '   <div id="invoice-container" class="ui basic segment" style="width: 100%;">  '  +
		 '		<h1>Account Statement</h1>' +
		 '		<p class="text-muted">For '+ moment(setup.startRange).format('l') +' - '+ moment(setup.endRange).format('l') +'</p>' +
		 '		<h3><b>'+ billedtoCompany +'</b></h3>'+
		 '		<p style="">'+ billedtoContact +'</p>'+
		 '		<p style="text-align:right;">'+ appConfig.systemName +'</p>'+
		 '		<p style="text-align:right;">'+ billingAddress +'</p>'+
		 '		<p><b>SUMMARY</b></p>'+
		 '		<hr>'+
		 summaryString +
		 '		<br /><br /><br />'+
		 '		<p><b>DETAILS</b></p>'+
		 '		<hr>'+
		 detailsString +
		 '</div>';

		return htmlString;

	}

	function createInvoice(bp, dom, state, draw, created){

		var objectType = this.objectType,
			contactId = this.contactId,
			objectId = this.objectId,
			clientId = state.clientId;

		var  formObj = {
/*
				invoice_type_list:{
					name:'invoice_type_list',
					label:'Invoice Type',
					type:'select',
					options:_.map(bp.invoice_type_list.options, function(v,k){
						return {
								name:v,
								value:k
							};
					}, [])
				},
*/
				name:{
					name:'name',
					label:'Invoice Name',
					type:'text',
					value:'Next Payment'
				},
				due_date:{
					name:'due_date',
					label:'Due Date',
					type:'date',
					dateFormat:'MM/DD/YYYY',
					value:moment().add(30, 'days')
				},
				amount:{
					name:'amount',
					label:'Amount',
					type:'usd',
					value:0
				}

			};

		function saveForm(created){

			var formInfo = this.wrapper.head.cont.form.process(),
				dom = this;

			if(formInfo.completed == false){
				sb.dom.alerts.alert('Error', 'Please fill out the entire form', 'error');
				return;
			}

			dom.wrapper.head.btns.btnGroup.save.loading();

			var newInvoice = {
					active:'Yes',
					related_object:objectId,
					main_contact:contactId,
					main_client:clientId,
					locked:'locked'
				};

			if(state.project.owner){
				newInvoice.owner = state.project.owner.id;
			}

			_.each(formInfo.fields, function(f,k){

				newInvoice[k] = f.value;

			});

			newInvoice.balance = newInvoice.amount;

			sb.data.db.obj.create('invoices', newInvoice, function(newObj){

				if(created){
					created(newObj);
				}else{

					sb.notify({
						type:'app-remove-main-navigation-item',
						data:{
							itemId:'self',
							viewId:'createInvoice'
						}
					});

					sb.notify({
						type:'app-navigate-to',
						data:{
							itemId:'workorders',
							viewId:'single-'+newObj.related_object.id
						}
					});

					domCache.dom.patch();

					domCache.after(domCache.dom);

				}

/*
				addLineItems.call(dom, newObj, function(domCache){

					if(newObj){

						if(created){
							created(newObj);
						}else{

							sb.notify({
								type:'app-remove-main-navigation-item',
								data:{
									itemId:'self',
									viewId:'createInvoice'
								}
							});

							sb.notify({
								type:'app-navigate-to',
								data:{
									itemId:'workorders',
									viewId:'single-'+newObj.related_object.id
								}
							});

							domCache.dom.patch();

							domCache.after(domCache.dom);

						}

					}else{

						dom.wrapper.head.btns.btnGroup.cancel.removeClass('disabled');
						dom.wrapper.head.btns.btnGroup.save.loading(false);
						sb.dom.alerts.alert('Error!', 'Please refresh and try again.', 'error');

					}

				});
*/

			}, 3);

		}

		dom.empty();
		dom.makeNode('wrapper', 'div', {css: 'ui stackable grid'});
		dom.wrapper.makeNode('head', 'div', {css:'row'});
		dom.wrapper.head.makeNode('col1', 'div', {css:'eight wide column'});
		dom.wrapper.head.col1.makeNode('title', 'headerText', {text:'Create an Invoice'});
		dom.wrapper.head.makeNode('cont', 'container', {css: 'sixteen wide column'});
		dom.wrapper.head.cont.makeNode('form', 'form', formObj);
		dom.wrapper.head.makeNode('break', 'div', {css:'sixteen wide column', text:'<br />'});
		dom.wrapper.head.makeNode('btns', 'div', {css: 'sixteen wide column'});

		dom.wrapper.head.btns.makeNode('btnGroup', 'div', {css:'ui buttons'});
/*
		dom.wrapper.head.btns.btnGroup.makeNode('cancel', 'div', {text:'<i class="fa fa-times"></i> Cancel', css:'ui red basic button'}).notify('click', {
			type:'invoicesRun',
			data:{
				run:function(){

					tableUI.state.show();

				}
			}
		}, sb.moduleId);
*/

		dom.wrapper.head.btns.btnGroup.makeNode('save', 'div', {text:'Save', css:'ui green button'}).notify('click', {
			type:'invoicesRun',
			data:{
				run:saveForm.bind(dom, created)
			}
		}, sb.moduleId);

		draw(dom);

	}

	function createPaymentForm(type, invoiceObj){

		var formSetup = {
				paymentType:{
					type:'hidden',
					name:'paymentType',
					value:type
				},
				fname:{
					type:'string',
					name:'fname',
					label:'First Name'
				},
				lname:{
					type:'string',
					name:'lname',
					label:'Last Name'
				},
				section1:{
					type:'section',
					name:'billingAddress',
					label:'Billing Address',
					fields:{
						street:{
							type:'string',
							name:'street',
							label:'Street Address'
						},
						city:{
							type:'string',
							name:'city',
							label:'City'
						},
						state:{
							type:'select',
							name:'state',
							label:'State',
							options:sb.data.stateArray
						},
						zip:{
							type:'string',
							name:'zip',
							label:'Postal Code'
						},
						country:{
							type:'select',
							name:'country',
							label:'Country',
							options:sb.data.countryArray
						}
					}
				},
				section2:{
					type:'section',
					name:'paymentInfo',
					label:'Payment Information',
					fields:{}
				}
			};

		if(_.isArray(invoiceObj)){

			_.each(_.sortBy(invoiceObj, function(o){return moment(o.due_date).unix();}), function(obj){

				formSetup['amountToPay'+obj.id] = {
					type:'usd',
					name: 'i-'+obj.id,
					label:'Amount To Pay <small>('+ obj.name +' invoice balance: $'+ (obj.balance/100).formatMoney(2) +' due '+ moment(obj.due_date).format('M/DD/YYYY') +')</small>',
					value:obj.balance
				};

			});

		}else{

			formSetup.amountToPay = {
				type:'usd',
				name:'i-'+invoiceObj.id,
				label:'Amount To Pay <small>(invoice balance: $'+ (invoiceObj.balance/100).formatMoney(2) +' due '+ moment(invoiceObj.due_date).format('M/DD/YYYY') +')</small>',
				value:invoiceObj.balance
			};

		}

		switch(type){

			case 'bank':

				formSetup.section2.fields.accountType = {
						type:'select',
						name:'accountType',
						label:'Account Type',
						options:[
							{
								name:'CHECKING',
								value:'checking'
							},
							{
								name:'SAVINGS',
								value:'savings'
							}
						]
					};

				formSetup.section2.fields.accountNumber = {
						type:'string',
						name:'accountNumber',
						label:'Account Number'
					};

				formSetup.section2.fields.routingNumber = {
						type:'string',
						name:'routingNumber',
						label:'Routing Number'
					};

				formSetup.section2.fields.bankName = {
						type:'string',
						name:'bankName',
						label:'Bank Name'
					};

				break;

			case 'card':

				formSetup.section2.fields.cardName = {
						type:'string',
						name:'cardName',
						label:'Name on Card'
					};

				formSetup.section2.fields.cardNumber = {
						type:'string',
						name:'cardNumber',
						label:'Card Number'
					};

				formSetup.section2.fields.expireMonth = {
						type:'select',
						name:'expireMonth',
						label:'Expiration Month',
						options:[]
					};

				formSetup.section2.fields.expireYear = {
						type:'select',
						name:'expireYear',
						label:'Expiration Year'
					};

				formSetup.section2.fields.cvv = {
						type:'string',
						name:'cvv',
						label:'CVV Code'
					};

				break;

		}

		return formSetup;

	}

	function duePaymentEmailSettings(dom){

		function createEdit(dom, currentObj){

			var setupObj = {};

			if(currentObj){
				setupObj = currentObj;
			}else{
				setupObj = {
						subject:'Hi {{invoice.main_contact.name}}!',
						body:'Hi {{invoice.main_contact.name}}, you have an invoice due in 3 days. Please click the link below to review and make a payment.',
						days:3,
						beforeAfter:'before'
					};
			}

			dom.modals.makeNode('modal','modal',{
				onShow:function(){

					var formObj = {
							subject:{
								name:'subject',
								label:'Subject Line',
								type:'text',
								value:setupObj.subject
							},
							body:{
								name:'body',
								label:'Body',
								type:'textbox',
								rows:8,
								value:setupObj.body
							},
							days:{
								name:'days',
								label:'Days',
								type:'number',
								value:setupObj.days
							},
							everyday:{
								name:'everyday',
								label:'Send every morning when balance is due?',
								type:'select',
								options:[
									{
										name:'No',
										label:'no',
										value:'no'
									},
									{
										name:'Yes',
										label:'yes',
										value:'yes'
									}
								]
							},
							beforeAfter:{
								name:'beforeAfter',
								label:'Before or After',
								type:'select',
								options:[
									{
										name:'After',
										label:'after',
										value:'after'
									},
									{
										name:'Before',
										label:'before',
										value:'before'
									}
								]
							}
						};

					if(setupObj.beforeAfter == 'before'){
						formObj.beforeAfter.options[1].selected = true;
					}else{
						formObj.beforeAfter.options[0].selected = true;
					}

					if(setupObj.everyday == 'yes'){
						formObj.everyday.options[1].selected = true;
					}else{
						formObj.everyday.options[0].selected = true;
					}

					dom.modals.modal.body.makeNode('title','div',{text:'Create an invoice notification email',css:'ui large header'});

					dom.modals.modal.body.makeNode('form','form',formObj);

					dom.modals.modal.body.makeNode('disclaimer','div',{text:'* The payment link will be inserted at the end of the message automatically.'});

					dom.modals.modal.body.makeNode('formBreak','div',{text:'<br />'});

					dom.modals.modal.body.makeNode('save','div',{css:'ui green button',text:'Save Email Notification'})
						.notify('click',{
							type:'invoicesRun',
							data:{
								run:function(){

									dom.modals.modal.body.save.loading();

									var formData = dom.modals.modal.body.form.process().fields;

									var emailObj = {
											subject:formData.subject.value,
											body:formData.body.value,
											beforeAfter:formData.beforeAfter.value,
											days:formData.days.value,
											everyday:formData.everyday.value
										};

									var createUpdate = 'create';
									if(currentObj){
										createUpdate = 'update';
										emailObj.id = setupObj.id;
									}

									sb.data.db.obj[createUpdate]('invoice_emails', emailObj, function(created){

										dom.modals.modal.hide();

										duePaymentEmailSettings(dom);

									});

								}
							}
						},sb.moduleId);

					dom.modals.modal.patch();

				}
			});

			dom.modals.patch();

			dom.modals.modal.show();

		}

		var stubbs = [
				{
					id:1,
					subject:'Upcoming payment',
					body:'Testing',
					beforeAfter:'before',
					days:1
				},
				{
					id:2,
					subject:'Late payment',
					body:'Testing',
					beforeAfter:'after',
					days:15
				}
			];

		dom.empty();

		dom.makeNode('seg','div',{css:'ui loading basic segment'});

		dom.patch();

		dom.empty();

		sb.data.db.obj.getAll('invoice_emails',function(emailObjs){

			dom.makeNode('modals','div',{});

			dom.makeNode('title', 'div', {text:'Due Payment Email Settings', css:'ui large header'});

			dom.makeNode('create', 'div', {css:'ui mini compact teal button', text:'Create Notification'})
				.notify('click',{
					type:'invoicesRun',
					data:{
						run:function(){

							createEdit(dom, false);

						}
					}
				},sb.moduleId);

			dom.makeNode('table', 'div', {tag:'table',css:'ui table'});

			dom.table.makeNode('thead','div',{tag:'thead'});
			dom.table.thead.makeNode('row','div',{tag:'tr'});
			dom.table.thead.row.makeNode('timing','div',{tag:'th', text:'Timing'});
			dom.table.thead.row.makeNode('name','div',{tag:'th', text:'Email Subject'});
			dom.table.thead.row.makeNode('template','div',{tag:'th', text:'Email Body'});
			dom.table.thead.row.makeNode('actions','div',{tag:'th', text:''});

			dom.table.makeNode('tbody','div',{tag:'tbody'});

			_.each(emailObjs, function(notification){

				var timingString = notification.days +' day(s) '+ notification.beforeAfter +' due.';

				if(notification.everyday == 'yes'){
					timingString = 'Sending every morning.';
				}

				dom.table.tbody.makeNode('row'+notification.id,'div',{tag:'tr'});
				dom.table.tbody['row'+notification.id].makeNode('timing','div',{tag:'td',text:timingString});
				dom.table.tbody['row'+notification.id].makeNode('name','div',{tag:'td',text:notification.subject});
				dom.table.tbody['row'+notification.id].makeNode('template','div',{tag:'td',text:notification.body});
				dom.table.tbody['row'+notification.id].makeNode('actions','div',{tag:'td'})
					.makeNode('btns','div',{css:'ui mini compact buttons'});

				dom.table.tbody['row'+notification.id].actions.btns.makeNode('delete','div',{css:'ui red button',text:'Delete'})
					.notify('click',{
						type:'invoicesRun',
						data:{
							run:function(){

								sb.dom.alerts.ask({
									title: 'Are you sure?',
									text: 'This cannot be undone.'
								}, function(resp){

									if(resp){

										swal.disableButtons();

										sb.data.db.obj.erase('invoice_emails', notification.id, function(deleted){

											swal.close();

											duePaymentEmailSettings(dom);

										});

									}

								});

							}
						}
					},sb.moduleId);
				dom.table.tbody['row'+notification.id].actions.btns.makeNode('edit','div',{css:'ui orange button',text:'Edit'})
					.notify('click',{
						type:'invoicesRun',
						data:{
							run:function(){

								createEdit(dom, notification);

							}
						}
					},sb.moduleId);

			});

			dom.patch();

		});

	}

	function editInvoice(bp, dom, invoice, draw, created){

		var objectType = this.objectType,
			contactId = this.contactId,
			objectId = this.objectId,
			clientId = this.clientId,
			state = this;

		var  formObj = {
/*
				invoice_type_list:{
					name:'invoice_type_list',
					label:'Invoice Type',
					type:'select',
					options:_.map(bp.invoice_type_list.options, function(v,k){
						return {
								name:v,
								value:k
							};
					}, [])
				},
*/
				name:{
					name:'name',
					label:'Invoice Name',
					type:'text',
					value:invoice.name
				},
				amount:{
					name:'amount',
					label:'Total Amount Due',
					type:'usd',
					value:invoice.amount
				},
				due_date:{
					name:'due_date',
					label:'Due Date',
					type:'date',
					dateFormat:'MM/DD/YYYY',
					value:moment(invoice.due_date, 'YYYY-MM-DD HH:mm:ss').startOf('day').format('MM/DD/YYYY')
				},
				memo:{
					name:'memo',
					label:'Memo',
					type:'textbox',
					value:invoice.memo
				}

			};

/*
		if ( invoice && invoice.locked != 'locked' ) {

			formObj.amount = {
				name:'amount',
				label:'Amount',
				type:'usd',
				value:invoice.amount
			}

		}
*/

		function saveForm(state, created){

			var formInfo = this.wrapper.head.cont.form.process(),
				dom = this,
				newInvoice = invoice;

			if(formInfo.completed == false){
				sb.dom.alerts.alert('Error', 'Please fill out the entire form', 'error');
				return;
			}

			dom.wrapper.head.btns.btnGroup.save.loading();

			if(state.project.owner){
				newInvoice.owner = state.project.owner.id;
			}

			_.each(formInfo.fields, function(f,k){

				newInvoice[k] = f.value;

			});

			newInvoice.balance = newInvoice.amount - newInvoice.paid;
			newInvoice.locked = 'locked';

			sb.data.db.obj.update('invoices', newInvoice, function(newObj){

				if(created){
					created(newObj);
				}else{

					sb.notify({
						type:'app-remove-main-navigation-item',
						data:{
							itemId:'self',
							viewId:'createInvoice'
						}
					});

					sb.notify({
						type:'app-navigate-to',
						data:{
							itemId:'workorders',
							viewId:'single-'+newObj.related_object.id
						}
					});

					domCache.dom.patch();

					domCache.after(domCache.dom);

				}

/*
				addLineItems.call(dom, newObj, function(domCache){

					if(newObj){

						if(created){
							created(newObj);
						}else{

							sb.notify({
								type:'app-remove-main-navigation-item',
								data:{
									itemId:'self',
									viewId:'createInvoice'
								}
							});

							sb.notify({
								type:'app-navigate-to',
								data:{
									itemId:'workorders',
									viewId:'single-'+newObj.related_object.id
								}
							});

							domCache.dom.patch();

							domCache.after(domCache.dom);

						}

					}else{

						dom.wrapper.head.btns.btnGroup.cancel.removeClass('disabled');
						dom.wrapper.head.btns.btnGroup.save.loading(false);
						sb.dom.alerts.alert('Error!', 'Please refresh and try again.', 'error');

					}

				});
*/

			}, 4);

		}

		dom.empty();
		dom.makeNode('wrapper', 'div', {css: 'ui stackable grid'});
		dom.wrapper.makeNode('head', 'div', {css:'row'});
		dom.wrapper.head.makeNode('col1', 'div', {css:'eight wide column'});
		dom.wrapper.head.col1.makeNode('title', 'headerText', {text:'Edit an Invoice'});
		dom.wrapper.head.makeNode('cont', 'container', {css: 'sixteen wide column'});
		dom.wrapper.head.cont.makeNode('form', 'form', formObj);
		dom.wrapper.head.makeNode('break', 'div', {css: 'sixteen wide column', text:'<br />'});
		dom.wrapper.head.makeNode('btns', 'div', {css: 'sixteen wide column'});

		dom.wrapper.head.btns.makeNode('btnGroup', 'div', {css:'ui buttons'});
		dom.wrapper.head.btns.btnGroup.makeNode('delete', 'div', {text:'<i class="fa fa-trash"></i> Delete', css:'ui basic red button'}).notify('click', {
			type:'invoicesRun',
			data:{
				run:function(){


					var hasPayments = false;
					var alertText = 'This cannot be undone.';

					if ( invoice.payments && invoice.payments.length > 0 )
						alertText = 'This will also delete any payments made to this invoice';

					sb.dom.alerts.ask(
						{
							title: 	'Are you sure?'
							, text: 	alertText
						}
						, function(resp){

							if(resp){

								swal.disableButtons();

								var paymentsToErase = invoice.payments;

								sb.data.db.obj.erase('invoices', invoice.id, function(){

									swal.close();

									if ( hasPayments ){

										sb.data.db.obj.erase('payments', paymentsToErase, function(){});

									}

									created(true);

								});
							}
						}
					);
				}
			}
		}, sb.moduleId);
		dom.wrapper.head.btns.btnGroup.makeNode('cancel', 'div', {text:'<i class="fa fa-times"></i> Cancel', css:'ui grey button'}).notify('click', {
			type:'invoicesRun',
			data:{
				run:function(){

					created();

				}
			}
		}, sb.moduleId);

		dom.wrapper.head.btns.btnGroup.makeNode('save', 'div', {text:'Save', css:'ui green button'}).notify('click', {
			type:'invoicesRun',
			data:{
				run:saveForm.bind(dom, state, created)
			}
		}, sb.moduleId);

		draw(dom);

	}

	function emailInvoice(obj, emailSent){

		this.modals.makeNode('modal', 'modal', {
			onShow:function(){

				sb.data.db.obj.getAll('invoice_system', function(sysSettings){

					var settings = sysSettings[0],
						objectId = obj.id,
						emailObjectType = 'invoices',
						emailAddress = '';

					if(obj.related_object){
						objectId = obj.related_object.id;
						emailObjectType = obj.related_object.object_bp_type;
					}

					if(obj.main_contact){
						if(obj.main_contact.contact_info){

							var count = 0;
							_.each(obj.main_contact.contact_info, function(i){

								if(i.type.data_type == 'email' && i.is_primary == 'yes'){

									if(count > 0){

										emailAddress += ', '+ i.info;

									}else{

										emailAddress += i.info;

									}

									count++;

								}

							});

						}
					}

					sb.notify({
						type:'show-compose-form',
						data:{
							domObj:m.body.cont,
							objectId:objectId,
							objectType:emailObjectType,
							email:{
								to:emailAddress,
								subject:_.where(settings.emails, {email_type:'initial'})[0].subject.replace(/{{INVOICE_NUMBER}}/g, obj.object_uid),
								message:_.where(settings.emails, {email_type:'initial'})[0].email.replace(/{{INVOICE_LINK}}/g, '<a href="https://pagoda.voltz.software/app/invoices/#?&i='+ appConfig.instance +'&iid='+ obj.id +'" target="_blank">https://pagoda.voltz.software/app/invoices/#?&i='+ appConfig.instance +'&iid='+ obj.id +'</a>')
							},
							action:function(sentEmail){

								sb.data.db.obj.update('invoices', {id:obj.id, sent:'Yes', sent_on:moment().format(), sent_by:+sb.data.cookie.userId}, function(updated){

									m.hide();

									if(emailSent){

										emailSent(updated);

										sb.dom.alerts.alert('Invoice sent!', '', 'success');

									}

								}, 2);

							}
						}
					});

				});

			}
		});

		var m = this.modals.modal;

/*
		m.body.makeNode('title', 'div', {css:'ui large header', text:'Send this invoice to someone'});

		m.body.makeNode('break', 'lineBreak', {});
*/

		m.body.makeNode('cont', 'div', {css:''});

		m.body.cont.makeNode('loader', 'loader', {});

		this.modals.patch();

		m.show();

	}

	function enterPayment(obj, mainDom, backButton, draw){

		function savePayment(inv, pay){

			if(Array.isArray(pay)){

				_.each(pay, function(updPay){

					var updInvoice = updPay.invoice;

					if(!updInvoice.payments)
						updInvoice.payments = [];

					updInvoice.paid += +updPay.amount;
					updInvoice.payments.push(updPay);
					updInvoice.balance = updInvoice.amount - updInvoice.paid;

					sb.data.db.obj.update('invoices', updInvoice, function(updatedInv){

						var noteObj = {
							type_id:updatedInv.id,
							type:'invoices',
							note: 'Payment for $'+ (updPay.amount/100).formatMoney(2) +' was made.',
							record_type:'log',
							author:sb.data.cookie.get('uid'),
							notifyUsers:[]
						};

						sb.data.db.obj.create('notes', noteObj, function(newNote){

							sb.dom.alerts.alert('Success!', '', 'success');

							dom.modals.modal.hide();

							sb.notify({
								type:'app-navigate-to',
								data:{
									itemId:'invoices',
									viewId:{
										id:'paid-table',
										type:'table',
										title:'PaidInvoices',
										icon:'<i class="fa fa-check"></i>',
										dom:mainDom
									}
								}
							});
						});

					}, 4);

				});

			}else{

				var updInvoice = inv;

				if(!updInvoice.payments)
					updInvoice.payments = [];

				updInvoice.paid += +pay.amount;
				updInvoice.payments.push(pay);
				updInvoice.balance = updInvoice.amount - updInvoice.paid;

				sb.data.db.obj.update('invoices', updInvoice, function(updatedInv){

					var noteObj = {
						type_id:updatedInv.id,
						type:'invoices',
						note: 'Payment for $'+ (pay.amount/100).formatMoney(2) +' was made.',
						record_type:'log',
						author:sb.data.cookie.get('uid'),
						notifyUsers:[]
					};

					sb.data.db.obj.create('notes', noteObj, function(newNote){

						sb.dom.alerts.alert('Success!', '', 'success');

						dom.modals.modal.hide();

						singleInvoice(updatedInv, dom, false, draw);

					});

				}, 4);

			}

		}

		var dom = this,
			total = 0;

		_.each(obj.items, function(item){

			total += (item.amount * item.quantity);

			// add tax
			if(item.tax_rate){

				total += (item.amount * item.quantity) * (item.tax_rate / 100);

			}

		});

		total = obj.balance;

		var contactId = 0;
		var clientId = 0;
		if(obj.main_contact){
			contactId = obj.main_contact.id;
			clientId = obj.main_contact.company.id;
		}

		dom.modals.makeNode('modal', 'modal', {
			onShow:function(){

				sb.notify({
					type:'show-make-payment-button',
					data:{
						domObj:dom.modals.modal.body.payNow,
						payment:{
							customerId:contactId,
							clientId:clientId,
							invoiceId:obj.id,
							price:Math.ceil(total),
							admin:true
						},
						buttonSetup:{
							text:'<i class="fa fa-credit-card"></i> Pay $'+ (Math.ceil(total)/100).formatMoney() +' now by card',
							css:'pda-btn-green pda-btn-fullWidth',
							skip:true,
							notification:'invoice-payment-completed',
							action:savePayment.bind(dom, obj),
							admin:true
						}
					}
				});

			}
		});

		dom.modals.modal.footer.makeNode('total', 'div', {text:'<small>$'+(Math.ceil(total)/100).formatMoney() +' due</small>', css:'ui huge center aligned header'});

		dom.modals.modal.body.makeNode('payNow', 'div', {});

		dom.modals.patch();

		dom.modals.modal.show();

	}

	function getAgingBuckets(data, type, callback) {

		var agingData;
		if (type = 'proposalsArray') {
			agingData = {
				type: 'invoices',
				subType: 'proposalsArray',
				proposalsArray: data,
			};
		}

		sb.data.db.service(
			'GroupByAgingService',
			'getAgingAccounts',
			agingData,
			(response) => callback(response)
		);

	}

	function getInvoiceImageHTML(invoice, callback, invoiceType){

		if(invoice){

			if(invoiceType) {

				if(invoice.company_logo) {

					if(invoice.company_logo.hasOwnProperty('id')) {

						//get html for logo and feed into callback(htmlLogo)
						callback('<img width=""; src="'+ sb.data.files.getURL(invoice.company_logo) +'" style="cursor:pointer!important;">');

					} else {

						callback('<i class="huge image icon"></i><div>'+ appConfig.headquarters.name +'<div>');

					}

				}else{

					//get images is_primary:"yes"[0] and feed html into callback
					sb.data.db.obj.getWhere('company_logo', {is_primary:"yes", childObjs:1}, function(logo){

						if(logo.length > 0){

							callback('<img width=""; src="'+sb.data.files.getURL(logo[0].company_logo) +'" style="cursor:pointer!important;">');

						}else{

							callback('<i class="huge image icon"></i><div>'+ appConfig.headquarters.name +'<div>');

						}

					});

				}

			}else{

				if(invoice.logo){

					//get html for logo and feed into callback(htmlLogo)
					callback('<img width=""; src="'+sb.data.files.getURL(invoice.logo.company_logo) +'" style="cursor:pointer!important;">');


				}else{

					//get images is_primary:"yes"[0] and feed html into callback
					sb.data.db.obj.getWhere('company_logo', {is_primary:"yes", childObjs:1}, function(logo){

						if(logo.length > 0){

							callback('<img width=""; src="'+sb.data.files.getURL(logo[0].company_logo) +'" style="cursor:pointer!important;">');

						}else{

							callback('<i class="huge image icon"></i><div>'+ appConfig.headquarters.name +'<div>');

						}

					});

				}

			}

		} else {

			//get images is_primary:"yes"[0] and feed html into callback
			sb.data.db.obj.getWhere('company_logo', {is_primary:"yes", childObjs:1}, function(logo){

				if(logo.length > 0){

					callback('<img width=""; src="'+sb.data.files.getURL(logo[0].company_logo) +'" style="cursor:pointer!important; max-height:90px;">');

				}else{

					callback('<i class="huge image icon"></i><div>'+ appConfig.headquarters.name +'<div>');

				}

			});

		}

	}

	function getInvoiceName (inv) {

		var invName = inv.name;

		if (
			!_.isEmpty(inv.related_object)
			&& !_.isEmpty(inv.related_object.main_object)
		) {

			invName += ' for '+ inv.related_object.main_object.name;

		}

		return invName;

	}

	function invoiceEmailsSettings(dom, bp, loaderOff){

		function saveEmail(formDom){

			var formInfo = formDom.form.process(),
				dom = this;

			if(formInfo.fields.email.value == '<br>'){
				sb.dom.alerts.alert('Error', 'Please provide some email text', 'error');
				return;
			}

			formDom.save.loading();

			sb.data.db.obj.getAll('invoice_system', function(systemSettings){

				if(systemSettings.length == 0){

					var emails = [];

				}else{

					var emails = systemSettings[0].emails;

				}

				if(_.where(emails, {email_type:formInfo.fields.emailType.value}).length > 0){

					_.where(emails, {email_type:formInfo.fields.emailType.value})[0].email = formInfo.fields.email.value;
					_.where(emails, {email_type:formInfo.fields.emailType.value})[0].email_type = formInfo.fields.emailType.value;
					_.where(emails, {email_type:formInfo.fields.emailType.value})[0].subject = formInfo.fields.subject.value;

					if(formInfo.fields.yes){
						_.where(emails, {email_type:formInfo.fields.emailType.value})[0].active = 'yes';
					}else{
						_.where(emails, {email_type:formInfo.fields.emailType.value})[0].active = 'no';
					}

				}else{

					var newObj = {
							email:formInfo.fields.email.value,
							email_type:formInfo.fields.emailType.value,
							subject:formInfo.fields.subject.value
						};

					if(formInfo.fields.yes){
						newObj.active = 'yes';
					}else{
						newObj.active = 'no';
					}

					emails.push(newObj);

				}

				if(systemSettings.length > 0){

					sb.data.db.obj.update('invoice_system', {id:systemSettings[0].id, emails:emails}, function(updatedSettingObj){

						invoiceEmailsSettings(dom, bp, true);

					});

				}else{

					sb.data.db.obj.create('invoice_system', {emails:emails}, function(newSettingObj){

						invoiceEmailsSettings(dom, bp, true);

					});

				}

			});

		}

		var formObj = {
				emailType:{
					name:'emailType',
					type:'hidden',
					value:''
				},
				active:{
					name:'active',
					type:'checkbox',
					label:'',
					options:[
						{
							name:'yes',
							label:'Yes',
							value:'yes',
							checked:true
						}
					]
				},
				subject:{
					name:'subject',
					type:'text',
					label:'Subject',
					value:appConfig.systemName +' Invoice Notice'
				},
				email:{
					name:'email',
					type:'textbox',
					label:'Edit Template Details',
					value:'',
					wysiwyg: {
						height: null,
						minHeight: 200,
						maxHeight: null,
						focus: true,
						toolbar: false
	/*
						hint:[{
							mentions: mappedMentions,
							match: /\B{{(\w*)$/,
							search: function(keyword, callback) {

								var ret = _.map(this.mentions, function(item){

									if(item.search(keyword) > -1){
										return item;
									}

								});

								callback(_.compact(ret));

							},
							content: function(item) {
								return '{{' + item + '}} ';
							}
						}]
	*/
					}
				}
			};

		if(loaderOff !== true){
			dom.empty();
			dom.makeNode('loader', 'loader', {});
			dom.patch();
		}

		sb.data.db.obj.getAll('invoice_system', function(settingsObj){

			var first = $.extend(true, {}, formObj),
				second = $.extend(true, {}, formObj),
				third = $.extend(true, {}, formObj),
				fourth = $.extend(true, {}, formObj);

			first.email.value = 'You have a new invoice (#{{INVOICE_NUMBER}}). Please use the link below to view and pay your invoice.';
			first.emailType.value = 'initial';
			delete first.active;

			second.active.options[0].label = 'Send this reminder 30 days after the invoice date for unpaid invoices?';
			second.emailType.value = '30day';
			second.email.value = 'Your invoice is now 30 days overdue.  Please pay your invoice.<br /><br />'+
				'To access your invoice from '+ appConfig.systemName +', go to:<br /><br />'+
				'{{INVOICE_LINK}}';

			third.active.options[0].label = 'Send this reminder 60 days after the invoice date for unpaid invoices?';
			third.emailType.value = '60day';
			third.email.value = 'Your invoice is now 60 days overdue.  Please pay your invoice.<br /><br />'+
				'To access your invoice from '+ appConfig.systemName +', go to:<br /><br />'+
				'{{INVOICE_LINK}}';

			fourth.active.options[0].label = 'Send this reminder 90 days after the invoice date for unpaid invoices?';
			fourth.emailType.value = '90day';
			fourth.email.value = 'Your invoice is now 90 days overdue.  Please pay your invoice.<br /><br />'+
				'To access your invoice from '+ appConfig.systemName +', go to:<br /><br />'+
				'{{INVOICE_LINK}}';

			if(settingsObj.length > 0){

				_.each(settingsObj[0].emails, function(email){

					switch(email.email_type){

						case 'initial':

							first.email.value = email.email;
							first.subject.value = email.subject;

							break;

						case '30day':

							second.email.value = email.email;
							second.subject.value = email.subject;

							if(email.active == 'yes'){
								second.active.options[0].checked = true;
							}else{
								second.active.options[0].checked = false;
							}

							break;

						case '60day':

							third.email.value = email.email;
							third.subject.value = email.subject;

							if(email.active == 'yes'){
								third.active.options[0].checked = true;
							}else{
								third.active.options[0].checked = false;
							}

							break;

						case '90day':

							fourth.email.value = email.email;
							fourth.subject.value = email.subject;

							if(email.active == 'yes'){
								fourth.active.options[0].checked = true;
							}else{
								fourth.active.options[0].checked = false;
							}

							break;

					}

				});

			}

			dom.makeNode('title', 'headerText', {css:'ui dividing header', text:'Invoice Email Templates', size:'small'});

			dom.makeNode('titleBreak', 'lineBreak', {});

			dom.makeNode('first', 'container', {title:'Initial Email', collapse:'closed', css:''});
			dom.first.makeNode('form', 'form', first);
			dom.first.makeNode('formSpacer', 'lineBreak', {spaces:1});
			dom.first.makeNode('save', 'button', {text:'Save Changes', css:'pda-btn-green'}).notify('click', {
				type:'invoicesRun',
				data:{
					run:saveEmail.bind(dom, dom.first)
				}
			}, sb.moduleId);

			dom.makeNode('break1', 'lineBreak', {});

			dom.makeNode('second', 'container', {title:'First Reminder Email <small>(sent 30 days after due date)</small>', collapse:'closed'});
			dom.second.makeNode('form', 'form', second);
			dom.second.makeNode('formSpacer', 'lineBreak', {spaces:1});
			dom.second.makeNode('save', 'button', {text:'Save Changes', css:'pda-btn-green'}).notify('click', {
				type:'invoicesRun',
				data:{
					run:saveEmail.bind(dom, dom.second)
				}
			}, sb.moduleId);

			dom.makeNode('break2', 'lineBreak', {});

			dom.makeNode('third', 'container', {title:'Second Reminder Email <small>(sent 60 days after due date)</small>', collapse:'closed'});
			dom.third.makeNode('form', 'form', third);
			dom.third.makeNode('formSpacer', 'lineBreak', {spaces:1});
			dom.third.makeNode('save', 'button', {text:'Save Changes', css:'pda-btn-green'}).notify('click', {
				type:'invoicesRun',
				data:{
					run:saveEmail.bind(dom, dom.third)
				}
			}, sb.moduleId);

			dom.makeNode('break3', 'lineBreak', {});

			dom.makeNode('fourth', 'container', {title:'Final Reminder Email <small>(sent 90 days after due date)</small>', collapse:'closed'});
			dom.fourth.makeNode('form', 'form', fourth);
			dom.fourth.makeNode('formSpacer', 'lineBreak', {spaces:1});
			dom.fourth.makeNode('save', 'button', {text:'Save Changes', css:'pda-btn-green'}).notify('click', {
				type:'invoicesRun',
				data:{
					run:saveEmail.bind(dom, dom.fourth)
				}
			}, sb.moduleId);

			if(loaderOff !== true){
				delete dom.loader;
			}

			dom.patch();

		});

	}

	function invoiceTypesSetup(dom, stripeAccount, callback){

		function getStripeAccount(stripeAccount, callback){

			if(!_.isEmpty(stripeAccount)){

				callback(stripeAccount)

			}else{

				sb.data.db.obj.getAll('instances', function(instances){

					sb.data.db.controller('getStripeConnectAccount', {accountId:instances[0].stripe_account_id}, function(stripeAccount){

						callback(stripeAccount);

					});

				});

			}



		}

		function updateInvoiceType(obj, callback){

			var dataCall = 'create';
			if(obj.id){
				dataCall = 'update';
			}

			sb.data.db.obj[dataCall]('invoice_type', obj, function(dbObj){

				callback(dbObj);

			});

		}

		function build_editInvoiceType(dom, type, connectAccounts) {

			var editDom = dom;

			editDom.empty();

			editDom.makeNode('load_cont', 'div', {css: 'text-center'});

			editDom.load_cont.makeNode('loader', 'loader', {});
			editDom.load_cont.makeNode('load_text', 'div', {text: 'Searching for invoice logo...'});

			getInvoiceImageHTML(type, function(image){

				var payoutAccounts = _.map(connectAccounts, function(ca){

						var ret = {
								name:ca.bank_name +' - xxxx'+ ca.last4 +' - '+ ca.routing_number +' - '+ ca.currency.toUpperCase(),
								value:ca.id
							};

						return ret;

					});

				payoutAccounts.unshift({
					name:'Please Choose',
					value:0
				});

				var formArgs = {
						invoice_type:{
							type:'text',
							label:'Invoice Type Name',
							name:'invoice_type',
							value:type.invoice_type
						},
						stripe_payout_account:{
							type:'select',
							label:'Deposit Account',
							name:'stripe_payout_account',
							options:payoutAccounts
						}
					};

				editDom.empty();

				editDom.makeNode('headerSpacer', 'lineBreak', {spaces:1});

				editDom.makeNode('body', 'div', {css:'small-container'});

				editDom.body.makeNode('title', 'headerText', {css:'ui dividing header', text:'Create Invoice Type', size:'small'});

				editDom.body.makeNode('cont', 'container', {}).makeNode('col', 'column', {w:16});

				editDom.body.cont.col.makeNode('form', 'form', formArgs);

				editDom.body.cont.col.makeNode('imageSpacer', 'lineBreak', {spaces:1});

				if(type.company_logo){

					editDom.body.cont.col.makeNode('imageBtn', 'div', {tag:'button', text:'Choose default image', css:'ui blue button'}).notify('click', {
						type: 'invoicesRun',
						data: {
							run: function(data) {

								buildLogoSelector.call(editDom, type, false, function(dom){

									dom.patch();

								}, {}, {}, {}, function(imgUploaded){

									invoiceTypesSetup(editDom);

								});

							}
						}
					}, sb.moduleId);

					editDom.body.cont.col.makeNode('imageBreak', 'lineBreak', {spaces:1});

				}

				editDom.body.cont.col.makeNode('image', 'div', {text:image});

				editDom.body.cont.col.makeNode('btnSpacer', 'lineBreak', {spaces:1});

				editDom.body.cont.col.makeNode('btns', 'buttonGroup', {css:'ui buttons'});

				editDom.body.cont.col.btns.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save', css:'ui green button'}).notify('click', {
					type:'invoicesRun',
					data:{
						run:function(type){

							var formData = this.body.cont.col.form.process(),
								dom = this;

							if(formData.completed == false){
								sb.dom.alerts.alert('No Name', 'Please provide a name', 'error');
								return;
							}

							dom.body.cont.col.btns.save.loading();

							updateInvoiceType({
								id:type.id,
								invoice_type:formData.fields.invoice_type.value,
								stripe_payout_account:formData.fields.stripe_payout_account.value
							}, function(updated){

								invoiceTypesSetup(dom);

							});

						}.bind(editDom, type)
					}
				}, sb.moduleId);

				editDom.body.cont.col.btns.makeNode('erase', 'button', {text:'<i class="fa fa-trash-o"></i> Delete', css:'ui red button'}).notify('click', {
					type:'invoicesRun',
					data:{
						run:function(type){

							sb.dom.alerts.ask({
								title: 'Are you sure?',
								text: 'This cannot be undone.'
							}, function(resp){

								if(resp){

									swal.disableButtons();

									sb.data.db.obj.erase('invoice_type', type.id, function(deleted){

										swal.close();

										invoiceTypesSetup(dom);

									});

								}

							});

						}.bind(editDom, type)
					}
				}, sb.moduleId);

				editDom.body.cont.col.btns.makeNode('back', 'button', {text:'<i class="fa fa-times"></i> Cancel', css:'ui red basic button'}).notify('click', {
					type:'invoicesRun',
					data:{
						run:function(){

							this.body.cont.col.btns.back.loading();

							invoiceTypesSetup(this);

						}.bind(editDom)
					}
				}, sb.moduleId);

				editDom.patch();

				editDom.body.cont.col.patch();

			}, true);

			editDom.patch();

		}

/*
		dom.empty();

		dom.makeNode('loader', 'loader', {});

		dom.patch();
*/

		getStripeAccount(stripeAccount, function(stripeAccount){

			sb.data.db.obj.getAll('invoice_type', function(types){

				if(stripeAccount){

					if(stripeAccount.external_accounts){

						var connectAccounts = stripeAccount.external_accounts.data;

					}else{

						var connectAccounts = [];

					}

				}else{

					var connectAccounts = [];

				}

				dom.empty();

				dom.makeNode('btns', 'buttonGroup', {});

				dom.btns.makeNode('new', 'button', {text:'<i class="fa fa-plus"></i> New Invoice Type', css:'ui green button'}).notify('click', {
					type:'invoicesRun',
					data:{
						run:function(){

							var payoutAccounts = _.map(connectAccounts, function(ca){

									var ret = {
											name:ca.bank_name +' - xxxx'+ ca.last4 +' - '+ ca.routing_number +' - '+ ca.currency.toUpperCase(),
											value:ca.id
										};

									return ret;

								});

							payoutAccounts.unshift({
								name:'Please Choose',
								value:0
							});

							this.btns.new.loading();

							var formArgs = {
									invoice_type:{
										type:'text',
										label:'Invoice Type Name',
										name:'invoice_type'
									},
									stripe_payout_account:{
										type:'select',
										label:'Deposit Account',
										name:'stripe_payout_account',
										options:payoutAccounts
									}
								};

							this.empty();

							this.makeNode('headerSpacer', 'lineBreak', {spaces:1});

							this.makeNode('body', 'div', {css:'small-container'});

							this.body.makeNode('title', 'headerText', {css:'ui dividing header', text:'Create Invoice Type', size:'small'});

							this.body.makeNode('cont', 'container', {}).makeNode('col', 'column', {w:16});

							this.body.cont.col.makeNode('form', 'form', formArgs);

/*
							this.body.cont.col.makeNode('imageSpacer', 'lineBreak', {spaces:1});

							this.body.cont.col.makeNode('imageBtn', 'div', {tag:'button', text:'Choose default image', css:'ui blue button'});
*/

							this.body.cont.col.makeNode('btnSpacer', 'lineBreak', {spaces:1});

							this.body.cont.col.makeNode('btns', 'buttonGroup', {css:'ui buttons'});

							this.body.cont.col.btns.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save', css:'ui green button'}).notify('click', {
								type:'invoicesRun',
								data:{
									run:function(){

										var formData = this.body.cont.col.form.process(),
											dom = this;

										if(formData.completed == false){
											sb.dom.alerts.alert('No Name', 'Please provide a name', 'error');
											return;
										}

										dom.body.cont.col.btns.save.loading();

										updateInvoiceType({
											invoice_type:formData.fields.invoice_type.value,
											stripe_payout_account:formData.fields.stripe_payout_account.value
										}, function(updated){

											invoiceTypesSetup(dom);

										});

									}.bind(this)
								}
							}, sb.moduleId);

							this.body.cont.col.btns.makeNode('back', 'button', {text:'<i class="fa fa-times"></i> Cancel', css:'ui red basic button'}).notify('click', {
								type:'invoicesRun',
								data:{
									run:function(){

										this.body.cont.col.btns.back.loading();

										invoiceTypesSetup(this);

									}.bind(this)
								}
							}, sb.moduleId);

							this.patch();

							/*
$(this.body.cont.col.imageBtn.selector).on('click', buildLogoSelector.bind(this, {}, false, function(dom){

								dom.patch();

							}, {}, {}));
*/

						}.bind(dom)
					}
				}, sb.moduleId);

				dom.makeNode('btnsSpacer', 'lineBreak', {spaces:2});

				dom.makeNode('cont', 'div', {css:'small-container'});

				dom.makeNode(
					'table',
					'table',
					{
						css: 'table-hover table-condensed',
						columns: {
							btns:'',
							name: 'Name',
							bank_account: 'Deposit Account'
						}
					}
				);

				_.each(types, function(t){

					dom.table.makeRow(
						'type-'+t.id,
						['', '', '']
					);

					var ca = _.where(connectAccounts, {id:t.stripe_payout_account})[0],
						accountString = '<i>Not selected</i>';

					if(ca){
						accountString = ca.bank_name +' - xxxx'+ ca.last4 +' - '+ ca.routing_number +' - '+ ca.currency.toUpperCase();
					}

					dom.table.body['type-'+t.id].name.makeNode('text', 'headerText', {text:t.invoice_type, size:'x-small'});
					dom.table.body['type-'+t.id].bank_account.makeNode('text', 'headerText', {text:accountString, size:'x-small'});
					dom.table.body['type-'+t.id].btns.makeNode('edit', 'button', {text:'<i class="fa fa-pencil"></i> Edit', css:'ui orange button'}).notify('click', {
						type:'invoicesRun',
						data:{
							run:function(type){

								build_editInvoiceType.call({}, dom, type, connectAccounts);

							}.bind(dom, t)
						}
					}, sb.moduleId);

				});


				dom.patch();

				if(callback){

					callback(true);

				}

			}, 1);

		});

	}

	function inoviceFeeSetup(dom, bp){

		sb.data.db.obj.getAll('invoice_fees', function(objects){

			sb.data.db.obj.getAll('chart_of_accounts', function(coas){

				var settingsObj = objects[0];

			    var formData = {
						ccPercent:{
							name:'ccPercent',
							label:'Credit Card Processing Fee (ex 2.9)',
							type:'number',
							value:0
						},
						ccFlatFee:{
							name:'ccFlatFee',
							label:'Credit Card Flat Fee (ex .30)',
							type:'number',
							value:0
						},
						achPercent:{
							name:'achPercent',
							label:'ACH Percent (ex .8)',
							type:'number',
							value:0
						},
						achFlatFee:{
							name:'achFlatFee',
							label:'ACH Flat Fee (ex 1.75)',
							type:'number',
							value:0
						},
						// ICGccPercent:{
						// 	name:'ICGccPercent',
						// 	label:'iCheckGateway Credit Card Processing Fee (ex 2.9)',
						// 	type:'hidden',
						// 	value:0
						// },
						// ICGccFlatFee:{
						// 	name:'ICGccFlatFee',
						// 	label:'iCheckGateway Credit Card Flat Fee (ex .30)',
						// 	type:'hidden',
						// 	value:0
						// },
						ICGachPercent:{
							name:'ICGachPercent',
							label:'iCheckGateway ACH Percent (ex .8)',
							type:'hidden',
							value:0
						},
						ICGachFlatFee:{
							name:'ICGachFlatFee',
							label:'iCheckGateway ACH Flat Fee (ex 1.75)',
							type:'hidden',
							value:0
						},
						chartOfAccount:{
							name:'chartOfAccount',
							label:'Chart of Account',
							type:'select',
							options:_.map(coas, function(obj){

								if(settingsObj){

									if(settingsObj.chart_of_account){

										if(settingsObj.chart_of_account.id == obj.id){
											return {
												name:obj.name,
												label:obj.id,
												value:obj.id,
												selected:true
											};
										}else{
											return {
												name:obj.name,
												label:obj.id,
												value:obj.id
											};
										}

									}else{

										return {
											name:obj.name,
											label:obj.id,
											value:obj.id
										};

									}

								}else{
									return {
										name:obj.name,
										label:obj.id,
										value:obj.id
									};
								}

							})
						}
				    };

				if(settingsObj){

					formData.ccPercent.value = +settingsObj.credit_card_percent;
					formData.ccFlatFee.value = +settingsObj.credit_card_flat_fee;
					formData.achPercent.value = +settingsObj.ach_percent;
					formData.achFlatFee.value = +settingsObj.ach_flat_fee;
					// formData.ICGccPercent.value = +settingsObj.ICG_credit_card_percent;
					// formData.ICGccFlatFee.value = +settingsObj.ICG_credit_card_flat_fee;
					formData.ICGachPercent.value = +settingsObj.ICG_ach_percent;
					formData.ICGachFlatFee.value = +settingsObj.ICG_ach_flat_fee;

				}

				if(appConfig.instance === 'infinity' || appConfig.instance === 'nlp' || appConfig.instance === 'rickyvoltz') {

					formData.ccPercent.label = "Stripe " + formData.ccPercent.label
					formData.ccFlatFee.label = "Stripe " + formData.ccFlatFee.label
					formData.achPercent.label = "Stripe " + formData.achPercent.label
					formData.achFlatFee.label = "Stripe " + formData.achFlatFee.label
					// formData.ICGccPercent.type = "number";
					// formData.ICGccFlatFee.type = "number";
					formData.ICGachPercent.type = "number";
					formData.ICGachFlatFee.type = "number";

				}

			    dom.empty();

			    dom.makeNode('title','div',{css:'ui header', text:'Invoice Fees'});

			    dom.makeNode('form','form',formData);

			    dom.makeNode('break','div',{text:'<br />'});

			    dom.makeNode('save', 'div', {css:'ui green button', text:'Save'}).notify('click',{
				    type:'invoicesRun',
				    data:{
					    run:function(){

						    dom.save.loading();

						    var formInfo = dom.form.process().fields;
						    var createUpdate = 'update';

							if(!settingsObj){

								createUpdate = 'create';

								settingsObj = {
										credit_card_percent:formInfo.ccPercent.value,
										credit_card_flat_fee:formInfo.ccFlatFee.value,
										ach_percent:formInfo.achPercent.value,
										ach_flat_fee:formInfo.ccFlatFee.value,
										// ICG_credit_card_percent:formInfo.ICGccPercent.value,
										// ICG_credit_card_flat_fee:formInfo.ICGccFlatFee.value,
										ICG_ach_percent:formInfo.ICGachPercent.value,
										ICG_ach_flat_fee:formInfo.ICGachFlatFee.value,
										chart_of_account:formInfo.chartOfAccount.value
									};

							}else{

								settingsObj.credit_card_percent=formInfo.ccPercent.value;
								settingsObj.credit_card_flat_fee=formInfo.ccFlatFee.value;
								settingsObj.ach_percent=formInfo.achPercent.value;
								settingsObj.ach_flat_fee=formInfo.achFlatFee.value;
								// settingsObj.ICG_credit_card_percent=formInfo.ICGccPercent.value;
								// settingsObj.ICG_credit_card_flat_fee=formInfo.ICGccFlatFee.value;
								settingsObj.ICG_ach_percent=formInfo.ICGachPercent.value;
								settingsObj.ICG_ach_flat_fee=formInfo.ICGachFlatFee.value;
								settingsObj.chart_of_account=formInfo.chartOfAccount.value;

							}

							sb.data.db.obj[createUpdate]('invoice_fees', settingsObj, function(udpated){

								dom.save.loading(false);

							});

					    }
				    }
			    }, sb.moduleId);

			    dom.patch();

			});

		}, 1);

	}

	function paymentScheduleSettings(dom, bp){

		console.log('DEBUG: paymentScheduleSettings called, about to call getAll for payment_schedule_template');
		console.log('DEBUG: dom:', dom, 'bp:', bp);
		console.log('DEBUG: getAll call parameters - objectType: payment_schedule_template, getChildObjs: 1');

		// Performance monitoring
		var startTime = performance.now();
		console.log('PERF: First getAll call started at:', startTime);

		// Add timeout detection
		var timeoutId = setTimeout(function() {
			console.error('DEBUG: getAll call timed out after 10 seconds - callback never executed');
			console.error('DEBUG: This suggests the API call is hanging on the backend');
		}, 10000);

		sb.data.db.obj.getAll('payment_schedule_template', function(objects){
			clearTimeout(timeoutId);
			var firstCallTime = performance.now();
			var firstCallDuration = firstCallTime - startTime;
			console.log('PERF: First getAll completed in:', firstCallDuration.toFixed(2), 'ms');
			console.log('DEBUG: getAll callback executed, objects received:', objects);
			console.log('DEBUG: objects length:', objects ? objects.length : 'objects is null/undefined');
			console.log('DEBUG: typeof objects:', typeof objects);
			if (objects && objects.length > 0) {
				console.log('DEBUG: sample object:', objects[0]);
				console.log('PERF: Sample object keys:', Object.keys(objects[0]));
				console.log('PERF: Checking for child objects in sample...');
				Object.keys(objects[0]).forEach(function(key) {
					if (typeof objects[0][key] === 'object' && objects[0][key] !== null && Array.isArray(objects[0][key])) {
						console.log('PERF: Child array found:', key, 'length:', objects[0][key].length);
					}
				});
			}

		    var comp = {
			    table:sb.createComponent('crud-table')
		    };

		var crudSetup = {
				domObj:dom,
				objectType:'payment_schedule_template',
				searchObjects:false,
				filters:false,
				download:false,
				headerButtons:{
					reload:{
						name:'Reload',
						css:'pda-btn-blue',
						action:function(){}
					},
					newObject:{
						name:'<i class="fa fa-plus"></i> Create New',
						css:'pda-btn-green',
						domType:'full',
						action:function(obj, domObj){

							var	formSetup = {
								name:{
									name:'name',
									type:'text',
									label:'Payment Template Name'
								}
							};

							domObj.makeNode('body', 'div', {css:'ui single column grid'});

							domObj.body.makeNode('btns', 'buttonGroup', {css:'ui buttons pull-right'});
							domObj.body.makeNode('title', 'headerText', {css:'ui dividing header', text:'Create Payment Schedule', size:'small'});
							domObj.body.makeNode('cont', 'container', {}).makeNode('col', 'column', {w:16});

							domObj.body.btns.makeNode('back', 'button', {css:'ui red basic button', text:'<i class="fa fa-arrow-left"></i> Cancel'}).notify('click', {
								type:'invoicesRun',
								data:{

									run:function(bp){

										paymentScheduleSettings(this, bp);

									}.bind(dom, bp)

								}
							}, sb.moduleId);
							domObj.body.btns.makeNode('save', 'button', {css:'ui green button', text:'<i class="fa fa-floppy-o"></i> Save'}).notify('click', {
								type:'invoicesRun',
								data:{

									run:function(){

										if(this.body.cont.col.form.process().completed == false){
											sb.dom.alerts.alert('Error', 'Please fill out the entire form.', 'error');
											return;
										}

										this.body.btns.save.loading();
										this.body.btns.back.css('pda-btnOutline-red pda-btn-disabled');

										var templateObj = {
												name:this.body.cont.col.form.process().fields.name.value
											};

										var dom = this;

										sb.data.db.obj.create('payment_schedule_template', templateObj, function(newObj){

											singlePaymentSchedule(newObj, dom);

										}, 1);

									}.bind(domObj)

								}
							}, sb.moduleId);

							domObj.body.cont.col.makeNode('form', 'form', formSetup);

							domObj.patch();

						}
					}
				},
				rowSelection:true,
				rowLink:{
					type:'tab',
					header:function(obj){
						return obj.name;
					},
					action:singlePaymentSchedule
				},
				multiSelectButtons:{
					erase:{
						name:'<i class="fa fa-trash-o"></i> Delete',
						css:'pda-btn-red',
						domType:'default',
						action:'erase'
					}
				},
				visibleCols:{
					name:'Name'
				},
				home:false,
				settings:false,
				cells: {
					name:function(obj){
						return obj.name;
					}
				},
				childObjs:1,
				data:function(paged, callback){

					console.log('DEBUG: Second getAll call - paged:', paged);
					console.log('DEBUG: Second getAll call parameters - objectType: payment_schedule_template, getChildObjs: 2, paged:', paged);

					// Performance monitoring for second call
					var secondStartTime = performance.now();
					console.log('PERF: Second getAll (paged) call started at:', secondStartTime);

					// Add timeout for second call
					var secondTimeoutId = setTimeout(function() {
						console.error('DEBUG: SECOND getAll call (paged) timed out after 15 seconds - callback never executed');
						console.error('DEBUG: This is the call that is actually hanging - paged query with getChildObjs: 2');
					}, 15000);

					sb.data.db.obj.getAll('payment_schedule_template', function(ret){
						clearTimeout(secondTimeoutId);
						var secondCallTime = performance.now();
						var secondCallDuration = secondCallTime - secondStartTime;
						console.log('PERF: Second getAll (paged) completed in:', secondCallDuration.toFixed(2), 'ms');
						console.log('DEBUG: Second getAll callback executed, ret:', ret);
						console.log('DEBUG: Second getAll ret length:', ret ? ret.length : 'ret is null/undefined');
						console.log('DEBUG: Second getAll ret type:', typeof ret);
						if (ret && ret.data && ret.data.length > 0) {
							console.log('PERF: Paged response - recordsTotal:', ret.recordsTotal, 'recordsFiltered:', ret.recordsFiltered);
							console.log('PERF: Sample paged object keys:', Object.keys(ret.data[0]));
						}
						callback(ret);

					}, 2, paged);

				}
			};

		comp.table.notify({
			type: 'show-table',
			data: crudSetup
		});

	}, 1);

	}

	function processUpdatedPricing(dom, obj, price, menuDom, priceDom){

		var total = 0;

		total = _.chain(price).reduce(function(memo, v, k){ return memo + v; }).value();

		if(!_.isEmpty(priceDom)){
			priceDom.empty();

			priceDom.makeNode('header', 'div', {css:'description', text:'Total Price'});
			priceDom.makeNode('cont', 'div', {css:'header', text:'$'+ (total/100).formatMoney()});
			priceDom.makeNode('meta', 'div', {css:'meta', text:'Estimated Value $' + (obj.potential_value/100).formatMoney(2)});

			priceDom.patch();
		}

		sb.data.db.obj.update('proposals', {id:obj.proposal.id, pricing:price, status:'Editing'}, function(done){

			//dom.empty();

			var invSetup = {
					dom: dom,
					state:{
						objectId:obj.proposal.id,
						contactId:obj.main_contact.id,
						objectType:obj.object_bp_type,
						price:price,
						onUpdate:function(callback, invoices, deleteInvoices){

							if(deleteInvoices){
								var newInvoiceArray = [];
							}else{
								var newInvoiceArray = _.pluck(obj.proposal.invoices, 'id');
							}

							sb.data.db.obj.update('proposals', {id:obj.proposal.id, pricing:price, invoices:newInvoiceArray.concat(_.pluck(invoices, 'id'))}, function(done){

								swal.close();

								callback(true);

								dom.empty();

								sb.notify({
									type: 'view-all-invoices2',
									data: invSetup
								});

							});

						}
					},
					draw:function(){

					},
					objectId: obj.proposal.id,
					contactId:obj.main_contact.id,
					objectType:obj.object_bp_type,
					dueDate: obj.proposal.event_start_date,
					price: price,
					balanceDom:priceDom,
					onUpdate:function(callback, invoices, deleteInvoices){

/*
						if(deleteInvoices){
							var newInvoiceArray = [];
						}else{
							var newInvoiceArray = _.pluck(obj.proposal.invoices, 'id');
						}

						sb.data.db.obj.update('proposals', {id:obj.proposal.id, pricing:price, invoices:newInvoiceArray.concat(_.pluck(invoices, 'id'))}, function(done){

							if(obj.status == 'Paid'){

								sb.data.db.obj.update('work_orders', {id:obj.id, status:'Signed'}, function(updated){

									buildHeaderNav(menuDom, updated.status);

									menuDom.patch();

									callback(true);

								});

							}else{

								callback(true);

							}

						});
*/

					}
			};

			sb.notify({
				type: 'view-all-invoices2',
				data: invSetup
			});

		});

	}

	function savePayment(inv, pay, refreshAction){

		if(Array.isArray(pay)){

			_.each(pay, function(updPay){

				var updInvoice = _.findWhere(inv, {id:updPay.invoice});

				if(!updInvoice.payments)
					updInvoice.payments = [];

				updInvoice.paid += +updPay.amount;
				updInvoice.payments.push(updPay);
				updInvoice.balance = updInvoice.amount - updInvoice.paid;

				sb.data.db.obj.update('invoices', updInvoice, function(updatedInv){

					var noteObj = {
						type_id:updatedInv.id,
						type:'invoices',
						note: 'Payment for $'+ (updPay.amount/100).formatMoney(2) +' was made.',
						record_type:'log',
						author:sb.data.cookie.get('uid'),
						notifyUsers:[]
					};

					sb.data.db.obj.create('notes', noteObj, function(newNote){



					});

				}, 4);

			});

			sb.dom.alerts.alert('Success!', '', 'success');

			if(refreshAction){

				refreshAction();

			}

		}else{

			var updInvoice = inv;

			if(!updInvoice.payments)
				updInvoice.payments = [];

			updInvoice.paid += +pay.amount;
			updInvoice.payments.push(pay);
			updInvoice.balance = updInvoice.amount - updInvoice.paid;

			sb.data.db.obj.update('invoices', updInvoice, function(updatedInv){

				var noteObj = {
					type_id:updatedInv.id,
					type:'invoices',
					note: 'Payment for $'+ (pay.amount/100).formatMoney(2) +' was made.',
					record_type:'log',
					author:sb.data.cookie.get('uid'),
					notifyUsers:[]
				};

				sb.data.db.obj.create('notes', noteObj, function(newNote){

					sb.dom.alerts.alert('Success!', '', 'success');

					if(refreshAction){

						refreshAction();

					}

				});

			}, 4);

		}

	}

	function singleInvoice(obj, domObj, backButton, draw){

		if(balanceUI.state){
			if(balanceUI.state.hide){
				balanceUI.state.hide();
			}
		}

		var invoiceStatus = 'Not sent';

		switch(obj.sent){

			case 'Yes':
			case 'sent':

				invoiceStatus = 'Sent on '+ moment(obj.sent_on).format('M/D/YYYY h:mm a');

				break;

		}

		var contactString = '<i>No contact assigned</i>',
			companyName = '<i>No company selected</i>',
			workOrder = '<i>Not assigned to a work order</i>';

		if(obj.main_contact){

			contactString = obj.main_contact.fname +' '+ obj.main_contact.lname +' <small><i class="fa fa-external-link"></i></small>';

		}

		if(obj.main_contact && obj.main_contact.company){
			companyName = obj.main_contact.company.name +' <small><i class="fa fa-external-link"></i></small>';
		}

		if(obj.related_object){
			workOrder = obj.related_object.name +' <small><i class="fa fa-external-link"></i></small>';
		}

		domObj.empty();

		domObj.makeNode('modals', 'div', {});

		domObj.makeNode('break', 'div', {text:'<br /><br />'});

		if(backButton){

			if(!backButton.id_hash){

				domObj.makeNode('back', 'div', {
					text:'<i class="left arrow icon"></i> Close'
					, css:'ui button'
					, tag: 'a'
					, href: sb.data.url.createPageURL('UP')
				});

			}

		}

		domObj.makeNode('break1', 'div', {text:'<br />'});

		var dom = domObj.makeNode('cont', 'div', {css:'ui stackable grid'});

		//dom.makeNode('header', 'column', {w: 5, css: 'ui grid'});
		dom.makeNode('info', 'column', {w:5, css: 'ui grid'});
		dom.makeNode('invoice', 'column', {w:11, css:''});

		dom.info.makeNode('left', 'column', {w: 16, css:''});
		dom.info.left.makeNode('name', 'div', {text:`<i class="fa fa-info"></i> ${obj.name}`, css:'ui large header'});
		dom.info.makeNode('right', 'column', {w: 16});
		dom.info.right.makeNode('btns', 'div', {css:'ui stackable buttons'});

		dom.info.right.makeNode('title', 'div', {css: ''});

		dom.invoice.makeNode('cont', 'div', {css: ''});
		dom.invoice.cont.makeNode('table', 'div', {css:''});

		var detailsCol = dom.info.right.title.makeNode('specs', 'div', {css:'ui items'});

		dom.info.right.title.makeNode('btns', 'div', {css:'one ui buttons'});

		dom.info.right.makeNode('payments', 'div', {css:''});

		if(obj.payments){
			if(obj.payments.length > 0){

				var totalPaid = _.reduce(obj.payments, function(memo, p){ return memo + +p.amount; }, 0);

				dom.info.right.payments.makeNode('total', 'headerText', {text:'Total Paid: $'+ (totalPaid/100).formatMoney(2), size:'x-small'});

				_.each(obj.payments, function(p){

					dom.info.right.payments.makeNode('pay-'+p.id, 'div', {});

					dom.info.right.payments['pay-'+p.id].makeNode('info', 'headerText', {text:'$'+ (p.amount/100).formatMoney(2) + ' <small>paid on</small> '+ moment(p.date_created).format('M/D/YYYY h:mm a') +' <i class="fa fa-eye"></i>', size:'xx-small'})
						.notify('click', {
							type:'invoicesRun',
							data:{
								run:function(payment, mainDom){

									var dom = this;

									dom.makeNode('modal', 'modal', {});

									dom.modal.body.makeNode('specs', 'div', {css:'ui items'});

									if(payment.details.type){

										dom.modal.body.specs.makeNode('id', 'div', {css:'item'})
											.makeNode('content', 'div', {css:'content'});
										dom.modal.body.specs.id.content.makeNode('header', 'div', {css:'description', text:'Payment ID'});
										dom.modal.body.specs.id.content.makeNode('meta', 'div', {css:'header', text:payment.id});

										dom.modal.body.specs.makeNode('amount', 'div', {css:'item'})
											.makeNode('content', 'div', {css:'content'});
										dom.modal.body.specs.amount.content.makeNode('header', 'div', {css:'description', text:'Total Paid'});
										dom.modal.body.specs.amount.content.makeNode('meta', 'div', {css:'header', text:(payment.amount/100).formatMoney(2)});

										dom.modal.body.specs.makeNode('payDate', 'div', {css:'item'})
											.makeNode('content', 'div', {css:'content'});
										dom.modal.body.specs.payDate.content.makeNode('header', 'div', {css:'description', text:'Paid On'});
										dom.modal.body.specs.payDate.content.makeNode('meta', 'div', {css:'header', text:moment(payment.date_created).format('M/D/YYYY h:mm a')});

										dom.modal.body.specs.makeNode('payBy', 'div', {css:'item'})
											.makeNode('content', 'div', {css:'content'});
										dom.modal.body.specs.payBy.content.makeNode('header', 'div', {css:'description', text:'Paid By'});
										dom.modal.body.specs.payBy.content.makeNode('meta', 'div', {css:'header', text:payment.details.type.toUpperCase()});

										dom.modal.body.specs.makeNode('notes', 'div', {css:'item'})
											.makeNode('content', 'div', {css:'content'});
										dom.modal.body.specs.notes.content.makeNode('header', 'div', {css:'description', text:'Notes'});
										dom.modal.body.specs.notes.content.makeNode('meta', 'div', {css:'header', text:payment.details.notes});

									}else{

										dom.modal.body.specs.makeNode('id', 'div', {css:'item'})
											.makeNode('content', 'div', {css:'content'});
										dom.modal.body.specs.id.content.makeNode('header', 'div', {css:'description', text:'Payment ID'});
										dom.modal.body.specs.id.content.makeNode('meta', 'div', {css:'header', text:payment.id});

										dom.modal.body.specs.makeNode('amount', 'div', {css:'item'})
											.makeNode('content', 'div', {css:'content'});
										dom.modal.body.specs.amount.content.makeNode('header', 'div', {css:'description', text:'Total Paid'});
										dom.modal.body.specs.amount.content.makeNode('meta', 'div', {css:'header', text:(payment.amount/100).formatMoney(2)});

										dom.modal.body.specs.makeNode('payDate', 'div', {css:'item'})
											.makeNode('content', 'div', {css:'content'});
										dom.modal.body.specs.payDate.content.makeNode('header', 'div', {css:'description', text:'Paid On'});
										dom.modal.body.specs.payDate.content.makeNode('meta', 'div', {css:'header', text:moment(payment.date_created).format('M/D/YYYY h:mm a')});

										dom.modal.body.specs.makeNode('payBy', 'div', {css:'item'})
											.makeNode('content', 'div', {css:'content'});
										dom.modal.body.specs.payBy.content.makeNode('header', 'div', {css:'description', text:'Paid By'});
										dom.modal.body.specs.payBy.content.makeNode('meta', 'div', {css:'header', text:payment.details.type.toUpperCase()});

										dom.modal.body.specs.makeNode('notes', 'div', {css:'item'})
											.makeNode('content', 'div', {css:'content'});
										dom.modal.body.specs.notes.content.makeNode('header', 'div', {css:'description', text:'Notes'});
										dom.modal.body.specs.notes.content.makeNode('meta', 'div', {css:'header', text:payment.details.notes});

										dom.modal.body.specs.makeNode('customer', 'div', {css:'item'})
											.makeNode('content', 'div', {css:'content'});
										dom.modal.body.specs.customer.content.makeNode('header', 'div', {css:'description', text:'Customer'});
										dom.modal.body.specs.customer.content.makeNode('meta', 'div', {css:'header', text:payment.details.customer});

									}

									dom.modal.footer.makeNode('btns', 'div', {css:'ui buttons'});

									if(!payment.details.type){
										dom.modal.footer.btns.makeNode('refund', 'button', {text:'<i class="fa fa-undo"></i> Refund', css:'pda-btn-orange'}).notify('click', {
											type:'invoicesRun',
											data:{
												run:function(invoice, payment){

													var dom = this;

													sb.dom.alerts.ask({
														title: 'Are you sure?',
														text: 'This cannot be undone.'
													}, function(resp){

														if(resp){

															swal.disableButtons();

															sb.data.db.controller('refundStripePayment', {transactionId:payment.details.id}, function(refundDetails){

																invoice.paid = invoice.paid - payment.amount;
																invoice.balance = invoice.balance + payment.amount;

																payment.amount = 0;
																payment.details = refundDetails;

																var noteObj = {
																		type_id: invoice.id,
																		type: 'invoices',
																		note: 'Payment '+ payment.details.id +' for $'+ (payment.amount/100).formatMoney(2) +' has been refunded on '+ moment().format('M/D/YYYY @ h:mm a') +'.',
																		record_type:'log',
																		author: sb.data.cookie.get('uid'),
																		notifyUsers: []
																	};

																sb.data.db.obj.create('notes', noteObj, function(newNote){

																	sb.data.db.obj.update('invoices', invoice, function(updatedInvoice){
// !@TODO .update('payments',
																		sb.data.db.obj.update('payments', payment, function(updated){

																			sb.dom.alerts.alert('Success', 'The payment has been refunded.', 'success');

																			dom.modal.hide();

																			sb.notify({
																				type:'app-redraw',
																				data:{}
																			});

																		});

																	}, 4);

																});

															});

														}

													});

												}.bind(dom, obj, payment)
											}
										}, sb.moduleId);
									}

									if(payment.details.type){
										dom.modal.footer.btns.makeNode('delete', 'button', {text:'<i class="fa fa-trash-o"></i> Delete', css:'pda-btn-red'}).notify('click', {
											type:'invoicesRun',
											data:{
												run:function(payment, invoice){

													var dom = this;

													sb.dom.alerts.ask({
														title: 'Are you sure?',
														text: 'This cannot be undone.'
													}, function(resp){

														if(resp){

															swal.disableButtons();

															invoice.paid = invoice.paid - payment.amount;
															invoice.balance = invoice.balance + payment.amount;
															invoice.payments = _.reject(invoice.payments, function(obj){ return obj.id == payment.id; });

															sb.data.db.obj.update('invoices', invoice, function(updatedInvoice){
// !@TODO .erase('payments'
																sb.data.db.obj.erase('payments', payment.id, function(deleted){

																	dom.modal.hide();

																	swal.close();

																	singleInvoice.call(this, updatedInvoice, mainDom, backButton, draw);

																});

															}, 4);

														}

													});

												}.bind(dom, payment, obj)
											}
										}, sb.moduleId);
									}

									dom.modal.footer.btns.makeNode('close', 'button', {text:'<i class="fa fa-times"></i> Close', css:'pda-btnOutline-red'}).notify('click', {
										type:'invoicesRun',
										data:{
											run:function(){

												this.modal.hide();

											}.bind(dom)
										}
									}, sb.moduleId);

									dom.patch();

									dom.modal.show();

								}.bind(domObj.modals, p, dom)
							}
						}, sb.moduleId);

					dom.info.right.payments['pay-'+p.id].makeNode('break', 'div', {text:'<br />'});

				});

			}else{

				dom.info.right.payments.makeNode('noPayments', 'headerText', {text:'No payments', size:'x-small', css:'text-center'});

			}
		}else{

			dom.info.right.payments.makeNode('noPayments', 'div', {text:'No payments', css:'ui small header'});

		}

		dom.info.right.makeNode('notesBreak', 'lineBreak', {});

		dom.info.right.makeNode('notes', 'container', {});

		dom.info.right.makeNode('payBtns', 'column', {width:6, offset:3});

		dom.info.right.payments.makeNode('makePayment', 'div', {text:'<i class="fa fa-usd"></i> Make A Payment', tag:'button', css:'ui green fluid button'}).notify('click', {
			type:'invoicesRun',
			data:{
				run:enterPayment.bind(domObj, obj, domObj, backButton, draw)
			}
		}, sb.moduleId);

		dom.info.right.btns.makeNode('pdf', 'button', {text:'PDF', css:'pda-btn-blue'}).notify('click', {
			type:'invoicesRun',
			data:{
				run:function(){

					sb.data.makePDF($(this.invoice.cont.table.selector).html(), 'I');

				}.bind(dom)
			}
		}, sb.moduleId);
		dom.info.right.btns.makeNode('edit', 'button', {text:'Edit', css:'pda-btn-orange'}).notify('click', {
			type:'invoicesRun',
			data:{
				run:function(obj){

					var modalDom = this;

					this.modals.makeNode('modal', 'modal', {
						onShow:function(){

							sb.data.db.obj.getAll('invoice_type', function(invoiceTypes){

								invoiceTypes.push({
									name:0,
									invoice_type:'Not selected'
								});

								modalDom.modals.modal.body.makeNode('title', 'headerText', {text:'Edit Invoice Details', size:'small'});

								modalDom.modals.modal.body.makeNode('break', 'lineBreak', {});

								modalDom.modals.modal.body.makeNode('form', 'form', {
									name:{
										name:'name',
										label:'Invoice Name',
										type:'text',
										value:obj.name,
									},
									due_date:{
										name:'due_date',
										label:'Due Date',
										type:'date',
										dateType:'date',
										value:moment(obj.due_date).startOf('day'),
										dateFormat:'YYYY-MM-DD'
									},
									amount:{
										name:'amount',
										label:'Amount',
										type:'usd',
										value:obj.amount
									}
/*
									invoice_type:{
										name:'invoice_type',
										label:'Invoice Type',
										type:'select',
										options:_.map(invoiceTypes, function(type){

											return {
												value:type.id,
												name:type.invoice_type
											};

										}),
										value:obj.invoice_type_list.id
									},
*/
/*
									memo:{
										name:'memo',
										label:'Memo',
										type:'textbox',
										rows:6,
										value:obj.memo
									}
*/
								});

								modalDom.modals.modal.footer.makeNode('btns', 'buttonGroup', {});

								modalDom.modals.modal.footer.btns.makeNode('close', 'button', {text:'Close', css:'pda-btnOutline-red'}).notify('click', {
									type:'invoicesRun',
									data:{
										run:function(){

											this.modals.modal.hide();

										}.bind(modalDom)
									}
								}, sb.moduleId);

								modalDom.modals.modal.footer.btns.makeNode('save', 'button', {text:'Save Changes', css:'pda-btn-green'}).notify('click', {
									type:'invoicesRun',
									data:{
										run:function(obj){

											var formInfo = this.modals.modal.body.form.process();

											/*
if(formInfo.completed == false){
												sb.dom.alerts.alert('Error', 'Please fill out the whole form', 'error');
												return;
											}
*/

											this.modals.modal.footer.btns.save.loading();
											this.modals.modal.footer.btns.close.css('pda-btnOutline-red pda-btn-disabled');

											var updateObj = {
													id:obj.id,
													due_date:formInfo.fields.due_date.value,
													name:formInfo.fields.name.value,
													amount:formInfo.fields.amount.value,
													balance:formInfo.fields.amount.value,
													locked:'locked'
												},
												dom = this;

											sb.data.db.obj.update('invoices', updateObj, function(updated){

												dom.modals.modal.hide(function(hidden){});

												singleInvoice(updated, domObj, backButton, draw);
												//addLineItems.call(dom, updated, draw);

											}, 2);

										}.bind(modalDom, obj)
									}
								}, sb.moduleId);

								modalDom.modals.modal.body.patch();
								modalDom.modals.modal.footer.patch();

							});

						}
					});
					this.modals.patch();
					this.modals.modal.show();

				}.bind(domObj, obj)
			}
		}, sb.moduleId);

		dom.makeNode('finalBreak', 'lineBreak', {});

		draw({
			dom:domObj,
			after:function(dom){

				buildInvoice.call(dom.cont.invoice.cont.table, obj, false, false, dom);

			}
		});

	}

	function singlePaymentSchedule(paymentTemplateObject, domObj){

		var singleUi = domObj;

		function getDataForForm(callback){

			var data = {};

			sb.data.db.obj.getAll('chart_of_accounts_companies', function(coaCompanies){

				sb.data.db.obj.getWhere('chart_of_accounts', {chart_of_accounts_company:coaCompanies[0].id}, function(categories){

					sb.data.db.obj.getAll('bank_account', function(accounts){

						sb.data.db.obj.getAll('invoice_type', function(invoices){

							//data.comboCategories = comboCategories;
							data.categories = categories;
							data.coaCompanies = coaCompanies;
							data.accounts = accounts;
							data.invoices = invoices;

							callback(data);

						});

					});

				});

			});

		}

		function preparePaymentTemplateForm(modal, data, template){

			var categoryNames = [],
				tableInfo = [],
				//comboCategories = data.comboCategories,
				categories = data.categories,
				accounts = data.accounts,
				invoices = data.invoices;

			_.each(categories, function(category){
				categoryNames.push({
				    name: 'chart_of_accounts',
				    label: category.name,
				    value: category.id
				});
			});

/*
			_.each(comboCategories, function(category){
				categoryNames.push({
				    name: 'chart_of_accounts',
				    label: 'Product Package >>'+ category.name,
				    value: category.id
				});
			});
*/

			var invoiceTypes = [];

			_.each(invoices, function(invoice){
				invoiceTypes.push({
				    name: invoice.invoice_type,
				    value: invoice.id
				});
			});

			var paymentScheduleForm = {
					name:{
						type:'text',
						name:'name',
						label:'Invoice Name'
					},
					payment_type: {
					    type: 'select',
					    name: 'payment_type',
					    label: 'Select One',
					    options: [
					         {
					              name: 'Percent of Total',
					              value: 'percentOfTotal'
					         },
					         {
					              name: 'Flat Rate',
					              value: 'flatRate'
					         },
					         {
					              name: 'Remaining Balance',
					              value: 'remainingBalance'
					         }
					    ]
					},
					percent_of_total: {
					    type: 'number',
					    name: 'percent_of_total',
					    placeholder: 'Percentage'
					},
					flat_rate: {
					    type: 'usd',
					    name: 'flat_rate',
					    placeholder: 'Dollar Amount'
					},
					before_after_type: {
					    type: 'select',
					    name: 'before_after_type',
					    label: 'What should trigger this invoice\'s due date?',
					    options: [
					         {
					              name: 'Work Order/Project Date',
					              value: 'project'
					         },
					         {
					              name: 'Proposal Acceptance Date',
					              value: 'proposal'
					         },
					         {
						         name:'Today',
						         value:'today'
					         }
					    ]
					},
					before_after: {
					    type: 'select',
					    name: 'before_after',
					    label: 'Is this payment due before or after the event / project date?',
					    options: [
					         {
					              name: 'Before',
					              value: 'before'
					         },
					         {
					              name: 'After',
					              value: 'after'
					         }
					    ]
					},
					due_date: {
					    type: 'number',
					    name: 'due_date',
					    label: 'How many days before or after?:'
					},
					invoice_type: {
					    type: 'select',
					    name: 'invoice_type',
					    label: 'Invoice Type',
					    options: invoiceTypes
					},
					chart_of_accounts_companies: {
					    type: 'select',
					    name: 'chart_of_accounts_companies',
					    label: 'Select a Chart of Account Company',
					    options: _.map(data.coaCompanies, function(coaCompany){

						    return {
							    name: coaCompany.name,
							    value: coaCompany.id
							};

					    }),
					    change: function(form, selected){

							sb.data.db.obj.getWhere('chart_of_accounts', {chart_of_accounts_company: parseInt(selected)}, function(coas){

							    form.chart_of_accounts.options = _.map(coas, function(coa){

								    return {
									    name: coa.name,
									    value: coa.id
									};

							    });

							    $(modal.body.paymentScheduleForm.chart_of_accounts.selector)
									.dropdown('change values', form.chart_of_accounts.options);

						    });

					    }
					},
					chart_of_accounts: {
					    type: 'checkbox',
					    name: 'chart_of_accounts',
					    label: 'Chart of Accounts',
					    options: _.map(data.categories, function(coa){

						    return {
							    name: 'chart_of_accounts',
							    label: coa.name,
							    value: coa.id
							};

					    })
					}

				}

			if(template){

				paymentScheduleForm.name.value = template.name;

				paymentScheduleForm.chart_of_accounts.value = [];

				_.each(template.chart_of_accounts, function(cat) {
				    paymentScheduleForm.chart_of_accounts.value.push(cat.id);
				});

				paymentScheduleForm.payment_type.value = template.payment_type;
				paymentScheduleForm.before_after_type.value = template.before_after_type;
				paymentScheduleForm.before_after.value = template.before_after;
				paymentScheduleForm.due_date.value = template.due_date;
				paymentScheduleForm.percent_of_total.value = template.percent_of_total;
				paymentScheduleForm.flat_rate.value = template.flat_rate;

				if(template.invoice_type){
					paymentScheduleForm.invoice_type.value = template.invoice_type.id;
				}

			}

			modal.empty();

			modal.makeNode('btns', 'buttonGroup', {css:'ui buttons pull-right'});

			modal.btns.makeNode('back', 'button', {text:'<i class="fa fa-arrow-left"></i> Cancel', css:'pda-btnOutline-red'}).notify('click', {
				type:'invoicesRun',
				data:{
					run:function(template){

						singlePaymentSchedule(template, this);

					}.bind(singleUi, paymentTemplateObject)
				}
			}, sb.moduleId);

			modal.makeNode('title', 'headerText', {css:'ui dividing header', text:'Create a new invoice template', size:'small'});

			modal.makeNode('body', 'container', {css:'pda-container', uiGrid:false});
			modal.makeNode('footer', 'container', {css:'pda-container', uiGrid:false});

			modal.body.makeNode('paymentScheduleForm', 'form', paymentScheduleForm); // Payment Schedule Form

			modal.patch();

			paymentScheduleForm.payment_type.change = radioOptionsChanged.bind(null, modal.body.paymentScheduleForm, modal)

			// If template already exists, display update button. If not, display Add button.
			if(template){

				modal.btns.makeNode(
				    'updatePaymentButton',
				    'button', {
				         text: 'Save Changes',
				         css:'pda-btn-green'
				    }).notify(
				    'click', {
				         type:'invoicesRun',
				         data:{
					         run:function(template, mainDom){

						        var newTemplate = {};
						        var domObj = this;
							   var formData = domObj.process();
							   var paymentType = '';

							   	if (formData.fields.payment_type.value == 'flatRate'){

							   		paymentType = 'flat_rate';

							   	} else if (formData.fields.payment_type.value == 'percentOfTotal') {

								   	paymentType = 'percent_of_total';

							   	}

/*
								if ( !formData.fields.inventory_billable_categories.value ) {

									sb.dom.alerts.alert('Wait!', 'Please make a category selection', 'error');
									return false;

								}
*/

								if (
									( paymentType == 'percent_of_total' || paymentType == 'flat_rate' )
									&& ( formData.fields.hasOwnProperty([paymentType]) && parseInt(formData.fields[paymentType].value) <= 0 )
								) {

									sb.dom.alerts.alert('Wait!', 'You must enter a number for this Payment Type', 'error');
									return false;

								}

								if ( formData.fields.due_date.value < 0) {

									sb.dom.alerts.alert('Wait!', 'Please make sure to specifiy the number of days', 'error');
									return false;

								}

						        // Delete Current List Item
								paymentTemplate.templates.splice(_.indexOf(paymentTemplate.templates, _.findWhere(paymentTemplate.templates, { id : template.id})), 1);

								sb.data.db.obj.update('payment_schedule_template', paymentTemplate, function(data){

									var paymentTypeVal = formData.fields.payment_type.value;

									_.each(formData.fields, function(field, key){

										if (
											( paymentTypeVal == 'percentOfTotal' && key == 'flat_rate' )
											|| ( paymentTypeVal == 'flatRate' && key == 'percent_of_total' )
											|| (
												paymentTypeVal == 'remainingBalance'
												&& (key == 'flat_rate' || key == 'percent_of_total')
											)
										) {

											newTemplate[key] = 0;

										} else {

											newTemplate[key] = field.value;

										}

									});

									// push to paymentTemplates
									paymentTemplate.templates.push(newTemplate);

									paymentTemplate.getChildObjs = 1;

									sb.data.db.obj.update('payment_schedule_template', paymentTemplate, function(done){

									    singlePaymentSchedule(done, singleUi);

									}, 1);

								}, 1);

					         }.bind(modal.body.paymentScheduleForm, template, modal, singleUi)
				         }
				    }, sb.moduleId
				);

			}else{

				modal.btns.makeNode(
				    'addPaymentButton',
				    'button', {
				         text: '<i class="fa fa-check"></i> Save',
				         css:'pda-btn-green'
				    }).notify(
				    'click', {
				         type:'invoicesRun',
				         data:{
					         run:function(template, mainDom){

						        var newTemplate = {},
						        	formData = this.process();

					               _.each(formData.fields, function(field, key){
					                    newTemplate[key] = field.value;
					               });

								   // push to paymentTemplates
					               paymentTemplate.templates.push(newTemplate);

					               paymentTemplate.getChildObjs = 1;

					               sb.data.db.obj.update('payment_schedule_template', paymentTemplate, function(response){

					                    singlePaymentSchedule(response, singleUi);

					               });

					         }.bind(modal.body.paymentScheduleForm, template, modal, singleUi)
				         }
				    }, sb.moduleId
				);

			}

			modal.makeNode('spacer', 'lineBreak', {spaces:1});

			modal.show();

			modal.patch();

			radioOptionsChanged(modal.body.paymentScheduleForm, modal);

		}

		function preparePaymentTemplateTable(container, paymentTemplates){

	          if(container.myTable){
		          delete container.myTable;
	          }

	          container.makeNode('myTable', 'table', {
                    css: 'table-hover table-condensed',
                    columns: {
	                    name: 'Name',
	                    chart_of_accounts: 'Chart Of Accounts',
	                    paymentType: 'Payment Type',
	                    paymentAmount: 'Payment Amount',
	                    dueDate: 'Due Date',
	                    dueDateType: 'Due Date Type',
	                    invoiceType: 'Invoice Type',
	                    btns: ''
	               }
	          });

	          var count = 0;
	          var paymentDueDate;
	          var paymentValue;
	          var rowName;
	          var templateName;

	          paymentTemplates = _.sortBy(paymentTemplates, function(paymentTemplate) {
	               if (paymentTemplate.before_after == 'before') {
	                    return -paymentTemplate.due_date
	               } else {
	                    return paymentTemplate.due_date
	               }
	          });

	          _.each(paymentTemplates, function(payment){
	               var categoryName = '';
	               count = count+1;
	               rowName = 'row-' + count;

	               _.each(payment.chart_of_accounts, function(cat, i){

	                    if (i == 0) {
	                         categoryName = cat.name;
	                    } else {
	                         categoryName += ', ' + cat.name;
	                    }

	               });

	               if (payment.payment_type == 'flatRate') {
	                    paymentValue = '$' + (payment.flat_rate / 100).toFixed(2);
	               } else if (payment.payment_type == 'percentOfTotal') {
	                    paymentValue = payment.percent_of_total + '%';
	               } else {
	                    paymentValue = '';
	               };

	               if (payment.before_after == 'before') {
	                    paymentDueDate = payment.due_date + ' days before';
	               } else {
	                    paymentDueDate = payment.due_date + ' days after';
	               }
	               if (payment.before_after == 'today') {
	                    paymentDueDate = 'today';
	               }

					var invoiceTypeString = '<i>No type selected</i>';
					if(payment.invoice_type){
						invoiceTypeString = payment.invoice_type.invoice_type;
					}

					if(payment.name){
						templateName = payment.name;
					}

	               container.myTable.makeRow(
	                    rowName,
	                    [templateName, categoryName, payment.payment_type_name, paymentValue, paymentDueDate, payment.before_after_type_name, invoiceTypeString, '']
	               );

				   container.myTable.body[rowName].btns.makeNode('editCOA', 'button', {text:'<i class="fa fa-pencil"></i> Edit Chart of Accounts', css:'pda-btn-blue pda-btn-fullWidth'}).notify('click', {
	                    type:'invoicesRun',
	                    data:{
		                    run:function(payment){

			                    var dom = this;
			                    var selectedAccounts = _.map(payment.chart_of_accounts, function(obj){
									                    return obj.id;
								                    });

			                    sb.data.db.obj.getAll('chart_of_accounts_companies', function(coaCompanies){

									sb.data.db.obj.getWhere('chart_of_accounts', {chart_of_accounts_company:coaCompanies[0].id}, function(coas){

										var selectedForm = {
							                    selected:{
								                    type:'checkbox',
								                    name:'selected',
								                    label:'',
								                    options:[],
								                    selected:_.map(payment.chart_of_accounts, function(obj){
									                    return obj.id;
								                    })
							                    }
						                    };

										var coaSelectionForm = {
												chart_of_accounts: {
												    type: 'checkbox',
												    name: 'chart_of_accounts',
												    label: 'Chart of Accounts',
												    options: _.map(coas, function(coa){

													    return {
														    name: 'chart_of_accounts',
														    label: coa.name,
														    value: coa.id
														};

												    })
												}
						                    };

										dom.empty();

					                    dom.makeNode('selectedHeader', 'div', {css:'ui header', text:'Selected Charts of Accounts'});
					                    dom.makeNode('selectedFormCont', 'div', {});
					                    dom.makeNode('buttonBreak', 'div', {text:'<br />'});
					                    dom.makeNode('save', 'div', {css:'ui green button', text:'Save Selected Accounts'}).notify('click', {
						                    type:'invoicesRun',
						                    data:{
							                    run:function(){

								                    var selected = _.uniq(selectedAccounts);

													_.findWhere(paymentTemplateObject.templates, {id: payment.id}).chart_of_accounts = selected;

								                    sb.data.db.obj.update('payment_schedule_template', {id:paymentTemplateObject.id, templates:paymentTemplateObject.templates}, function(updated){

														singlePaymentSchedule(updated, dom);

								                    }, 2);
							                    }
						                    }
					                    }, sb.moduleId);

					                    _.each(payment.chart_of_accounts, function(account){

						                    dom.selectedFormCont.makeNode('label-'+account.id, 'div', {css:'ui icon label', text:account.name +' <i class="close icon"></i>'}).notify('click', {
							                    type:'invoicesRun',
							                    data:{
								                    run:function(){

									                    selectedAccounts = _.reject(selectedAccounts, function(obj){
										                    return obj == account.id;
									                    });

														$(dom.selectedFormCont['label-'+account.id].selector).remove();

								                    }
							                    }
						                    }, sb.moduleId);

					                    });

					                    dom.makeNode('break', 'div', {text:'<br /><br />'});

					                    dom.makeNode('coaCompanyCont', 'div', {css:'ui secondary segment'});

					                    dom.coaCompanyCont.makeNode('coaCompanySelectionForm', 'form', {
						                    chart_of_accounts_companies: {
											    type: 'select',
											    name: 'chart_of_accounts_companies',
											    label: 'Select a Chart of Account Company',
											    options: _.map(coaCompanies, function(coaCompany){

												    return {
													    name: coaCompany.name,
													    value: coaCompany.id
													};

											    }),
											    change: function(form, selected){

												    sb.data.db.obj.getWhere('chart_of_accounts', {chart_of_accounts_company:+selected}, function(coas){

													    coaSelectionForm.chart_of_accounts.options = [];
													    coaSelectionForm.chart_of_accounts.options = _.map(coas, function(coa){

														    return {
															    name: 'chart_of_accounts',
															    label: coa.name,
															    value: coa.id
															};

													    });

													    dom.coaCompanyCont.coaContainer.makeNode('coaSelectionForm', 'form', coaSelectionForm);

													    dom.coaCompanyCont.coaContainer.patch();

												    });



											    }
											}
					                    });

					                    dom.coaCompanyCont.makeNode('coaContainer', 'div', {css:'ui basic segment'});

					                    dom.coaCompanyCont.coaContainer.makeNode('coaSelectionForm', 'form', coaSelectionForm);

					                    dom.coaCompanyCont.coaContainer.makeNode('break', 'div', {text:'<br />'});

					                    dom.coaCompanyCont.coaContainer.makeNode('save', 'div', {css:'ui blue button', text:'Add These Accounts'}).notify('click', {
						                    type:'invoicesRun',
						                    data:{
							                    run:function(){

								                    var selected = dom.coaCompanyCont.coaCompanySelectionForm.process().fields.chart_of_accounts_companies.value;

								                    sb.data.db.obj.getWhere('chart_of_accounts', {chart_of_accounts_company:+selected}, function(coas){

								                    	var accounts = dom.coaCompanyCont.coaContainer.coaSelectionForm.process().fields.chart_of_accounts.value;

								                    	_.each(accounts, function(coaId){

															selectedAccounts.push(+coaId);

									                    	dom.selectedFormCont.makeNode('label-'+coaId, 'div', {css:'ui icon label', text:_.where(coas, {id:+coaId})[0].name +' <i class="close icon"></i>'});

														});

														dom.selectedFormCont.patch();

								                    });

							                    }
						                    }
					                    }, sb.moduleId);

					                    dom.patch();

				                    });

			                    });

		                    }.bind(container, payment)
	                    }
	               });

	               container.myTable.body[rowName].btns.makeNode('editBtn', 'button', {text:'<i class="fa fa-pencil"></i> Edit', css:'pda-btn-orange pda-btn-fullWidth'}).notify('click', {
	                    type:'invoicesRun',
	                    data:{
		                    run:function(payment){

			                    var dom = this;

			                    getDataForForm(function(data){

				                   preparePaymentTemplateForm(dom, data, payment);

			                    });

		                    }.bind(container, payment)
	                    }
	               });

	               container.myTable.body[rowName].btns.makeNode('deleteBtn', 'button', {text: '<i class="fa fa-times"></i> Delete', css:'pda-btn-red pda-btn-fullWidth'}).notify('click', {
	                    type:'invoicesRun',
	                    data:{
		                    run:function(payment, paymentTemplates) {

							var domObj = this;

							sb.dom.alerts.ask({
								title: 'Are you sure?',
								text: ''
							}, function(resp){
							    if(resp) {

							         paymentTemplates.splice(_.indexOf(paymentTemplates, _.findWhere(paymentTemplates, { id : payment.id})), 1);

							         paymentTemplate.templates = paymentTemplates;

							         sb.data.db.obj.update('payment_schedule_template', paymentTemplate, function(data) {

								        singlePaymentSchedule(data, domObj);

							         }, 1);

							         sb.dom.alerts.alert('Complete', 'Payment Schedule Deleted', 'success');

							    }

							});
						}.bind(container, payment, paymentTemplates)
	                    }
	               });

	          }, this);
	     }

		function radioOptionsChanged(data, domObj){

			var formData = data.process();

			$(domObj.body.paymentScheduleForm.percent_of_total.selector).hide();
			$(domObj.body.paymentScheduleForm.flat_rate.selector).hide();

			if(formData.fields.payment_type){

			   if(formData.fields.payment_type.value == 'percentOfTotal'){

			        $(domObj.body.paymentScheduleForm.percent_of_total.selector).show();
   			        $(domObj.body.paymentScheduleForm.flat_rate.selector).hide();

			   }else if(formData.fields.payment_type.value == 'flatRate'){

			        $(domObj.body.paymentScheduleForm.percent_of_total.selector).hide();
			        $(domObj.body.paymentScheduleForm.flat_rate.selector).show();

			   }else{

			        $(domObj.body.paymentScheduleForm.percent_of_total.selector).hide();
			        $(domObj.body.paymentScheduleForm.flat_rate.selector).hide();

			   }

			}

		}

		count = 0;
		paymentTemplate = paymentTemplateObject;

		// add id to each template object in array
		_.each(paymentTemplate.templates, function(template) {
			count += 1;
			template.id = count;
		});

		domObj.empty();

		domObj.makeNode('panel', 'container', {style:'width:100%!important;'}).makeNode('body', 'column', {w:16});

		domObj.makeNode('modal', 'modal', {css:'small-container'});

		domObj.panel.body.makeNode('btns', 'buttonGroup', {css:'ui buttons pull-right'});

		domObj.panel.body.btns.makeNode('edit', 'button', {text:'<i class="fa fa-pencil"></i> Edit Name', css:'ui orange button'}).notify('click', {
			type:'invoicesRun',
			data:{
				run:function(obj){

					var	formSetup = {
						name:{
							name:'name',
							label:'Payment Template Name',
							type:'text',
							value:obj.name
						}
					};

					domObj.empty();

					domObj.makeNode('bodyTopSpacer', 'lineBreak', {spaces:1});
					domObj.makeNode('body', 'div', {style:'width:100%!important;'});
					domObj.makeNode('bodyBottomSpacer', 'lineBreak', {spaces:1});

					domObj.body.makeNode('btns', 'buttonGroup', {css:'ui buttons pull-right'});
					domObj.body.makeNode('title', 'headerText', {css:'ui dividing header', text:'Editing '+ obj.name, size:'small'});
					domObj.body.makeNode('cont', 'container', {}).makeNode('col', 'column', {w:16});

					domObj.body.btns.makeNode('back', 'button', {text:'<i class="fa fa-arrow-left"></i> Cancel', css:'ui red basic button'}).notify('click', {
						type:'invoicesRun',
						data:{
							run:function(obj){

								singlePaymentSchedule(obj, this);

							}.bind(domObj, obj)
						}
					}, sb.moduleId);
					domObj.body.btns.makeNode('save', 'button', {text:'<i class="fa fa-check"></i> Save', css:'ui green button'}).notify('click', {
						type:'invoicesRun',
						data:{
							run:function(obj){

								if(this.body.cont.col.form.process().completed == false){
									sb.dom.alerts.alert('Error', 'Please fill out the whole form', 'error');
									return;
								}

								this.body.btns.save.loading();
								this.body.btns.back.css('pda-btnOutline-red pda-btn-disabled');

								var domObj = this;

								sb.data.db.obj.update('payment_schedule_template', {id:obj.id, name:this.body.cont.col.form.process().fields.name.value}, function(updated){

									singlePaymentSchedule(updated, domObj);

								}, 1);

							}.bind(domObj, obj)
						}
					}, sb.moduleId);

					domObj.body.cont.col.makeNode('form', 'form', formSetup);

					domObj.patch();

				}.bind(domObj, paymentTemplate)
			}
		}, sb.moduleId);

		domObj.panel.body.btns.makeNode('button', 'button', {text: '<i class="fa fa-plus"></i> Create New Invoice', css: 'ui green button'}).notify('click', {
			type: 'invoicesRun',
			data: {
				run:function(template){

					var domObj = this;

					domObj.panel.body.btns.button.loading();

					// empty modal
					domObj.modal.body.empty();
					domObj.modal.footer.empty();

					// make title
					domObj.modal.body.makeNode('header', 'headerText', {size: 'small', css: 'text-center', text: 'Create New Payment'});

					getDataForForm(function(formData){

						preparePaymentTemplateForm(domObj.panel.body, formData);

					});

				}.bind(domObj, paymentTemplateObject)
			}
		}, sb.moduleId);

		domObj.panel.body.makeNode('title', 'headerText', {css:'ui dividing header', text:paymentTemplateObject.name, size:'small'});

		domObj.panel.body.makeNode('tableTitle', 'headerText', {text:'Invoice Template List', size:'x-small'});

		preparePaymentTemplateTable(domObj.panel.body, paymentTemplate.templates);

		domObj.panel.body.makeNode('bottomSpacer', 'lineBreak', {spaces:1});

		domObj.patch();

		getDataForForm(function(formData){

			dataForForm = formData;

		});

	}

	function tableState(objectType, objectId, contactId, price, dueDate, data){

		function buildTable(objId){

			var crudSetup = {
					domObj:this,
					tableTitle:'',
					objectType:'invoices',
					searchObjects:false,
					filters:false,
					download:false,
					headerButtons:{
						reload:{
							name:'Reload',
							css:'pda-btn-blue',
							action:function(){}
						}
					},
					rowSelection:true,
					rowLink:{
						type:'app_component',
						header:function(obj){
							return obj.name;
						},
						action:singleInvoice
					},
					multiSelectButtons:{
						erase:{
							name:'<i class="fa fa-trash-o"></i> Delete',
							css:'pda-btn-red',
							domType:'erase',
							action:'erase'
						}
					},
					visibleCols:{
						name:'Name',
						invoice_type_list:'Type',
						amount:'Total Amount',
						balance:'Balance Due',
						due_date:'Due Date',
						sent:'Sent'
					},
					dateRange:'next_service_date',
					cells: {
						date_created:function(obj){
							return moment(obj.date_created).format();
						},
						due_date:function(obj){
							return moment(obj.due_date).format('M/D/YYYY');
						},
						amount:function(obj){

							var invoiceTotal = 0;

							_.each(obj.items, function(i){

								if(i.tax_rate){

									if(i.tax_rate > 0){

										invoiceTotal += ((+i.amount * (+i.tax_rate/100)) + +i.amount);

									}

								}else{

									invoiceTotal += +i.amount;

								}

							});

							return '$'+ (obj.amount/100).formatMoney(2);
							//return '$'+parseInt(invoiceTotal/100).formatMoney(2);
						},
						paid:function(obj){

							return '$'+(obj.paid/100).formatMoney(2);
						},
						balance:function(obj){

							var balance = 0,
								invoiceTotal = 0;

							_.each(obj.items, function(i){

								if(i.tax_rate){

									if(i.tax_rate > 0){

										invoiceTotal += ((+i.amount * (+i.tax_rate/100)) + +i.amount);

									}

								}else{

									invoiceTotal += +i.amount;

								}

							});

							balance = Math.ceil(invoiceTotal) - obj.paid;

							return '$'+ (obj.balance/100).formatMoney(2);

						},
						sent:function(obj){

							if(obj.sent == 'Yes'){

								return '<div class="ui green label">Sent by '+ obj.sent_by.fname +' '+ obj.sent_by.lname +' on '+ moment(obj.sent_on).format('M/D/YYYY') +'</div>';

							}else{

								return '<div class="ui label">Not sent yet</div>';

							}

						}
					},
					childObjs:4,
					data:function(paged, callback){

						sb.data.db.obj.getAll('invoices', function(ret){

							callback(ret);

						}, 4, paged);

					}
				};
			var domObj = this;

			if(objectId > 0){
				crudSetup.rowLink.itemId = 'invoices';
				crudSetup.rowLink.type = 'custom';
				crudSetup.rowLink.action = function(obj, draw){

// 					singleInvoice(obj, domObj, true, draw);
				}
			}

			if(objectId > 0 && objectId != contactId){

				crudSetup.noObjects = {
					action:chooseInvoiceTemplate.bind({contactId:contactId, objectId:objectId, price:price, dueDate:dueDate, objectType:objectType, onUpdate:data.onUpdate})
				};

				crudSetup.headerButtons.create = {
					name:'<i class="fa fa-plus"></i> Create Invoice',
					css:'pda-btn-green',
					domType:'modal',
					objectType:objectType,
					objectId:objectId,
					contactId:contactId,
					action:function(bp, dom){

						var setup = this;

						createInvoice.call(setup, bp, dom.body, setup, function(done){

							done.patch();

						}, function(created){

							dom.hide();

							tableUI.state.show();

						});

					}.bind({contactId:contactId, objectId:objectId, price:price, dueDate:dueDate, objectType:objectType})
				};

				crudSetup.headerButtons.template = {
					name:'<i class="fa fa-retweet"></i> Apply Another Template',
					css:'pda-btn-primary',
					domType:'full',
					objectType:objectType,
					objectId:objectId,
					contactId:contactId,
					price:price,
					dueDate:dueDate,
					action:chooseInvoiceTemplate.bind({contactId:contactId, objectId:objectId, price:price, dueDate:dueDate, objectType:objectType, onUpdate:data.onUpdate})
				};

				crudSetup.data = function(paged, callback){

					sb.data.db.obj.getWhere('invoices', {related_object:objectId, childObjs:2, paged:paged}, function(ret){

						callback(ret);

						invoiceList = ret.data;

						balanceUI.state(objectType, objectId, contactId, price, dueDate, data);

						balanceUI.state.show(invoiceList);

					});

				};

			}

			if(contactId === objectId){

				crudSetup.headerButtons.create = {
					name:'<i class="fa fa-plus"></i> Create Invoice',
					css:'pda-btn-green',
					domType:'full',
					objectType:objectType,
					objectId:objectId,
					contactId:contactId,
					action:createInvoice
				};

				crudSetup.data = function(paged, callback){

					sb.data.db.obj.getWhere('invoices', {main_contact:contactId, active:'Yes', childObjs:3, paged:paged}, function(ret){

						callback(ret);

					});

				};

			}

			if(objectType == 'projectss'){

				var dom = this;

				dom.empty();

				sb.data.db.obj.getWhere('invoices', {related_object:objectId, childObjs:2}, function(ret){

					if(ret.length == 0){

						dom.makeNode('tableCont', 'div', {css:'ui huge header', text:'No invoices yet'});

						dom.patch();

					}else{

						dom.makeNode('tableCont', 'div', {css:'ui grid'})
							.makeNode('cont', 'div', {css:'sixteen wide column'})
								.makeNode('table', 'table', {
									clearCSS:true,
									css:'ui single line table',
									columns:{
										name:'Name',
										dueDate:'Due Date',
										amount:'Amount',
										balance:'Balance',
										view:''
									}
								});

						_.each(ret, function(inv){

							dom.tableCont.cont.table.makeRow('row-'+inv.id, [
								inv.name,
								inv.due_date,
								'$'+(inv.amount/100).formatMoney(2),
								'$'+(inv.balance/100).formatMoney(2),
								''
							]);

							dom.tableCont.cont.table.body['row-'+inv.id].view.makeNode('btn', 'div', {css:'ui blue button', text:'View'})
								.notify('click', {
									type:'invoicesRun',
									data:{
										run:function(inv, dom){

											singleInvoice(inv, dom, true, function(dom){

												dom.dom.patch();

												dom.after(dom.dom);

											});

										}.bind({}, inv, dom)
									}
								}, sb.moduleId);

						});

						dom.patch();

					}

				});

			}else{

				comps.table.notify({
					type: 'show-table',
					data: crudSetup
				});

			}

		}

		this.state.show = buildTable.bind(this);

	}

	function totalInvoices(invoiceList){

		var total = 0;

		_.each(invoiceList, function(inv){

			total += inv.amount;

		});

		return total;

	}

	function totalPrice(priceArray){

		var total = 0;

		_.each(priceArray, function(v, k){

			total += v;

		});

		return total;

	}

	function invoicesDashboard(dom, state, draw){

		var startRange = moment().startOf('day'),
		endRange = moment().endOf('day');

		function displayNav(){

			this.makeNode('header', 'headerText', {text: 'Invoices', size: 'x-small'});
			this.makeNode('date', 'form', {date:{type:'text',name:'date', label:''}});

			this.patch();

			var dateRangeDom = this.date.selector;

			$(dateRangeDom).daterangepicker({
			    "showDropdowns": true,
			    "ranges": {
			        "Last 7 Days": [moment().subtract(7, 'day').startOf('day'), moment().endOf('day')],
			        "Last 30 Days": [moment().subtract(30, 'day').startOf('day'), moment().endOf('day')],
			    },
			    "alwaysShowCalendars": false,
			    "startDate":moment(startRange).subtract('days', 7),
			    "endDate":moment(endRange).add('days', 7),
				"opens":"right"
			}, function(start, end, label) {
				$(dateRangeDom +' span').html(start.format('MM-DD-YY') + ' - ' + end.format('MM-DD-YY'));
			});

			$(dateRangeDom).on('apply.daterangepicker', function(ev, picker) {

				sb.notify({
					type:'invoicesRun',
					data: {
						run: function(){

						var data = {
							startRange:picker.startDate,
							endRange:picker.endDate
							}


						}
					}
				});

			});

		}
		function displayCount(){

			function getCount(callback){

				var ret = {};
				callback(ret);

			}

			this.makeNode('totunp', 'container', {css: 'pda-container pda-background-gray'});
			this.totunp.makeNode('header1', 'headerText', {text: 'Total Unpaid: ', size: 'small'});
			this.makeNode('sp1', 'lineBreak', {spaces: 1});

			this.makeNode('totpaid', 'container', {css: 'pda-container pda-background-gray'});
			this.totpaid.makeNode('header2', 'headerText', {text: 'Total Paid: ', size: 'small'});
			this.makeNode('sp2', 'lineBreak', {spaces: 1});

			this.makeNode('pastdue', 'container', {css: 'pda-container pda-background-gray'});
			this.pastdue.makeNode('header3', 'headerText', {text: 'Total Past Due: ', size: 'small'});
			this.makeNode('sp3', 'lineBreak', {spaces: 1});

			this.patch();

			getCount(function(data){

				sb.data.getSum


			}.bind(this));

		}
		function displayPayments(){

			this.makeNode('loader', 'loader', {size: 'small'});

			this.patch();

			if(comps.hasOwnProperty('payDash')){
				comps.payDash.destroy();
				delete comps.payDash;
			}

			var tableSetup = {
				domObj: this,
				tableTitle: 'Recent Payments',
				objectType: 'payments',
				rowlink: false,
				searchObjects: false,
				navigation: false,
				headerButtons: {},
				visibleCols: {
					disp: ''
				},
				feedUI: {
					action: function(ui, obj){

						domObj = sb.dom.make(ui.selector);

						domObj.makeNode('cont', 'container', {});
						domObj.cont.makeNode('header', 'headerText', {text: `<label class="label label-primary">$ ${(obj.amount/100).formatMoney(2)}</label> to invoice No. 12345`, size: 'x-small', style: 'display: inline-block;'});
						domObj.cont.makeNode('view', 'button', {text:'<i class="fa fa-external-link" aria-hidden="true"></i>', css:'pda-align-right pda-btn-medium pda-btnOutline-blue pda-transparent'})
						.notify('click', {
							type: 'invoicesRun',
							data: {
								run: function(){

									singleInvoice(obj, dom, backButton, draw);

								}
							}
						}, sb.moduleId);
						domObj.makeNode('info', 'headerText', {text:'<small>Payment made on</small> '+ moment(obj.date_created).format('M/D/YYYY h:mm a'), size:'xx-small'})
						domObj.makeNode('sp', 'lineBreak', {spaces: 2});
						domObj.build();
					}
				},
				data:function(page, callback){

					page.sortCol = 'amount';
					page.sortDir = 'asc';

					sb.data.db.obj.getAll('payments', function(data){

						callback(data);

					}, 3, page);

				}
			}

			comps.payDash = sb.createComponent('crud-table');
			comps.payDash.notify({
				type: 'show-table',
				data:tableSetup
			});

		}

		var navUI = dom.makeNode('navigation', 'container', {css:'pda-container pda-Panel pda-panel-gray'}).makeNode('body', 'container', {});

		dom.makeNode('sp', 'lineBreak', {});

		dom.makeNode('charts', 'container', {css: 'pda-container'});

		var countUI = dom.charts.makeNode('chart1', 'column', {css:'pda-container', width: 7}).makeNode('body', 'container', {css: 'pda-container'});

		dom.charts.makeNode('chart2', 'column', {css:'pda-container pda-background-gray pda-Panel pda-panel-gray', width: 5}).makeNode('body', 'container', {css: 'pda-container'});

		dom.charts.chart2.makeNode('header', 'headerText', {text: 'Recent Payments', size: 'x-small'});

		var paymentsUI = dom.charts.chart2.makeNode('payment', 'column', {css:'pda-container'});

		dom.charts.chart2.payment.makeNode('test', 'text', {text: 'First Payment'});

		navUI.display = displayNav.bind(navUI);
		countUI.display = displayCount.bind(countUI);
		paymentsUI.display = displayPayments.bind(paymentsUI);

		dom.patch();

		navUI.display();
		countUI.display();
		paymentsUI.display();

		draw(false);

	}

	function surchargeSetup(dom, bp){

		function getChartOfAccounts(coaCompId, callback) {

			if(coaCompId.chart_of_accounts_company){

				sb.data.db.obj.getAll('chart_of_accounts_companies', function(coaComps){

					sb.data.db.obj.getWhere('chart_of_accounts', {chart_of_accounts_company: coaCompId.chart_of_accounts_company}, function(accounts){

						callback({
							coaComps:coaComps,
							accounts:accounts
						});

					});

				});

			}else{

				sb.data.db.obj.getAll('chart_of_accounts_companies', function(coaComps){

					if(coaComps.length > 0){

						sb.data.db.obj.getWhere('chart_of_accounts', {chart_of_accounts_company:coaComps[0].id}, function(accounts){

							callback({
								coaComps:coaComps,
								accounts:accounts
							});

						});

					}else{

						callback({
							coaComps:coaComps,
							accounts:[]
						});

					}

				});

			}

		}

		function processRate(db, rate){

			var formData = this.form.process(),
				taxRateObj = {};
			var domObj = this;

			if(formData.completed){

				if(+formData.fields.chart_of_account.value != 0){

					if(!_.isNaN(+formData.fields.rate.value)){

						if(rate){
							taxRateObj.id = rate.id;
						}

						taxRateObj.name = formData.fields.name.value,
						taxRateObj.note = formData.fields.note.value;
						taxRateObj.chart_of_account = formData.fields.chart_of_account.value;
						taxRateObj.rate = +formData.fields.rate.value*100;

						sb.data.db.obj[db]('surcharges', taxRateObj, function(done){

							var dom = this;

							sb.dom.alerts.alert('Success', 'Tax Rate Saved', 'success');

							setTimeout(function(){

								surchargeSetup.call({}, dom, bp);

							}, 500);


						}.bind(domObj));

					}else{

						sb.dom.alerts.alert('Warning', 'Please enter a number for Rate %', 'warning');

					}

				}else{

					sb.dom.alerts.alert('Warning', 'Please select a Chart of Account', 'warning');

				}

			}else{

				sb.dom.alerts.alert('Warning', 'Please fill out entire form', 'warning');

			}

		}

		function rateCreateEdit(coa, rate){

			var dom = this;
			var coaID = {};
			var formArgs = {
					name: {
						type: 'text',
						label: 'Name',
						name: 'name'
					},
					note: {
						type: 'text',
						label: 'Note',
						name: 'note'
					},
					chart_of_account_company: {
						name: 'chart_of_account_company',
						type: 'select',
						label: 'Chart Of Account Company',
						options: [],
						change: update_CoAFormField.bind(this, dom)
					},
					chart_of_account: {
						name: 'chart_of_account',
						type: 'select',
						label: 'Chart Of Account',
						options: []
					},
					rate: {
						type: 'number',
						label: 'Surcharge Rate %',
						name: 'rate',
						placeholder: 'Ex. 7.25%'
					}
				},
				ceHeader = 'Create a new Surcharge',
				dbCall = 'create';

			if(!_.isEmpty(rate)){

				formArgs.chart_of_account_company.value = rate.chart_of_account.chart_of_accounts_company;
				formArgs.chart_of_account.value = rate.chart_of_account.id;
				coaID = rate.chart_of_account;
			}

			if(!_.isEmpty(coa)){

				_.each(coa, function(c){

					var cObj = {
						name: c.name,
						value: c.id
					}

					if(rate && rate.chart_of_account.id == c.id){
						cObj.selected = true;
					}

					formArgs.chart_of_account.options.push(cObj);

				});

			}

			if(rate){
				formArgs.name.value = rate.name;
				formArgs.note.value = rate.note;
				formArgs.rate.value = +rate.rate/100;
				dbCall = 'update';
				ceHeader = 'Edit Tax Rate';
			}



			getChartOfAccounts(coaID, function(coaInfo) {

				formArgs.chart_of_account_company.options = _.map(_.sortBy(coaInfo.coaComps, 'name'), function(company) {

					return {
						name: company.name,
						value: company.id
					};

				});

				formArgs.chart_of_account.options = _.map(_.sortBy(coaInfo.accounts, 'name'), function(company) {

					return {
						name:company.name,
						value:company.id
					};

				});

				dom.empty();
				dom.makeNode('header', 'container', {});
				dom.header.makeNode('title', 'column', {width:6});
				dom.header.title.makeNode('header', 'headerText', {text: ceHeader, size: 'small'});
				dom.header.makeNode('btns', 'column', {width:6});
				dom.header.btns.makeNode('btnGroup', 'buttonGroup', {css: 'pull-right'});
				dom.header.btns.btnGroup.makeNode('back', 'button', {css: 'pda-btnOutline-red', text: 'Back'}).notify('click', {
					type: 'invoicesRun',
					data: {
						run: surchargeSetup.bind({}, dom, bp)
					}
				}, sb.moduleId);

				if(rate){
					dom.header.btns.btnGroup.makeNode('delete', 'button', {css: 'pda-btn-red', text: 'Delete'}).notify('click', {
						type: 'invoicesRun',
						data: {
							run: function(r, bp){

								var domObj = this;

								sb.data.db.obj.erase('tax_rate', r.id, function(ret){

									if(ret){

										sb.dom.alerts.alert('Success', 'Tax Rate Deleted', 'success');

										setTimeout(function(){

											sb.notify({
												type:'app-redraw',
												data:{}
											});

											taxRateSetup.call({}, domObj, bp);

										}, 500);

									}

								});

							}.bind(dom, rate, bp)
						}
					}, sb.moduleId);
				}

				dom.header.btns.btnGroup.makeNode('save', 'button', {css: 'pda-btn-green', text: 'Save'});
				dom.makeNode('sp', 'lineBreak', {});
				dom.makeNode('form', 'form', formArgs);

				dom.header.btns.btnGroup.save.notify('click', {
					type: 'invoicesRun',
					data: {
						run: processRate.bind(dom, dbCall, rate)
					}
				}, sb.moduleId);

				dom.patch();

			});

		}

		function getCOA(callback, rateObj){

			var domObj = this;

			sb.data.db.obj.getAll('chart_of_accounts', function(ret){

				callback.call(domObj, ret, rateObj);

			});
		}

		function update_CoAFormField(dom, form, value) {

			sb.data.db.obj.getWhere('chart_of_accounts', {chart_of_accounts_company:+value}, function(accounts) {

				dom.form.chart_of_account.update({
					options: _.map(accounts, function(company) {

						return {
							name: company.name,
							value: company.id
						};

					})
				});

			});

		}

		dom.empty();
		dom.makeNode('btnGroup', 'buttonGroup', {});
		dom.btnGroup.makeNode('new', 'button', {css: 'ui green button', text: '<i class="fa fa-plus"></i> New Surcharge'}).notify('click', {
			type: 'invoicesRun',
			data: {
				run: getCOA.bind(dom, rateCreateEdit)
			}
		}, sb.moduleId);
		dom.makeNode('btnsSpacer', 'lineBreak', {spaces:2});

		sb.data.db.obj.getAll('surcharges', function(rates){

			dom.makeNode('cont', 'container', {css: 'pda-container'});
			dom.makeNode('table', 'table',
				{
					css: 'table-hover table-condensed',
					columns: {
						btns: '',
						name: 'Name',
						note: 'Note',
						rate: 'Surcharge Rate',
						chart_of_account: 'Chart of Account'
					}
				});

			_.each(rates, function(r){

				dom.table.makeRow('type-'+r.id, ['','','']);
				dom.table.body['type-'+r.id].btns.makeNode('edit', 'button', {css: 'pda-btn-orange', text: '<i class="fa fa-pencil"></i> Edit'}).notify('click', {
					type: 'invoicesRun',
					data: {
						run: getCOA.bind(dom, rateCreateEdit, r)
					}
				}, sb.moduleId);
				dom.table.body['type-'+r.id].name.makeNode('name', 'text', {text: r.name, size: 'x-small'});
				dom.table.body['type-'+r.id].note.makeNode('note', 'text', {text: r.note, size: 'x-small'});
				dom.table.body['type-'+r.id].rate.makeNode('rate', 'text', {text: `${r.rate/100} %`, size: 'x-small'});
				dom.table.body['type-'+r.id].chart_of_account.makeNode('coa', 'text', {text: r.chart_of_account.name, size: 'x-small'});

			});

			dom.patch();

		},1);

	}

	function taxRateSetup(dom, bp){

		function getChartOfAccounts(coaCompId, callback) {

			if(coaCompId.chart_of_accounts_company){

				sb.data.db.obj.getAll('chart_of_accounts_companies', function(coaComps){

					sb.data.db.obj.getWhere('chart_of_accounts', {chart_of_accounts_company: coaCompId.chart_of_accounts_company}, function(accounts){

						callback({
							coaComps:coaComps,
							accounts:accounts
						});

					});

				});

			}else{

				sb.data.db.obj.getAll('chart_of_accounts_companies', function(coaComps){

					if(coaComps.length > 0){

						sb.data.db.obj.getWhere('chart_of_accounts', {chart_of_accounts_company:coaComps[0].id}, function(accounts){

							callback({
								coaComps:coaComps,
								accounts:accounts
							});

						});

					}else{

						callback({
							coaComps:coaComps,
							accounts:[]
						});

					}

				});

			}

		}

		function processRate(db, rate){

			var formData = this.form.process(),
				taxRateObj = {};
			var domObj = this;

			if(formData.completed){

				if(+formData.fields.chart_of_account.value != 0){

					if(!_.isNaN(+formData.fields.rate.value)){

						if(rate){
							taxRateObj.id = rate.id;
						}

						taxRateObj.name = formData.fields.name.value,
						taxRateObj.note = formData.fields.note.value;
						taxRateObj.chart_of_account = formData.fields.chart_of_account.value;
						taxRateObj.rate = +formData.fields.rate.value*100;

						sb.data.db.obj[db]('tax_rates', taxRateObj, function(done){

							var dom = this;

							sb.dom.alerts.alert('Success', 'Tax Rate Saved', 'success');

							setTimeout(function(){

								taxRateSetup.call({}, dom, bp);

							}, 500);


						}.bind(domObj));

					}else{

						sb.dom.alerts.alert('Warning', 'Please enter a number for Rate %', 'warning');

					}

				}else{

					sb.dom.alerts.alert('Warning', 'Please select a Chart of Account', 'warning');

				}

			}else{

				sb.dom.alerts.alert('Warning', 'Please fill out entire form', 'warning');

			}

		}

		function rateCreateEdit(coa, rate){

			var dom = this;
			var coaID = {};
			var formArgs = {
					name: {
						type: 'text',
						label: 'Name',
						name: 'name'
					},
					note: {
						type: 'text',
						label: 'Note',
						name: 'note'
					},
					chart_of_account_company: {
						name: 'chart_of_account_company',
						type: 'select',
						label: 'Chart Of Account Company',
						options: [],
						change: update_CoAFormField.bind(this, dom)
					},
					chart_of_account: {
						name: 'chart_of_account',
						type: 'select',
						label: 'Chart Of Account',
						options: []
					},
					rate: {
						type: 'number',
						label: 'Tax Rate %',
						name: 'rate',
						placeholder: 'Ex. 7.25%'
					}
				},
				ceHeader = 'Create a new Tax Rate',
				dbCall = 'create';

			if(!_.isEmpty(rate)){

				if(rate.chart_of_account){

					formArgs.chart_of_account_company.value = rate.chart_of_account.chart_of_accounts_company;
					formArgs.chart_of_account.value = rate.chart_of_account.id;
					coaID = rate.chart_of_account;

				}

			}

			if(!_.isEmpty(coa)){

				_.each(coa, function(c){

					var cObj = {
							name: c.name,
							value: c.id
						}

					if(rate){

						if(rate.chart_of_account){

							cObj.selected = true;

						}

					}

					formArgs.chart_of_account.options.push(cObj);

				});

			}

			if(rate){
				formArgs.name.value = rate.name;
				formArgs.note.value = rate.note;
				formArgs.rate.value = +rate.rate/100;
				dbCall = 'update';
				ceHeader = 'Edit Tax Rate';
			}

			getChartOfAccounts(coaID, function(coaInfo) {

				formArgs.chart_of_account_company.options = _.map(_.sortBy(coaInfo.coaComps, 'name'), function(company) {

					return {
						name: company.name,
						value: company.id
					};

				});

				formArgs.chart_of_account.options = _.map(_.sortBy(coaInfo.accounts, 'name'), function(company) {

					return {
						name:company.name,
						value:company.id
					};

				});

				dom.empty();
				dom.makeNode('header', 'container', {});
				dom.header.makeNode('title', 'column', {width:6});
				dom.header.title.makeNode('header', 'headerText', {text: ceHeader, size: 'small'});
				dom.header.makeNode('btns', 'column', {width:6});
				dom.header.btns.makeNode('btnGroup', 'buttonGroup', {css: 'pull-right'});
				dom.header.btns.btnGroup.makeNode('back', 'button', {css: 'pda-btnOutline-red', text: 'Back'}).notify('click', {
					type: 'invoicesRun',
					data: {
						run: taxRateSetup.bind({}, dom, bp)
					}
				}, sb.moduleId);

				if(rate){
					dom.header.btns.btnGroup.makeNode('delete', 'button', {css: 'pda-btn-red', text: 'Delete'}).notify('click', {
						type: 'invoicesRun',
						data: {
							run: function(r, bp){

								var domObj = this;

								sb.data.db.obj.erase('tax_rate', r.id, function(ret){

									if(ret){

										sb.dom.alerts.alert('Success', 'Tax Rate Deleted', 'success');

										setTimeout(function(){

											sb.notify({
												type:'app-redraw',
												data:{}
											});

											taxRateSetup.call({}, domObj, bp);

										}, 500);

									}

								});

							}.bind(dom, rate, bp)
						}
					}, sb.moduleId);
				}

				dom.header.btns.btnGroup.makeNode('save', 'button', {css: 'pda-btn-green', text: 'Save'});
				dom.makeNode('sp', 'lineBreak', {});
				dom.makeNode('form', 'form', formArgs);

				dom.header.btns.btnGroup.save.notify('click', {
					type: 'invoicesRun',
					data: {
						run: processRate.bind(dom, dbCall, rate)
					}
				}, sb.moduleId);

				dom.patch();

			});

		}

		function getCOA(callback, rateObj){

			var domObj = this;

			sb.data.db.obj.getAll('chart_of_accounts', function(ret){

				callback.call(domObj, ret, rateObj);

			});
		}

		function update_CoAFormField(dom, form, value) {

			sb.data.db.obj.getWhere('chart_of_accounts', {chart_of_accounts_company:+value}, function(accounts) {

				dom.form.chart_of_account.update({
					options: _.map(accounts, function(company) {

						return {
							name: company.name,
							value: company.id
						};

					})
				});

			});

		}

		dom.empty();
		dom.makeNode('btnGroup', 'buttonGroup', {});
		dom.btnGroup.makeNode('new', 'button', {css: 'ui green button', text: '<i class="fa fa-plus"></i> New Tax Rate'}).notify('click', {
			type: 'invoicesRun',
			data: {
				run: getCOA.bind(dom, rateCreateEdit)
			}
		}, sb.moduleId);
		dom.makeNode('btnsSpacer', 'lineBreak', {spaces:2});

		sb.data.db.obj.getAll('tax_rates', function(rates){

			dom.makeNode('cont', 'container', {css: 'pda-container'});
			dom.makeNode('table', 'table',
				{
					css: 'table-hover table-condensed',
					columns: {
						btns: '',
						name: 'Name',
						note: 'Note',
						rate: 'Tax Rate',
						chart_of_account: 'Chart of Account'
					}
				});

			_.each(rates, function(r){

				dom.table.makeRow('type-'+r.id, ['','','']);
				dom.table.body['type-'+r.id].btns.makeNode('edit', 'button', {css: 'pda-btn-orange', text: '<i class="fa fa-pencil"></i> Edit'}).notify('click', {
					type: 'invoicesRun',
					data: {
						run: getCOA.bind(dom, rateCreateEdit, r)
					}
				}, sb.moduleId);
				dom.table.body['type-'+r.id].name.makeNode('name', 'text', {text: r.name, size: 'x-small'});
				dom.table.body['type-'+r.id].note.makeNode('note', 'text', {text: r.note, size: 'x-small'});
				dom.table.body['type-'+r.id].rate.makeNode('rate', 'text', {text: `${r.rate/100} %`, size: 'x-small'});

				if(r.chart_of_account){
					dom.table.body['type-'+r.id].chart_of_account.makeNode('coa', 'text', {text: r.chart_of_account.name, size: 'x-small'});
				}

			});

			dom.patch();

		},1);

	}

	///Various Reports
	function invoiceReport(dom, options, draw){

		var header = '';
		var icon = '';

		if(options.hasOwnProperty('header')){
			header = options.header;
		}
		if(options.hasOwnProperty('icon')){
			icon = options.icon.type;
		}

		dom.empty();
		dom.makeNode('sp', 'lineBreak', {spaces: 1});
		dom.makeNode('header', 'div', {css: 'ui dividing header', text:header});

		draw({
			dom:dom,
			after:function(dom){

				sb.notify({
					type:'show-collection',
					data:{
						domObj:dom,
						actions:{
							downloadCSV:true
						},
						objectType:'invoices',
						fields:{
							id: {
								title:'Project id',
								view:function(ui, obj){

									var ret = 'Not set';

									if ( (obj.related_object || {}).main_object ) {
										ret = '#'+obj.related_object.main_object.object_uid;
									}

									if(ui){
										ui.makeNode('data','div',{text:ret});
									}

									return ret;
								}
							},
							start_date:{
								title: 'Project Start Date',
								view:function(ui, obj){

									var ret = 'Not set';

									if ( (obj.related_object || {}).main_object){
										ret = moment(obj.related_object.main_object.start_date).local().format('M/D/YYYY');
									}

									if(ui){
										ui.makeNode('data','div',{text:ret});
									}

									return ret;

								}
							},
							name:{
								title:'Project Name',
								view:function(ui,obj){

									var ret = '';
									var contactLink;
									if(obj){


										if ( (obj.related_object || {}).main_object ) {

											ret = obj.related_object.main_object.name;

											contactLink = sb.data.url.createPageURL('object-view', {
												id: 					obj.related_object.main_object.id
												, name: 				obj.related_object.main_object.name
												, object_bp_type: 		'groups'
												, type: 				'project'
											});
										}

									}

									if(ui && contactLink){
										var text = '<span class="ui text" style="white-space:normal"><b>'+ret+'</b></span>';
										ui.makeNode('data', 'div', {
											text: text,
											tag:'a',
											href:contactLink
										});
									}else{
										return ret;
									}

								}
							},
							invoice_name: {
								title:'Invoice Name',
								view:function(ui, obj){

									var ret = 'Not set';
									if(obj.name) {
										ret = obj.name;
									}

									if(ui){
										var text = '<span class="ui text" style="white-space:normal">'+ret+'</span>';
										ui.makeNode('invoice_name','div',{text:text});
									}

									return ret;

								}
							},
							due_date:{
								title:'Due Date',
								rangeOver:true,
								view:function(ui, obj){

									var ret = '';
									if(obj.due_date){
										ret = moment(obj.due_date).local().format('M/D/YYYY')
									}

									if(ui){
										ui.makeNode('data','div',{text:ret});
									}

									return ret;

								}
							},
							amount:{
								title:'Invoice Total',
								view:function(ui, obj){

									var ret = '$'+ (obj.amount/100).formatMoney();
									if(!_.isUndefined(ui) && !_.isNull(ui))
										ui.makeNode('total', 'div', {text: ret});

									return ret;

								}
							},
							payments:{
								title:'Total Payments',
								view:function(ui, obj){

									var totalNumberPayments = 0;
									var paymentTotal = 0;

									ret = '';

									if(obj.hasOwnProperty('payments')){

										_.map(obj.payments, function(paym){

											if(paym.amount != 0)
												totalNumberPayments++;

											return paymentTotal += paym.amount;

										});

										ret = '$'+ (paymentTotal/100).formatMoney();

										if(!_.isUndefined(ui) && !_.isNull(ui))
											ui.makeNode('msg', 'div', {text:ret});

									}

									return ret;

								}
							},
							balance:{
								title:'Invoice Balance',
								view:function(ui, invoice){

									var balanceTotal = 0;
									var paymentTotal = 0;

									var bal = 0;
									if(!_.isNull(invoice.amount) && !_.isUndefined(invoice.amount)){
										bal = invoice.amount;
									}

									balanceTotal += bal;

									if(invoice.hasOwnProperty('payments') && !_.isEmpty(invoice.payments)){

										_.map(invoice.payments, function(paym){
											// Only include payments that are not marked as returned and not detached
											if(!Number.isNaN(paym.amount) && paym.status !== 'Returned' && paym.invoice !== 0){
												paymentTotal += paym.amount;
											}


										});

									}

									if(!_.isUndefined(ui) && !_.isNull(ui)){
										ui.makeNode('balance', 'div', {text:'$'+((balanceTotal-paymentTotal)/100).formatMoney()});
									}

									return '$'+((balanceTotal-paymentTotal)/100).formatMoney();
								}
							},
							main_contact:{
								title:'Contact',
								view:function(ui,obj){

									var ret = 'No contact selected';
									var url = '';
									//if(obj.main_contact){
									if ((obj.related_object || {}).main_object) {
										ret = obj.related_object.main_object.main_contact.fname +' '+ obj.related_object.main_object.main_contact.lname;
										url = sb.data.url.createPageURL('object', {
											id: 					obj.related_object.main_object.main_contact.id
											, name: 				obj.related_object.main_object.main_contact.fname +' '+ obj.related_object.main_object.main_contact.lname
											, object_bp_type: 		'contacts'
											, type: 				'contacts'
										});
									}

									if(ui){
										ui.makeNode('data', 'div', {text: ret, href:url, tag:'a', css:'ui blue text', style:'text-decoration:underline;'});
									}else{
										return ret;
									}

								}
							},
							end_date:{
								title:'Project End Date',
								isHidden: true,
								view:function(ui, obj){

									var ret = 'Not set';
									if(obj.related_object){
										if(obj.related_object.main_object.end_date){
											ret = moment(obj.related_object.main_object.end_date).local().format('M/D/YYYY');
										}
									}

									if(ui){
										ui.makeNode('data','div',{text:ret});
									}

									return ret;

								}
							},
							company:{
								title:'Company',
								isHidden: true,
								view:function(ui,obj){

									var ret = 'No company selected';
									if(obj.main_contact){
										if(obj.main_contact.company){
											ret = obj.main_contact.company.name;
											url = sb.data.url.createPageURL('object', {
												id: 					obj.main_contact.company.id
												, name: 				obj.main_contact.company.name
												, type: 				'companies'
											});
										}
									}
									if(ui){
										ui.makeNode('data', 'div', {text: ret, href:url, tag:'a', css:'ui blue text', style:'text-decoration:underline;'});
									}else{
										return ret;
									}

								}
							},
						},
						groupings:{},
						metrics:{
							sum:{
								title:'Total',
								fields:{
									amount:function(ui, calculated, rawValue, data){

										var totalAmount = 0;

										if(_.isArray(data)){

											_.each(data, function(inv){

												totalAmount += inv.amount;

											});

										}else{

											totalAmount = data;

										}


										if(!_.isUndefined(ui) && !_.isNull(ui)){

											ui.empty();
											ui.makeNode('total', 'div', {css: 'left aligned', text:'<b>$'+ (totalAmount/100).formatMoney() +'</b>'});
											ui.patch();

										}


										return '$'+ (totalAmount/100).formatMoney();

									},
									payments:function(ui, calculated, rawValue, data){

										var paymentTotal = 0;

										if(_.isArray(data)){

											if(!_.isEmpty(data)){
												_.map(data, function(invoice){

													if(invoice.hasOwnProperty('payments') && !_.isEmpty(invoice.payments)){

														_.map(invoice.payments, function(paym){

															if(!Number.isNaN(paym.amount)){
																paymentTotal += paym.amount;
															}


														});

													}

												});
											}

										} else {

											paymentTotal = data;

										}


										if(!_.isUndefined(ui) && !_.isNull(ui)){

											ui.empty();
											ui.makeNode('msg', 'div', {css:'left aligned', text:'<b>$'+ (paymentTotal/100).formatMoney()+'</b>'});
											ui.patch();

										}


										return '$'+ (paymentTotal/100).formatMoney();

									},
									balance:function(ui, calculated, rawValue, data){

										var balanceTotal = 0;
										var paymentTotal = 0;

										if(_.isArray(data)){

											if(!_.isEmpty(data)){

												_.map(data, function(invoice){

													var bal = 0;
													if(!_.isNull(invoice.amount) && !_.isUndefined(invoice.amount)){
														bal = invoice.amount;
													}

													balanceTotal += bal;

													if(invoice.hasOwnProperty('payments') && !_.isEmpty(invoice.payments)){

														_.map(invoice.payments, function(paym){

															if(!Number.isNaN(paym.amount)){
																paymentTotal += paym.amount;
															}


														});

													}

												});

											}
										} else {

											balanceTotal = data;
										}


										if(!_.isUndefined(ui) && !_.isNull(ui)){

											ui.empty();
											ui.makeNode('balance', 'div', {css: 'left aligned', text:'<b>$'+ ((balanceTotal-paymentTotal)/100).formatMoney()+'</b>'});
											ui.patch();

										}

										return '$'+ ((balanceTotal-paymentTotal)/100).formatMoney();

									}
								}
							}
						},
						rangeOver: 'due_date',
						sortCol: 'due_date',
						sortDir: 'asc',
						where:{

						}
						, query: function(data, callback) {

							//callback(data);
							sb.data.db.service("InvoicesService", "getInvoicesForReports", {data}, function (res) {

								data.data = res.data;
								data.recordsFiltered = res.recordsFiltered;
								data.recordsTotal = res.recordsTotal;

								callback(data);

							});
						}
					}
				});

			}
		});

	}

	function accountsAgingReport(dom, options){

		var header = '';

		function display_payments(ui, obj) {

			ui.makeNode('wrapper', 'div', {});

			ui.wrapper.makeNode('modal', 'modal', {

				onShow: function() {

					ui.wrapper.modal.body.empty();

					var collectionsSetup = {
						domObj: ui.wrapper.modal.body
						, fields: {
							id: {
								title: 'Id'
								, type: 'number'
							}
							, date: {
								title:'Date'
								, view: function(ui, obj) {
									if (obj.date) {
										ui.makeNode('date', 'div', {
											text: moment(obj.date).format('YYYY-MM-DD HH:mm:ss.SS')
										});
									} else {
										ui.makeNode('date', 'div', {text:'-'});
									}
									ui.patch();
								}
							}
							, amount: {
								title: 'Amount'
								, type: 'usd'
								, view: function(ui, obj) {
									ui.makeNode('value', 'div', {
										text: '$' + (obj.amount/100).formatMoney()
									});
									ui.patch();
								}
							}
							, fee: {
								title: 'Fee'
								, type: 'usd'
								, view: function(ui, obj) {
									ui.makeNode('value', 'div', {
										text: '$' + (obj.fee/100).formatMoney()
									});
									ui.patch();
								}
							}
							, stripe_payment_id: {
								title: 'Stripe Payment Id'
								, type: 'text'
								, view: function (ui, obj) {
									if (obj.stripe_payment_id !== '') {
										sb.notify({
											type: 'view-field',
											data: {
												type: 'text',
												property: 'stripe_payment_id',
												ui: dom,
												obj: obj,
												options: {}
											}
										});
									} else {
										ui.makeNode('stripe_payment_id', 'div', {text:'-'});
									}
								}
							}
							, checkNumber: {
								title: 'Check Number'
								, type: 'number'
								, view: function (ui, obj) {
									if (obj.checkNumber > 0) {
										sb.notify({
											type: 'view-field',
											data: {
												type: 'number',
												property: 'checkNumber',
												ui: dom,
												obj: obj,
												options: {}
											}
										});
									} else {
										ui.makeNode('checkNumber', 'div', {text:'-'});
									}
								}
							}
						}
						, objectType: ''
						, actions: {}
						, selectedView: 'table'
						, menu: {
							subviews: false
						}
						, subviews: {
							table: {
								hideSelectionBoxes:true
							}
						}
						, data: {
							obj: obj
						}
					};

					sb.notify({
						type:'show-collection',
						data: collectionsSetup
					});

					ui.wrapper.modal.body.patch();

				}
			});

			ui.patch();

			ui.wrapper.modal.show();

		}

		function createAgingText(bucket) {

			var totalAmount = ' --- ';

			if (bucket?.count && bucket?.balance) {

				if (bucket.balance > 0) {

					totalAmount = '$' + (bucket.balance/100).formatMoney();

				} else {

					totalAmount = '$' + 0;

				}

				if (bucket.count > 0 && bucket.balance > 0) {

					totalAmount = '('+ bucket.count +') '+ totalAmount;

				}

			}

			return totalAmount;

		}

		if(options.hasOwnProperty('header')){

			header = options.header;

		}

		if(options.hasOwnProperty('icon')){

			icon = options.icon.type;

		}

		dom.empty();

		dom.makeNode('sp', 'lineBreak', {spaces: 1});

		dom.makeNode('header', 'div', {css: 'ui dividing header', text:header});

		var discountsSetup = {
				domObj: dom
				, actions:{
					downloadCSV: true
				}
				, selectedView: 'table'
				, objectType: 'groups'
				, menu: {
					subviews: false
				}
				, subviews: {
					table: {
						hideSelectionBoxes: true
						, hideRowActions: true
					}
				}
				, fields:{
					name:{
						title:'Project Name',
						type: 'title',
						isSearchable: true
					},
					main_contact:{
						title:'Main Contact',
						type: 'users',
						isSearchable: true
					},
					zeroThirty:{
						title: 	'0 - 30 Days',
						type: 	'title',
						view: 	function(ui, obj){
							ui.empty();
							var text = createAgingText(obj.zeroThirty);
							ui.makeNode('amount', 'div', {css: 'right aligned', text: text});
							ui.patch();
						}
					},
					thirtySixty:{
						title: 	'31 - 60 Days',
						type: 	'title',
						view: 	function(ui, obj){
							ui.empty();
							var text = createAgingText(obj.thirtySixty);
							ui.makeNode('amount', 'div', {css: 'right aligned', text: text});
							ui.patch();
						}
					},
					sixtyNinety:{
						title: 	'61 - 90 Days',
						type: 	'title',
						view: 	function(ui, obj){
							ui.empty();
							var text = createAgingText(obj.sixtyNinety);
							ui.makeNode('amount', 'div', {css: 'right aligned', text: text});
							ui.patch();
						}
					},
					ninetyonePlus:{
						title: 	'91+ Days',
						type: 	'title',
						view: 	function(ui, obj){
							ui.empty();
							var text = createAgingText(obj.ninetyOnePlus);
							ui.makeNode('amount', 'div', {css: 'right aligned', text: text});
							ui.patch();
						}
					},
					payments:{
						title: 	'Payments',
						type: 	'title',
						view: 	function(ui, obj){
							ui.empty();

							if (obj.payments?.length > 0) {

								ui.makeNode('payments', 'div', {css: 'center aligned'});

								ui.makeNode('payments_button', 'div', {
									tag: 'button',
									css:'ui fluid teal button',
									text: obj.payments.length + ' Payments'
								}).notify('click', {
									type:'contactComponent-run',
									data: {
											run: function(data) {
												display_payments(data.ui, data.obj.payments);
											}
											, ui: ui
											, obj: obj
										},
									}, sb.moduleId);

							} else {

								ui.makeNode('payments', 'div', {
									tag: 'button',
									css: 'ui fluid button disabled',
									text: 'No Payments'
								});

							}

							ui.patch();

						}
					},
					totals:{
						title: 	'Total Balance',
						type: 	'title',
						view: 	function(ui, obj){
							ui.empty();
							var text = createAgingText(obj.totals);
							ui.makeNode('amount', 'div', {css: 'right aligned', text: text});
							ui.patch();
						}
					}
				}
				, parseData: function(data, callback) {

					function merge_project_data(project_data, data) {

						for (const [i, project] of data.entries()) {

							let newData = _.find(project_data, p => p.id === project.id);

							if (newData) {

								data[i] = {
									...newData,
									...project
								}

							}

						}

						return data;

					}

					let proposals = [];

					data?.data?.forEach( (project) => {

						if (project.proposal?.id) proposals.push(project.proposal.id);

					});

					if (proposals.length) {

						getAgingBuckets(proposals, 'proposalsArray', (buckets) => {

							if (!(_.isEmpty(buckets.project_data))) {

								data.data = merge_project_data(buckets.project_data, data.data);

							}

							callback(data);

						});

					} else {

						callback(data);

					}

				}
				, where: {
					group_type: 'Project'
					, childObjs: {
						name: true,
						main_contact: {
							id: true
							, fname: true
							, lname: true
						},
						description: true,
						state: true,
						group_type: true,
						type: {
							name:true,
							states:true
						},
						invoice_value: true,
						start_date: true,
						end_date: true,
						last_updated: true,
						proposal: {
							invoices: {
								balance: true
								, due_date: true
								, payments: true
							}
						}
					}
					, state: {
						type: 'or'
						, values: [2, 3, 8]
					}
				    , type: 1625566
				}
			};

		sb.notify({
			type:'show-collection',
			data: discountsSetup
		});

	}

	function accountsAgingReport_old(dom, options, draw){

		var header = '';
		var icon = '';
		var select = {
			zeroThirty:{
				start: 		moment().clone().subtract(30, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss.SS'),
				end: 		moment().clone().subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss.SS'),
				startUnix: 	moment().clone().subtract(30, 'days').unix(),
				endUnix: 	moment().clone().subtract(1, 'days').unix()
			},
			thirtySixty:{
				start: 		moment().clone().subtract(60, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss.SS'),
				end: 		moment().clone().subtract(31, 'days').format('YYYY-MM-DD HH:mm:ss.SS'),
				startUnix: 	moment().clone().subtract(60, 'days').unix(),
				endUnix: 	moment().clone().subtract(31, 'days').unix()
			},
			sixtyNinety:{
				start: 		moment().clone().subtract(90, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss.SS'),
				end: 		moment().clone().subtract(61, 'days').format('YYYY-MM-DD HH:mm:ss.SS'),
				startUnix: 	moment().clone().subtract(90, 'days').unix(),
				endUnix: 	moment().clone().subtract(61, 'days').unix()
			},
			ninetyonePlus:{
				start: 		moment().clone().subtract(900, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss.SS'),
				end: 		moment().clone().subtract(91, 'days').format('YYYY-MM-DD HH:mm:ss.SS'),
				startUnix: 	moment().clone().subtract(900, 'days').unix(),
				endUnix: 	moment().clone().subtract(91, 'days').unix()
			}
		};

		function parse_contact_data(data, callback, query){

			if (_.isEmpty(data)) {

				callback({ data: [] });

			} else {

				sb.data.db.obj.getGroupSum('invoices', 'balance', {
					balance: {
						type: 'greater_than'
						, value: 0
					},
					groupOn:'main_contact',
					groupBy:'month',
					sumCast:'int',
					dateRange:{
						start:select.zeroThirty.start,
						end:select.zeroThirty.end
					},
					dateField:'due_date'
				}, function(data_zeroThirty){
					sb.data.db.obj.getGroupSum('invoices', 'balance', {
						balance: {
							type: 'greater_than'
							, value: 0
						},
						groupOn:'main_contact',
						groupBy:'month',
						sumCast:'int',
						dateRange:{
							start:select.thirtySixty.start,
							end:select.thirtySixty.end
						},
						dateField:'due_date'
					}, function(data_thirtySixty){

						sb.data.db.obj.getGroupSum('invoices', 'balance', {
							balance: {
								type: 'greater_than'
								, value: 0
							},
							groupOn:'main_contact',
							groupBy:'month',
							sumCast:'int',
							dateRange:{
								start:select.sixtyNinety.start,
								end:select.sixtyNinety.end
							},
							dateField:'due_date'
						}, function(data_sixtyNinety){

							sb.data.db.obj.getGroupSum('invoices', 'balance', {
								balance: {
									type: 'greater_than'
									, value: 0
								},
								groupOn:'main_contact',
								groupBy:'month',
								sumCast:'int',
								dateRange:{
									start:select.ninetyonePlus.start,
									end:select.ninetyonePlus.end
								},
								dateField:'due_date'
							}, function(data_NinetyPlus){

								updContact = [];

								var zeroThirty = _.reduce(_.clone(data.data), function(mem, con, i){

									var parsed = _.findWhere(data_zeroThirty, {grouped:con.id.toString()})

									con.parsed_data = {};

									if (parsed) {

										con.parsed_data.zeroThirty = parsed;
										mem.push(con);

									} else {

										con.parsed_data.zeroThirty = {
											grouped: 0
											, grouped_total: 0
											, sum: 0
											, avg: 0
										};

									}

									return mem;

								}, []);

								var thirtySixty = _.reduce(_.clone(data.data), function(mem, con, i){

									var parsed = _.findWhere(data_thirtySixty, {grouped:con.id.toString()})

									if (parsed) {

										if(con.hasOwnProperty('parsed_data'))
											con.parsed_data.thirtySixty = parsed;

										mem.push(con);

									} else {

										con.parsed_data.thirtySixty = {
											grouped: 0
											, grouped_total: 0
											, sum: 0
											, avg: 0
										};

									}

									return mem;

								}, []);

								var sixtyNinety = _.reduce(_.clone(data.data), function(mem, con, i){

									var parsed = _.findWhere(data_sixtyNinety, {grouped:con.id.toString()})

									if (parsed) {

										con.parsed_data.sixtyNinety = parsed;

									} else {

										con.parsed_data.sixtyNinety = {
											grouped: 0
											, grouped_total: 0
											, sum: 0
											, avg: 0
										};

									}

									mem.push(con);

									return mem;

								}, []);

								var ninetyPlus = _.reduce(_.clone(data.data), function(mem, con, i){

									var parsed = _.findWhere(data_NinetyPlus, {grouped:con.id.toString()})

									if (parsed) {

										con.parsed_data.ninetyPlus = parsed;

									} else {

										con.parsed_data.ninetyPlus = {
											grouped: 0
											, grouped_total: 0
											, sum: 0
											, avg: 0
										};

									}

									mem.push(con);

									return mem;

								}, []);

								data.data = _.chain(updContact)
											.union(zeroThirty)
											.union(thirtySixty)
											.union(sixtyNinety)
											.filter(function(item){

												var addItem = true;
												var count = 0;

												_.mapObject(item.parsed_data, function( val, key ){

													if (val['grouped_total'] == 0) {
														count++;
													}

												});

												if ( count == _.keys(item.parsed_data).length ) {
													addItem = false;
												}

												if ( addItem )
													return item;

											})
											.uniq()
											.value();

								callback(data);

							});

						})

					});

				});

			}

		}

		if(options.hasOwnProperty('header')){
			header = options.header;
		}
		if(options.hasOwnProperty('icon')){
			icon = options.icon.type;
		}

		dom.empty();
		dom.makeNode('sp', 'lineBreak', {spaces: 1});
		dom.makeNode('header', 'div', {css: 'ui dividing header', text:header});


		draw({
			dom:dom,
			after:function(dom){

				sb.notify({
					type:'show-collection',
					data:{
						domObj:dom,
						actions:{
							downloadCSV: true
						},
						fields:{
							fname:{
								title:'Name',
								type:'title',
								view:function(ui, obj){

									if (ui) {

/*
										contactLink = sb.data.url.createPageURL('object', {
											id: 					obj.id
											, name: 				obj.name
											, object_bp_type: 		'contacts'
											, type: 				'contacts'
										});
*/

/*
										var wh = {
												main_contact: obj.id,
												due_date: {
													type:'after',
													value:moment().add(30, 'days').unix()
												}
											};

										var contactLink = sb.data.url.createPageURL(
										  	'hqTool'
											, {
												tool: 'invoiceHQTool'
												, params: {
														type: obj.id
														, where: encodeURIComponent(JSON.stringify(wh))
													}
												}
											);
*/

										ui.makeNode('name', 'div'
											, {
												css: 'ui header'
												//, tag: 'a'
												, text:obj.fname +' '+ obj.lname
												//, href: contactLink
											}
										).notify('click', {
											type:'invoicesRun',
											data:{
												run:function(){

														dom.empty();

														dom.makeNode('back', 'div', {css:'ui teal button', text:'Back to Accounts Aging Report'})
															.notify('click', {
																type:'invoicesRun',
																data:{
																	run:function(){

																		accountsAgingReport(dom, options, draw);

																	}
																}
															}, sb.modulId);

														dom.makeNode('break','div',{text:'<br />'});

														dom.makeNode('table', 'div', {});

														dom.patch();

														sb.notify({
															type: 'show-collection',
															data: {
																domObj:dom.table,
																layer: 'hq',
																objectType:'invoices',
																actions:{
																	view:true,
																	downloadCSV: true,
																	statement:{
																		name:'Statement',
																		title:'Statement',
																		headerAction:true,
																		icon:'file alternate outline',
																		action:function(selected, ui, onComplete, collectionSettings){

																			build_statement(
																				selected,
																				ui,
																				{
																					startRange:moment().startOf('year').format(),
																					endRange:moment().format()
																				},
																				function(done){


																				}
																			);

																		}
																	}
																},
																templates: false,
																fields:{
																	name:{
																		title:'Name',
																		view:function(ui, obj){

																			contactLink = sb.data.url.createPageURL('object-view', {
																				id: 					obj.related_object.main_object.id
																				, name: 				obj.related_object.main_object.name
																				, object_bp_type: 		'groups'
																				, type: 				'project'
																			});

																			if(ui){

																				ui.makeNode('data','div',{
																					text:'<b>'+getInvoiceName (obj)+'</b>',
																					tag:'a',
																					href:contactLink
																				});

																			}else{

																				return getInvoiceName (obj);

																			}

																		}
																	},
																	main_contact:{
																		title:'Main Contact',
																		view:function(domObj, object){

																			var ret = 'No contact';

																			if(object.main_contact){

																				ret = object.main_contact.fname +' '+ object.main_contact.lname;

																			}

																			if(domObj){
																				domObj.makeNode('main_contact', 'div', {text: ret});
																			}else{
																				return ret;
																			}


																		}
																	},
																	amount:{
																		title:'Total Amount',
																		view:function(domObj, object){

																			var invoiceTotal = 0;

																			_.each(object.items, function(i){

																				if(i.tax_rate){

																					if(i.tax_rate > 0){

																						invoiceTotal += ((+i.amount * (+i.tax_rate/100)) + +i.amount);

																					}

																				}else{

																					invoiceTotal += +i.amount;

																				}

																			});

																			if(domObj){
																				domObj.makeNode('total_amount', 'div', {text:'$'+ ((object.amount)/100).formatMoney(2)});
																			}else{
																				return (object.amount/100).formatMoney(2);
																			}



																		}
																	},
																	balance:{
																		title:'Balance Due',
																		view:function(domObj, object){

																			var labelColor = '';

																			if(object.balance > 0){

																				labelColor = 'label-warning';

																				if(moment(object.due_date) < moment()){

																					labelColor = 'label-danger';

																				}

																			}else{

																				labelColor = 'label-success';

																			}

																			if(domObj){
																				domObj.makeNode('balance', 'div', {text:'<h4><span class="label '+ labelColor +'">$'+ (object.balance/100).formatMoney(2) +'</span></h4>'});
																			}else{
																				return (object.balance/100).formatMoney(2);
																			}


																		}
																	},
																	due_date:{
																		title:'Due Date',
																		view:function(ui, obj){

																			if(ui){
																				ui.makeNode('data','div',{text:moment(obj.due_date).format('MM/DD/YYYY')});
																			}else{
																				return moment(obj.due_date).format('MM/DD/YYYY');
																			}

																		}
																	}

																},
																metrics: {
																	sum:{
																		title:'Total'
																		, fields:{
																			amount: {
																				view: function(ui, totalAmount, rawValue){

																					if (
																						!_.isUndefined(ui)
																						&& !_.isNull(ui)
																					) {

																						ui.empty();
																						ui.makeNode(
																							'total'
																							, 'div'
																							, {
																								css: 'ui right aligned'
																								, text:'<strong>$'+ (totalAmount/100).formatMoney() + '</strong>'
																							}
																						);
																						ui.patch();

																					}

																					// If this is a list
																					if (Array.isArray(totalAmount)) {

																						var ret = _.reduce(totalAmount, function (memo, obj) {

																							return memo + parseInt(obj.amount);

																						}, 0);

																						return '$'+ (ret/100).formatMoney();

																					} else {

																						return (rawValue) ? totalAmount : '$'+ (totalAmount/100).formatMoney();

																					}

																				}
																			}
																			, balance: {
																				view: function(ui, totalAmount, rawValue){

																					if (
																						!_.isUndefined(ui)
																						&& !_.isNull(ui)
																					) {

																						ui.empty();
																						ui.makeNode(
																							'total'
																							, 'div'
																							, {
																								css: 'ui right aligned'
																								, text:'<strong>$'+ (totalAmount/100).formatMoney() + '</strong>'
																							}
																						);
																						ui.patch();

																					}

																					// If this is a list
																					if (Array.isArray(totalAmount)) {

																						var ret = _.reduce(totalAmount, function (memo, obj) {

																							return memo + parseInt(obj.balance);

																						}, 0);

																						return '$'+ (ret/100).formatMoney();

																					} else {

																						return (rawValue) ? totalAmount : '$'+ (totalAmount/100).formatMoney();

																					}

																				}
																			}
																			, due_date: {
																				view: function(ui, totalAmount, rawValue){

																					if (
																						!_.isUndefined(ui)
																						&& !_.isNull(ui)
																					) {

																						ui.empty();
/*
																						ui.makeNode(
																							'total'
																							, 'div'
																							, {
																								css: 'ui right aligned'
																								, text:'<strong>$'+ (totalAmount/100).formatMoney() + '</strong>'
																							}
																						);
*/
																						ui.patch();

																					}

																					// If this is a list
																					if (Array.isArray(totalAmount)) {

																						var ret = _.reduce(totalAmount, function (memo, obj) {

																							return memo + 0;

																						}, 0);

																						return '';

																					} else {

																						return '';

																					}

																				}
																			}
																		}
																	}
																},
																fullView:{
																	id:'invoiceHQTool',
																	type:'hqTool'
																},
																groupings:false,
																singleView:{
																	view:function(ui, obj, draw){

																		singleInvoice(obj, ui, {}, draw);

																	},
																	select:2
																},
																where:{
																	main_contact:obj.id,
																	due_date:{
																		type:'before',
																		date:moment().unix()
																	},
																	childObjs:{
																		name:true,
																		main_contact:true,
																		amount:true,
																		balance:true,
																		due_date:true,
																		related_object:{
																			main_object:{
																				name:true
																			}
																		}
																	}
																}
															}
														});

													}

												}
											}, sb.moduleId);

										ui.patch();

									} else {

										return obj.fname +' '+ obj.lname;

									}

								},
								isSearchable: true
							},
							lname:{
								title:'Last Name',
								type:'title',
								isSearchable: true,
								isHidden: true
							},
							zeroThirty:{
								title: 	'0 - 30 Days',
								type: 	'title',
								view: 	function(ui, obj){

									var totalAmount = ' --- ';


									if (ui) {

										if (obj.parsed_data.zeroThirty.grouped_total > 0) {
											totalAmount = '('+ obj.parsed_data.zeroThirty.grouped_total +') $'+ (obj.parsed_data.zeroThirty.sum/100).formatMoney();
										}

										ui.makeNode('amount', 'div', {css: 'right aligned', text: totalAmount});
										ui.patch();

									} else {

										if (obj.parsed_data.zeroThirty.grouped_total > 0) {
											totalAmount = (obj.parsed_data.zeroThirty.sum/100).formatMoney();
										}else{
											totalAmount = 0;
										}

										return totalAmount;

									}

								}
							},
							thirtySixty:{
								title: 	'31 - 60 Days',
								type: 	'title',
								view: 	function(ui, obj){

									var totalAmount = ' --- ';
									if (obj.parsed_data.thirtySixty.grouped_total > 0) {
										totalAmount = '('+ obj.parsed_data.thirtySixty.grouped_total +') $'+ (obj.parsed_data.thirtySixty.sum/100).formatMoney();
									}

									if (ui) {

										ui.makeNode('amount', 'div', {css: 'right aligned', text: totalAmount});
										ui.patch();

									} else {

										return totalAmount;

									}

								}
							},
							sixtyNinety:{
								title: 	'61 - 90 Days',
								type: 	'title',
								view: 	function(ui, obj){

									var totalAmount = ' --- ';
									if (obj.parsed_data.sixtyNinety.grouped_total > 0) {
										totalAmount = '('+ obj.parsed_data.sixtyNinety.grouped_total +') $'+ (obj.parsed_data.sixtyNinety.sum/100).formatMoney();
									}

									if (ui) {

										ui.makeNode('amount', 'div', {css: 'right aligned', text: totalAmount});
										ui.patch();

									} else {

										return totalAmount;

									}

								}
							},
							ninetyonePlus:{
								title: 	'91+ Days',
								type: 	'title',
								view: 	function(ui, obj){

									var totalAmount = ' --- ';
									if (obj.parsed_data.ninetyPlus.grouped_total > 0) {
										totalAmount = '('+ obj.parsed_data.ninetyPlus.grouped_total +') $'+ (obj.parsed_data.ninetyPlus.sum/100).formatMoney();
									}

									if (ui) {

										ui.makeNode('amount', 'div', {css: 'right aligned', text: totalAmount});
										ui.patch();

									} else {

										return totalAmount;

									}

								}
							},
							total:{
								title: 	'Total Amount',
								type: 	'title',
								view: 	function(ui, obj){

									var totalSum = 0;
									var totalCount = 0;

									_.mapObject(obj.parsed_data, function(set){

										totalSum += parseInt(set.sum);
										totalCount += parseInt(set.grouped_total);

									});

									var totalAmount = ' --- ';
									if (totalCount) {
										totalAmount = '('+ totalCount +') $'+ (totalSum/100).formatMoney();
									}

									if (ui) {

										ui.makeNode('amount', 'div', {css: 'right aligned', text: totalAmount});
										ui.patch();

									} else {

										return totalAmount;

									}

								}
							}
						},
						parseData:function(data, callback, query){

							parse_contact_data(data, callback, query);

						},
						groupings:{
							state:'State'
						},
						hideDateRange: true,
						metrics: {
							sum:{
								title:'Total'
								, fields:{
									zeroThirty: {
										view: function(ui, totalAmount, rawValue){

											if (
												!_.isUndefined(ui)
												&& !_.isNull(ui)
											) {

												ui.empty();
												ui.makeNode(
													'total'
													, 'div'
													, {
														css: 'ui right aligned'
														, text:'<strong>$'+ (totalAmount/100).formatMoney() + '</strong>'
													}
												);
												ui.patch();

											}

											// If this is a list
											if (Array.isArray(totalAmount)) {

												var ret = _.reduce(totalAmount, function (memo, obj) {

													return memo + parseInt(obj.parsed_data.zeroThirty.sum);

												}, 0);

												return '$'+ (ret/100).formatMoney();

											} else {

												return (rawValue) ? totalAmount : '$'+ (totalAmount/100).formatMoney();

											}

										}
										, sum: function (objType, field, queryObj, callback, options) {

											sb.data.db.obj.sum('invoices', 'balance', {
												balance: {
													type: 'greater_than'
													, value: 0
												},
												main_contact: {
													type: 'greater_than'
													, value: 0
												},
												due_date:{
													type: 'between',
													start: 	select.zeroThirty.startUnix,
													end: 	select.zeroThirty.endUnix
												},
											}, function(data_zeroThirty){

												callback(data_zeroThirty);

											});

										}
									}
									, thirtySixty: {
										view: function(ui, totalAmount, rawValue){

											if (
												!_.isUndefined(ui)
												&& !_.isNull(ui)
											) {

												ui.empty();
												ui.makeNode(
													'total'
													, 'div'
													, {
														css: 'ui right aligned'
														, text:'<strong>$'+ (totalAmount/100).formatMoney() + '</strong>'
													}
												);
												ui.patch();

											}

											// If this is a list
											if (Array.isArray(totalAmount)) {

												var ret = _.reduce(totalAmount, function (memo, obj) {

													return memo + parseInt(obj.parsed_data.thirtySixty.sum);

												}, 0);

												return '$'+ (ret/100).formatMoney();

											} else {

												return (rawValue) ? totalAmount : '$'+ (totalAmount/100).formatMoney();

											}

										}
										, sum: function (objType, field, queryObj, callback, options) {

											sb.data.db.obj.sum('invoices', 'balance', {
												due_date:{
													type: 'between',
													start: 	select.thirtySixty.startUnix,
													end: 	select.thirtySixty.endUnix
												},
											}, function(data_zeroThirty){

												callback(data_zeroThirty);

											});

										}
									}
									, sixtyNinety: {
										view: function(ui, totalAmount, rawValue){

											if (
												!_.isUndefined(ui)
												&& !_.isNull(ui)
											) {

												ui.empty();
												ui.makeNode(
													'total'
													, 'div'
													, {
														css: 'ui right aligned'
														, text:'<strong>$'+ (totalAmount/100).formatMoney() + '</strong>'
													}
												);
												ui.patch();

											}

											// If this is a list
											if (Array.isArray(totalAmount)) {

												var ret = _.reduce(totalAmount, function (memo, obj) {

													return memo + parseInt(obj.parsed_data.sixtyNinety.sum);

												}, 0);

												return '$'+ (ret/100).formatMoney();

											} else {

												return (rawValue) ? totalAmount : '$'+ (totalAmount/100).formatMoney();

											}

										}
										, sum: function (objType, field, queryObj, callback, options) {

											sb.data.db.obj.sum('invoices', 'balance', {
												due_date:{
													type: 'between',
													start: 	select.sixtyNinety.startUnix,
													end: 	select.sixtyNinety.endUnix
												},
											}, function(data_zeroThirty){

												callback(data_zeroThirty);

											});

										}
									}
									, ninetyonePlus: {
										view: function(ui, totalAmount, rawValue){

											if (
												!_.isUndefined(ui)
												&& !_.isNull(ui)
											) {

												ui.empty();
												ui.makeNode(
													'total'
													, 'div'
													, {
														css: 'ui right aligned'
														, text:'<strong>$'+ (totalAmount/100).formatMoney() + '</strong>'
													}
												);
												ui.patch();

											}

											// If this is a list
											if (Array.isArray(totalAmount)) {

												var ret = _.reduce(totalAmount, function (memo, obj) {

													return memo + parseInt(obj.parsed_data.ninetyPlus.sum);

												}, 0);

												return '$'+ (ret/100).formatMoney();

											} else {

												return (rawValue) ? totalAmount : '$'+ (totalAmount/100).formatMoney();

											}

										}
										, sum: function (objType, field, queryObj, callback, options) {

											sb.data.db.obj.sum('invoices', 'balance', {
												due_date:{
													type: 'between',
													start: 	select.ninetyonePlus.startUnix,
													end: 	select.ninetyonePlus.endUnix
												},
											}, function(data_zeroThirty){

												callback(data_zeroThirty);

											});

										}
									}
									, total: {
										view: function(ui, totalAmount, rawValue){

											if (
												!_.isUndefined(ui)
												&& !_.isNull(ui)
											) {

												ui.empty();
												ui.makeNode(
													'total'
													, 'div'
													, {
														css: 'ui right aligned'
														, text:'<strong>$'+ (totalAmount/100).formatMoney() + '</strong>'
													}
												);
												ui.patch();

											}

											// If this is a list
											if (Array.isArray(totalAmount)) {

												var ret = _.reduce(totalAmount, function (memo, obj) {

													memo += parseInt(obj.parsed_data.zeroThirty.sum);
													memo += parseInt(obj.parsed_data.thirtySixty.sum);
													memo += parseInt(obj.parsed_data.ninetyPlus.sum);

													return memo + parseInt(obj.parsed_data.sixtyNinety.sum);

												}, 0);

												return '$'+ (ret/100).formatMoney();

											} else {

												return (rawValue) ? totalAmount : '$'+ (totalAmount/100).formatMoney();

											}

										}
										, sum: function (objType, field, queryObj, callback, options) {

											sb.data.db.obj.sum('invoices', 'balance', {
												due_date:{
													type: 'between',
													start: 	select.ninetyonePlus.startUnix,
													end: 	select.zeroThirty.endUnix
												}
											}, function(data_zeroThirty){

												callback(data_zeroThirty);

											});

										}
									}
								}
							}
						},
						objectType:'contacts',
						pageLength:100,
						where:{
							childObjs:{
								fname:true,
								lname:true,
								type:{
									name:true,
									states:true
								},
								company:{
									id:true,
									name:true
								},
								state:true,
								contact_info:{
									city:true,
									country:true,
									state:true,
									street:true,
									street2:true,
									zip:true,
									info:true,
									is_primary:true,
									name:true,
									title:true,
									type:1
								}
							}
						}
					}
				});

			}
		});

	}

	function chartOfAccountsReport (dom, state, draw) {

		var collectionsBody = undefined;
		var collectionsData = [];
		var aggWhere = {};

		if(state.hasOwnProperty('project')
			&& state.project !== undefined) {

			aggWhere.menu = state.project.proposal.menu;

		}

		dom.makeNode('wrapper', 'div', {});

		dom.wrapper.makeNode('head', 'div', {});
		dom.wrapper.makeNode('body', 'div', {});
		dom.patch();

		sb.data.db.obj.getAll(
			'chart_of_accounts'
			, function (accts) {

				sb.notify({
					type: 'show-collection'
					, data: {
						actions: {
							create:		false
							, downloadPDF: true
						},
						domObj: dom,
						state: state,
						selectedView: 'table',
						objectType:'chart_of_accounts',
						templates: false,
						fields: {
							account_id: {
								title: 'Account Id'
								, type: 'plain-text'
							}
							, name: {
								title:'Name'
								, options: {
									style: 'font-weight: normal !important;'
								}
							}
							, amount: {
								title: 		'Amount'
								, type: 	'text'
								, view: 	function (ui, obj) {

									ui.makeNode(
										't'
										, 'div'
										, {
											text: '$'+ (obj._agg.sum/100).toFixed(2)
											, css: 'pull-right'
										}
									);

								}
							}
							, chart_of_accounts_company: {
								isSearchable: true
								, isHidden: true
							}
							, tags: {
								isHidden: true
								, view: function () { return false; }
							}
							, date_of_use: {
								type: 'date'
								, isHidden: true
								, rangeOver: true
							}
						},
						filterBy: {
							owner: {
								title: 'Managers'
								, getOptions: 	function (callback) {

									sb.data.db.obj.getAll(
										'users'
										, function (opts) {

											callback(opts, true);

										}
										, {
											id: true
											, fname: true
											, lname: true
											, name: true
										}
									);


								}
								, parseSelection: function (selection, options) {

									options.where.childObjs._agg.owner = parseInt(selection);
									if (!options.where.childObjs._agg.owner) {
										delete options.where.childObjs._agg.owner;
									}

								}
							}
						},
						groupings: {
							type: 'Type',
							main_client: 'chart_of_accounts_company'
						},
						metrics: {
							sum:{
								title:'Total'
								, fields:{
									amount: {
										view: function(ui, totalAmount, rawValue){

											if (
												!_.isUndefined(ui)
												&& !_.isNull(ui)
											) {

												ui.empty();
												ui.makeNode(
													'total'
													, 'div'
													, {
														css: 'ui right aligned'
														, text:'<strong>$'+ (totalAmount/100).formatMoney() + '</strong>'
													}
												);
												ui.patch();

											}

											return (rawValue) ? totalAmount : '$'+ (totalAmount/100).formatMoney();

										}
										, sum: function (objType, field, queryObj, callback, options) {

											if (queryObj.chart_of_accounts_company) {

												queryObj.coa = {
													type: 'or'
													, values: _.chain(accts)
																.where({
																	chart_of_accounts_company: queryObj.chart_of_accounts_company
																})
																.pluck('id')
																.value()
												};

												delete queryObj.chart_of_accounts_company;

											}

											if (options.where.childObjs._agg.owner) {
												queryObj.owner = options.where.childObjs._agg.owner;
											}

											if(state.hasOwnProperty('project')
												&& state.project !== undefined) {
												queryObj.menu = state.project.proposal.menu;
											}

											sb.data.db.obj.sum(
												'inventory_menu_line_item'
												, 'absolute_price'
												, queryObj
												, function(resp){

													callback(resp);

												}
											);
										}
									}
								}
							}
						},
						menu: {
							subviews: false
						},
						subviews: {
							table: {
								groupBy: {
									defaultTo: 'chart_of_accounts_company'
								}
								, showDateField: true
								, hideSelectionBoxes:true
								, hideRowActions:  true
								, range: {
									defaultTo: 	'range'
									, not: 		['all_time']
								}
							}
							, timeRangeOptions:{
								reports:true
								, hideRowActions:  true
							}
						},
						where: {
							childObjs: {
								name: true
								, chart_of_accounts_company: true
								, account_id: true
								, _agg: {
									field: 			'absolute_price'
									, objectType: 	'inventory_menu_line_item'
									, groupOn:		'coa'
		// 							, groupBy:		'month'
									, sumCast:		'real'
// 									, dateField:	'date_created'
									, dateField:	'date_of_use'
									, where: 		aggWhere
									/*
		, dateRange:{
										start:select.zeroThirty.start
										, end:select.zeroThirty.end
									}
		*/
								}
							}
						}
					}
				});

			}
			, {
				id: true
				, chart_of_accounts_company: 'id'
			}
		);

	}

	// Can be used for any tool type
	function build_statement(selected, ui, state, callback){

		ui.makeNode('top', 'div', {css:''})
			.makeNode('btns', 'div', {css:'ui mini compact buttons'});
		//ui.makeNode('topBreak', 'div', {text:'<br />'});

		ui.top.btns.makeNode('pdf', 'div', {css:'ui teal button pdf-statement-download', text:'Download PDF'})
			.notify('click', {
				type:'invoicesRun',
				data:{
					run:function(){

						sb.data.makePDF($('#invoice-container').html(), 'D');

					}
				}
			}, sb.moduleId);

		ui.top.btns.makeNode('statement', 'div', {css:'ui teal disabled button', text:'View Statement'})
			.notify('click', {
				type:'invoicesRun',
				data:{
					run:function(){

						$(ui.top.btns.payments.selector).removeClass('disabled');
						$(ui.top.btns.statement.selector).addClass('disabled');

						sb.data.db.obj.getWhere(
							'invoices'
							, {
								id: {
									type: 'or'
									, values: _.pluck(selected, 'id')
								}
								, childObjs: {
									name: 		true
									, due_date: true
									, amount: 	true
									, paid: 	true
									, balance: 	true
									, payments: true
									, object_uid: true
									, main_contact: true
									, main_client: true
									// The proposal
									, related_object: {
										// The project
										main_object: {
											name: 			true
											, details: 		true
											, description: 	true
										}
									}
								}
							}, function(invoices){

							sb.data.db.obj.getAll('invoice_system', function(invoiceSystem){

								sb.data.db.obj.getWhere('company_logo', {is_primary:"yes", childObjs:1}, function(logo){

									var statementHTML = createMergedInvoiceHeaderHTML({
											invoiceSystem:invoiceSystem[0],
											invoices:invoices,
											client:state.pageObject,
											logo:logo[0]
										});

									ui.bottom.makeNode('header','div', {text:statementHTML});

									ui.bottom.patch();

								});

							}, 1);

						}, null, false, true);

					}
				}
			}, sb.moduleId);

		ui.top.btns.makeNode('payments', 'div', {css:'ui green button', text:'Manage Payments'})
			.notify('click', {
				type:'invoicesRun',
				data:{
					run:function(){

						$(ui.top.btns.payments.selector).addClass('disabled');
						$(ui.top.btns.statement.selector).removeClass('disabled');

						sb.data.db.obj.getWhere(
							'invoices'
							, {
								id: {
									type: 'or'
									, values: _.pluck(selected, 'id')
								}
								, childObjs: {
									name: 		true
									, due_date: true
									, amount: 	true
									, paid: 	true
									, balance: 	true
									, payments: true
									, object_uid: true
									// The proposal
									, related_object: {
										// The project
										main_object: {
											name: 			true
											, details: 		true
											, description: 	true
										}
									}
								}
							}, function(invoices){
// !@TODO show-payment-button
							sb.notify({
								type:'show-payment-button',
								data:{
									dom:ui.bottom.header,
									notification:'invoice-payment-completed',
									admin:true,
									invoices:invoices,
									completeCallback:function(updatedInvoices){

										callback();

									}
								}
							});

						}, null, false, true);

					}
				}
			}, sb.moduleId);

		ui.makeNode('bottom', 'div', {css:'ui basic loading segment'});

		ui.patch();

		sb.data.db.obj.getWhere(
			'invoices'
			, {
				id: {
					type: 'or'
					, values: _.pluck(selected, 'id')
				}
				, childObjs: {
					name: 		true
					, due_date: true
					, amount: 	true
					, paid: 	true
					, balance: 	true
					, payments: true
					, object_uid: true
					, main_contact: true
					, main_client: true
					// The proposal
					, related_object: {
						// The project
						main_object: {
							name: 			true
							, details: 		true
							, description: 	true
						}
					}
				}
			}, function(invoices){

			sb.data.db.obj.getAll('invoice_system', function(invoiceSystem){

				sb.data.db.obj.getWhere('company_logo', {is_primary:"yes", childObjs:1}, function(logo){

					var statementHTML = createMergedInvoiceHeaderHTML({
							invoiceSystem:invoiceSystem[0],
							invoices:invoices,
							client:invoices[0].main_client,
							pageObject:invoices[0].main_contact,
							logo:logo[0],
							startRange:state.startRange,
							endRange:state.endRange
						});

					ui.bottom.makeNode('header','div', {text:statementHTML});

					ui.bottom.patch();

					ui.bottom.loading(false);

					callback();

				});

			}, 1);

		});

	}

	function build_collections(dom, state, options) {

		var collectionsSetup = {
				domObj:dom,
				layer: 'hq',
				state:state,
				objectType:'invoices',
				actions:{
					view:true,
					statement:{
						name:'Statement',
						title:'Statement',
						headerAction:true,
						icon:'file alternate outline',
						action:function(selected, ui, onComplete){

							build_statement(selected, ui, state, onComplete);

						}
					}
				},
				templates: false,
				fields:{
					name:{
						title:'Name'
					},
					main_contact:{
						title:'Main Contact',
						view:function(domObj, object){

							var ret = '<i>No contact</i>';

							if(object.main_contact){

								ret = object.main_contact.fname +' '+ object.main_contact.lname;

							}

							domObj.makeNode('main_contact', 'div', {text: ret});
							domObj.patch();

						}
					},
					type:{
						title:'Type',
						view:function(domObj, object){

							var typeString = '<i class="text-muted">Not set</i>';

							if(object.type !== ''){

								typeString = object.type;

							}

							domObj.makeNode('type', 'div', {text: typeString});
							domObj.patch();

						}
					},
					amount:{
						title:'Total Amount',
						view:function(domObj, object){

							var invoiceTotal = 0;

							_.each(object.items, function(i){

								if(i.tax_rate){

									if(i.tax_rate > 0){

										invoiceTotal += ((+i.amount * (+i.tax_rate/100)) + +i.amount);

									}

								}else{

									invoiceTotal += +i.amount;

								}

							});

							domObj.makeNode('total_amount', 'div', {text:'$'+ (Math.round(object.amount)/100).formatMoney(2)});
							domObj.patch();

						}
					},
					balance:{
						title:'Balance Due',
						view:function(domObj, object){

							var labelColor = '';

							if(object.balance > 0){

								labelColor = 'label-warning';

								if(moment(object.due_date) < moment()){

									labelColor = 'label-danger';

								}

							}else{

								labelColor = 'label-success';

							}

							domObj.makeNode('balance', 'div', {text:'<h4><span class="label '+ labelColor +'">$'+ Math.round(object.balance/100).formatMoney(2) +'</span></h4>'});
							domObj.patch();

						}
					},
					due_date:{
						title:'Due Date'
					}

				},
				fullView:{
					id:'invoiceHQTool',
					type:'hqTool'
				},
				groupings:false,
				singleView:{
					view:function(ui, obj, draw){

						singleInvoice(obj, ui, {}, draw);

					},
					select:2
				},
				state:state,
				where:{
					childObjs:{
						name:true,
						main_contact:true,
						type:true,
						amount:true,
						balance:true,
						due_date:true
					}
				}
			};

		if(options !== undefined) {

			if(options.hasOwnProperty('collections')) {

				_.each(options.collections, function(propVal, propName) {

					collectionsSetup[propName] = propVal;

				});

			}

		}

		if (state.layer) {

			collectionsSetup.layer = state.layer;
			if (state.pageObjectType === 'contacts') {
				collectionsSetup.where.main_contact = state.object.id;
			}

		}

		sb.notify({
			type: 'show-collection',
			data: collectionsSetup
		});

	}

	return {

		destroy: function(){

			_.each(comps, function(comp){
				comp.destroy();
			});

		},

		init: function(){

			comps.table = sb.createComponent('crud-table');
			//comps.emails = sb.createComponent('emails');
// 			comps.menuBuilder = sb.createComponent('inventory');

			if ($(window).width() <= 768) {
				onMobile = true;
			}

			sb.notify({
				type:'register-application',
				data:{
					navigationItem:{
						id:'invoices',
						title:'Invoices',
						icon:'<i class="fa fa-file-text-o"></i>',
						views:[

						/*
							{
								id:'invoices-dashboard',
								type: 'custom',
								default: true,
								title: 'Invoices Dashboard',
								icon: '<i class="fa fa-file-o"></i>',
								dom: invoicesDashboard
							},
*/

							{
								id:'overdue-invoices',
								default: true,
								type:'table',
								title:'Invoices Due',
								icon:'<i class="fa fa-exclamation"></i>',
								setup:{
									objectType:'invoices',
									tableTitle:'<i class="fa fa-exclamation"></i> Invoices Due',
									searchObjects:false,
									filters:false,
									download:false,
									headerButtons:{
										reload:{
											name:'Reload',
											css:'pda-btn-blue',
											action:function(){}
										}
									},
									rowSelection:true,
									rowLink:{
										type:'tab',
										header:function(obj){
											return obj.name;
										},
										action:singleInvoice
									},
									multiSelectButtons:{
										erase:{
											name:'<i class="fa fa-trash-o"></i> Delete',
											css:'pda-btn-red',
											domType:'erase',
											action:'erase'
										}
									},
									visibleCols:{
										name:'Name',
										main_contact:'Contact',
										invoice_type_list:'Type',
										amount:'Total Amount',
										balance:'Balance Due',
										due_date:'Due Date'
									},
									dateRange:'next_service_date',
									cells: {
										date_created:function(obj){
											return moment(obj.date_created).format();
										},
										due_date:function(obj){
											return moment(obj.due_date).format('M/D/YYYY');
										},
										amount:function(obj){

											var invoiceTotal = 0;

											_.each(obj.items, function(i){

												if(i.tax_rate){

													if(i.tax_rate > 0){

														invoiceTotal += ((+i.amount * (+i.tax_rate/100)) + +i.amount);

													}

												}else{

													invoiceTotal += +i.amount;

												}

											});

											return '$'+ (Math.round(obj.amount)/100).formatMoney(2);
											//return '$'+(invoiceTotal/100).formatMoney(2);
										},
										paid:function(obj){

											return '$'+(obj.paid/100).formatMoney(2);
										},
										balance:function(obj){

											var labelColor = '';

											if(obj.balance > 0){

												labelColor = 'label-warning';

												if(moment(obj.due_date) < moment()){

													labelColor = 'label-danger';

												}

											}else{

												labelColor = 'label-success';

											}

											return '<h4><span class="label '+ labelColor +'">$'+ Math.round(obj.balance/100).formatMoney(2) +'</span></h4>';

										},
										main_contact:function(obj){

											var ret = '<i>No contact</i>';

											if(obj.main_contact){
												ret = obj.main_contact.fname +' '+ obj.main_contact.lname;
											}

											return ret;

										}
									},
									childObjs:3,
									data:function(paged, callback){

										sb.data.db.obj.getWhere('invoices', {
											active:'Yes',
											balance:{
												type:'greater_than',
												value:0
											},
											childObjs:4,
											paged:paged
										}, function(ret){

											callback(ret);

										});

									}
								}
							},
							{
								id:'paid-table',
								type:'table',
								title:'Paid Invoices',
								icon:'<i class="fa fa-check"></i>',
								setup:{
									objectType:'invoices',
									tableTitle:'<i class="fa fa-check"></i> Paid Invoices',
									searchObjects:false,
									filters:false,
									download:false,
									headerButtons:{
										reload:{
											name:'Reload',
											css:'pda-btn-blue',
											action:function(){}
										}
									},
									rowSelection:true,
									rowLink:{
										type:'tab',
										header:function(obj){
											return obj.name;
										},
										action:singleInvoice
									},
									multiSelectButtons:{
										erase:{
											name:'<i class="fa fa-trash-o"></i> Delete',
											css:'pda-btn-red',
											domType:'erase',
											action:'erase'
										}
									},
									visibleCols:{
										name:'Name',
										main_contact:'Contact',
										invoice_type_list:'Type',
										amount:'Total Amount',
										balance:'Balance Due',
										due_date:'Due Date'
									},
									dateRange:'next_service_date',
									cells: {
										date_created:function(obj){
											return moment(obj.date_created).format();
										},
										due_date:function(obj){
											return moment(obj.due_date).format('M/D/YYYY');
										},
										amount:function(obj){

											var invoiceTotal = 0;

											_.each(obj.items, function(i){

												if(i.tax_rate){

													if(i.tax_rate > 0){

														invoiceTotal += ((+i.amount * (+i.tax_rate/100)) + +i.amount);

													}

												}else{

													invoiceTotal += +i.amount;

												}

											});

											return '$'+ (Math.round(obj.amount)/100).formatMoney(2);
											//return '$'+(invoiceTotal/100).formatMoney(2);
										},
										paid:function(obj){

											return '$'+(obj.paid/100).formatMoney(2);
										},
										balance:function(obj){

											var labelColor = '';

											if(obj.balance > 0){

												labelColor = 'label-warning';

												if(moment(obj.due_date) < moment()){

													labelColor = 'label-danger';

												}

											}else{

												labelColor = 'label-success';

											}

											return '<h4><span class="label '+ labelColor +'">$'+ Math.round(obj.balance/100).formatMoney(2) +'</span></h4>';

										},
										main_contact:function(obj){

											var ret = '<i>No contact</i>';

											if(obj.main_contact){
												ret = obj.main_contact.fname +' '+ obj.main_contact.lname;
											}

											return ret;

										}
									},
									childObjs:4,
									data:function(paged, callback){

										sb.data.db.obj.getWhere('invoices', {
											active:'Yes',
											balance:{
												type:'less_than',
												value:1
											},
											childObjs:4,
											paged:paged
										}, function(ret){

											callback(ret);

										});

									}
								}
							},
							{
								id:'newInvoice',
								type:'modal',
								title:'New Invoice',
								icon:'<i class="fa fa-plus"></i>',
								color:'green',
								modal:function(modal, state, draw){

									modal.footer.makeNode('btns', 'buttonGroup', {});

									modal.footer.makeNode('button', 'button', {text:'<i class="fa fa-times"></i> Close', css:'pda-btnOutline-red pda-btn-x-small'})
										.notify('click', {
											type:'contactComponent-run',
											data:{
												run:function(){

													this.hide();

												}.bind(modal)
											}
										}, sb.moduleId);

									modal.body.makeNode('break', 'lineBreak', {});

									modal.body.makeNode('header', 'headerText', {text:'You can create a new invoice in a Work Order.', css:'text-center', size:'x-small'});

									modal.body.makeNode('btns', 'buttonGroup', {});

									modal.body.btns.makeNode('search', 'button', {text:'View All Work Orders', css:'pda-btn-primary pda-btn-x-large'})
										.notify('click', {
											type:'contactComponent-run',
											data:{
												run:function(){

													this.hide();

													sb.notify({
														type:'app-navigate-to',
														data:{
															itemId:'workorders',
															viewId:'table'
														}
													});

												}.bind(modal)
											}
										}, sb.moduleId);

									draw(modal);

									//createContactState([], dom, draw);

								}
							},
							{
								id:'settings',
								type:'settings',
								title:'Settings',
								icon:'<i class="fa fa-cog"></i>',
								setup:[

								/*
									{
										object_type:'chart_of_accounts_companies',
										name:'Chart of Accounts',
										action:chartOfAccountSettings
									},
*/

/*
									{
										object_type:'tax_rates',
										name:'5. Tax Rates',
										action: taxRateSetup
									},
									{
										object_type:'surcharges',
										name:'6. Surcharges',
										action: surchargeSetup
									},
*/
									{
										object_type:'stripe',
										name:'3. Banking Info',
										action:bankingSetup
									},
									{
										object_type:'invoice_system',
										name:'2. Billing Address',
										action:billingAddressSetup
									},
									{
										object_type:'schedule_templates',
										name:'7. Payment Schedule Templates',
										action:paymentScheduleSettings
									},
									{
										object_type:'emails',
										name:'1. Invoice Emails',
										action:invoiceEmailsSettings
									},
									{
										object_type:'invoice_types',
										name:'4. Invoice Types',
										action:function(dom, bp){

											dom.empty();

											dom.makeNode('loadingText', 'text', {text:'Loading invoice types...', css:'text-center'});
											dom.makeNode('loader', 'loader', {});

											dom.patch();

											invoiceTypesSetup(dom, {});

										}
									},
									{
										object_type:'company_logo',
										name:'8. Company Invoice Logo'
									}
								]
							},
							{
								id: 'qc-invoices',
								type: 'quickCreate',
								title: 'Invoices',
								icon: '<i class="fa fa-file"></i>',
								dom: function(dom, state, draw){

									dom.makeNode('break', 'lineBreak', {});

									dom.makeNode('header', 'headerText', {text:'You can create a new invoice in a Work Order.', css:'text-center', size:'x-small'});

									dom.makeNode('btns', 'buttonGroup', {});

									dom.btns.makeNode('search', 'button', {text:'View All Work Orders', css:'pda-btn-primary pda-btn-x-large'})
										.notify('click', {
											type:'contactComponent-run',
											data:{
												run:function(){

													this.closeView();

													sb.notify({
														type:'app-navigate-to',
														data:{
															itemId:'workorders',
															viewId:'table'
														}
													});

												}.bind(dom)
											}
										}, sb.moduleId);

									draw(dom);

								}
							},
/*
							{
								id:'invoiceTool',
								type:'tool',
								toolType:'billing',
								default:true,
								name:'Invoices & Items',
								tip:'Send your customer and invoice based on the packlists you create.',
								icon:{
									type: 'dollar sign',
									color: 'teal'
								},
								mainViews:[
									{
										name:'Invoices',
										dom:function(dom, state, draw){

											var object = state.project.proposal;
											var objectId = state.project.proposal.id;
											var mainContactId = 0;
											var objectType = 'projects';

											if(object.main_contact){
												mainContactId = object.main_contact.id;
											}

											dom.makeNode('menuCont', 'container', {title:'ITEMS', subTitle:'Select your billable items.', collapse:true});
											dom.makeNode('invoicesCont', 'container', {title:'INVOICES', subTitle:'Create a billing schedule.', collapse:true});

											dom.menuCont.makeNode('loading', 'div', {css:'ui basic loading segment'});
											dom.invoicesCont.makeNode('loading', 'div', {css:'ui basic loading segment'});

											draw({
												dom:dom,
												after:function(dom){

													dom.invoicesCont.show = function(){

														this.patch();

													}.bind(dom);

													sb.notify({
														type: 'show-menu-builder',
														data: {
															domObj: dom.menuCont,
															object: state.pageObject.proposal,
															menuId: state.pageObject.proposal.menu,
															invoicesDom: dom.invoicesCont,
															pricing:function(updatedPrice){


																price = Object.assign(updatedPrice);

																processUpdatedPricing(dom.invoicesCont, state.pageObject, price, dom.balance);

																sb.data.db.obj.getWhere(['inventory_billable_categories', 'inventory_billable_combination_categories', ''], {
																	id:{
																		type:'or',
																		values:Object.keys(price)
																	}
																}, function(categories){

																	sb.data.db.obj.getWhere('staff_schedules', {event: state.pageObject.proposal.id, active:'Yes'}, function(schedules){

																		//estimateView(dom.cols.right.estimates.cont, obj, price, categories, schedules);


																	});

																});

															},
															isInAccordion:true
														}
													});


												}
											});

										}
									}
								],
								boxViews:[
									{
										dom:function(dom, state, draw){

											sb.data.db.obj.getWhere('invoices', {related_object:state.project.proposal.id, childObjs:1}, function(ret){

												if(ret.length > 0){

													var invoicesTotal = 0;
													var totalPaid = 0;

													_.each(ret, function(invoice){

														totalPaid += invoice.paid;

														_.each(invoice.items, function(item){

															invoicesTotal += (item.amount * item.quantity);

															// add tax
															if(item.tax_rate){

																invoicesTotal += (item.amount * item.quantity) * (item.tax_rate / 100);

															}

														});

													});

													dom.makeNode('total', 'div', {css:'ui huge header', text:'<small><small>Total</small></small><br />$'+ (invoicesTotal/100).formatMoney(2)});

													dom.makeNode('balance', 'div', {css:'ui huge header', text:'<small><small>Balance</small></small><br />$'+ ((invoicesTotal - totalPaid)/100).formatMoney(2)});

													draw(dom);

												}else{

													draw(false);

												}

											});

										}
									}
								]
							},
*/
							// HQ Tool
							{
								id:'invoiceHQTool',
								type:'hqTool',
								icon:{type:'usd', color:'green'},
								name:'Payments & Banking Info',
								tip:'Send your customer and invoice based on the packlists you create.',
								display:false,
								newButton:{
									text:'',
									action:function(){}
								},
								settings:[
									{
										object_type:'stripe',
										name:'1. Banking Info',
										action:bankingSetup
									},
									{
										object_type:'emails',
										name:'2. Due Invoice Emails',
										action:duePaymentEmailSettings
									},
									{
										object_type:'invoice_system',
										name:'3. Billing Address',
										action:billingAddressSetup
									},
									{
										object_type:'invoice_types',
										name:'4. Invoice Types',
										action:function(dom, bp){

											dom.empty();

											dom.makeNode('loadingText', 'text', {text:'Loading invoice types...', css:'text-center'});
											dom.makeNode('loader', 'loader', {});

											dom.patch();

											invoiceTypesSetup(dom, {});

										}
									},
									{
										object_type:'schedule_templates',
										name:'5. Payment Schedule Templates',
										action:paymentScheduleSettings
									},
									{
										object_type:'company_logo',
										name:'6. Company Invoice Logo'
									},
									{
										object_type:'invoice_fees',
										name:'7. Invoice Fees',
										action:inoviceFeeSetup
									}

								],
								mainViews:[
									{
										dom:function(dom, state, draw){

											build_collections(dom, state, undefined);

										}
									}
								]
							},
							// Object Tool
/*
							{
								id: 'invoicesTool',
								type: 'objectTool',
								name: 'Invoices',
								tip:'Send your customer and invoice based on the packlists you create.',
								icon:{
									type:'usd',
									color:'green'
								},
								default:true,
								settings:false,
								mainViews: [
									{
										dom: function(dom, state, draw, mainDom, options) {

											build_collections(dom, state, options);

										}
									}
								]
							},
*/
/*
							{
								id:'invoiceTeamTool',
								type:'teamTool',
								icon:{type:'file alternate', color:'olive'},
								name:'Invoices',
								tip:'Send your customer and invoice based on the packlists you create.',
								default:true,
								newButton:{
									text:'',
									action:function(){}
								},
								mainViews:[
									{
										dom:function(dom, state, draw){

											sb.notify({
												type:'show-collection',
												data:{
													domObj:dom,
													state:state,
													objectType:'invoices',
													actions:{
														view:true
														// need to add a create action?
													},
													fields:{
														name:{
															title:'Name'
														},
														main_contact:{
															title:'Main Contact',
															view:function(domObj, object){

																var ret = '<i>No contact</i>';

																if(object.main_contact){

																	ret = object.main_contact.fname +' '+ object.main_contact.lname;

																}

																domObj.makeNode('main_contact', 'div', {text: ret});
																domObj.patch();

															}
														},
														type:{
															title:'Type',
															view:function(domObj, object){

																var typeString = '<i class="text-muted">Not set</i>';

																if(object.type !== ''){

																	typeString = object.type;

																}

																domObj.makeNode('type', 'div', {text: typeString});
																domObj.patch();

															}
														},
														amount:{
															title:'Total Amount',
															view:function(domObj, object){

																var invoiceTotal = 0;

																_.each(object.items, function(i){

																	if(i.tax_rate){

																		if(i.tax_rate > 0){

																			invoiceTotal += ((+i.amount * (+i.tax_rate/100)) + +i.amount);

																		}

																	}else{

																		invoiceTotal += +i.amount;

																	}

																});

																domObj.makeNode('total_amount', 'div', {text:'$'+ (Math.round(object.amount)/100).formatMoney(2)});
																domObj.patch();

															}
														},
														balance:{
															title:'Balance Due',
															view:function(domObj, object){

																var labelColor = '';

																if(object.balance > 0){

																	labelColor = 'label-warning';

																	if(moment(object.due_date) < moment()){

																		labelColor = 'label-danger';

																	}

																}else{

																	labelColor = 'label-success';

																}

																domObj.makeNode('balance', 'div', {text:'<h4><span class="label '+ labelColor +'">$'+ Math.round(object.balance/100).formatMoney(2) +'</span></h4>'});
																domObj.patch();

															}
														},
														due_date:{
															title:'Due Date'
														}

													},
													fullView:{
														id:'invoiceTeamTool',
														type:'teamTool'
													},
													groupings:false,
													singleView:{
														view:function(ui, obj, draw){

															singleInvoice(obj, ui, {}, draw);

														},
														select:2
													},
													state:state,
													where:{childObjs:{
														name:true,
														main_contact:true,
														type:true,
														amount:true,
														balance:true,
														due_date:true
														}
													}
												}
											});

										}
									}
								]
							},
*/
							// Single object view
							{
								id:'invoices-obj',
								type:'object-view',
								title:'Invoices',
								icon:{type:'file alternate', color:'olive'},
								dom:function(dom, state, draw){

									dom.makeNode('menu', 'div', {css:'ui black inverted menu'});
									dom.menu.makeNode('header', 'div', {text:'Statement', css:'ui header item'});

									dom.makeNode('grid', 'div', {css:'ui stackable grid'});
									dom.grid.makeNode('col1', 'div', {css:'ui three wide column'}).makeNode('seg', 'div', {css:'ui basic segment'});

									dom.grid.col1.seg.makeNode('head' , 'div'
										, {
											text:'<strong>Search Invoices by Due Date</strong>'
											, style: 'margin-left:6px;'
										}
									);

									dom.grid.makeNode('col2', 'div', {css:'ui thirteen wide column'}).makeNode('seg', 'div', {css:'ui secondary segment'});

									dom.grid.col1.seg.makeNode('form', 'form', {
										start:{
											name:'start',
											label:'From:',
											type:'date',
											value:moment().startOf('month')
										},
										end:{
											name:'end',
											label:'To:',
											type:'date',
											value:moment().endOf('month')
										},
										balance:{
											name:'balance',
											label:'Invoices to Show',
											type:'select',
											options:[
												{
													name:'Unpaid',
													label:'unpaid',
													value:'unpaid'
												},
												{
													name:'Paid',
													label:'paid',
													value:'paid'
												},
												{
													name:'All',
													label:'all',
													value:'all'
												}
											]
										}
									});

									dom.grid.col1.seg.makeNode('generateBreak', 'div', {text:'<br />'});

									dom.grid.col1.seg.makeNode('generate', 'div', {css:'ui mini compact teal button', text:'Generate Statement'}).notify('click',{
										type:'invoicesRun',
										data:{
											run:function(){

												dom.grid.col1.seg.generate.loading();

												var formInfo = dom.grid.col1.seg.form.process().fields;
												var where = {};
												var mainClient = 0;

												where.due_date = {
														type:'between',
														start:moment(formInfo.start.value).unix(),
														end:moment(formInfo.end.value).unix()
													};

												if(formInfo.balance.value == 'unpaid'){

													where.balance = {
														type:'greater_than',
														value:'0'
													};

												}

												if(formInfo.balance.value == 'paid'){

													where.balance = 0;

												}


												if(state.pageObject.main_client){

													where.main_client = state.pageObject.main_client.id;

													mainClient = state.pageObject.main_client.id;

												}else{

													where.main_client = {id:state.pageObject.main_contact.company};

													mainClient = {id:state.pageObject.main_contact.company};

												}

												sb.data.db.obj.getWhere('invoices', where, function(invoices){

														sb.data.db.obj.getById('contacts', state.pageObject.main_contact.id, function(contact){

															sb.data.db.obj.getById('companies', mainClient, function(client){

																state.client = client;
																state.pageObject.main_conatct = contact;
																state.startRange = formInfo.start.value;
																state.endRange = formInfo.end.value;

																build_statement(invoices, dom.grid.col2.seg, state, function(){

																	dom.grid.col1.seg.generate.loading(false);

																});

															}, 1);

														}, 1);


													}
												);

											}
										}},
										sb.moduleId
									);

									draw({
										dom:dom,
										after:function(dom){

											var formInfo = dom.grid.col1.seg.form.process().fields;
											var where = {};
											var mainClient = 0;

											where.due_date = {
												type:'between',
												start:moment(formInfo.start.value).unix(),
												end:moment(formInfo.end.value).unix()
											};


											if(formInfo.balance.value == 'unpaid'){

												where.balance = {
													type:'greater_than',
													value:'0'
												};

											}

											if(formInfo.balance.value == 'paid'){

												where.balance = 0;

											}

											if(state.pageObject.main_client){

												where.main_client = state.pageObject.main_client.id;

												mainClient = state.pageObject.main_client.id;

											}else{

												where.main_client = {id:state.pageObject.main_contact.company};

												mainClient = {id:state.pageObject.main_contact.company};

											}

											sb.data.db.obj.getWhere('invoices', where, function(invoices){

													sb.data.db.obj.getById('contacts', state.pageObject.main_contact.id, function(contact){

														sb.data.db.obj.getById('companies', mainClient, function(client){

															state.client = client;
															state.pageObject.main_conatct = contact;
															state.startRange = formInfo.start.value;
															state.endRange = formInfo.end.value;

															build_statement(invoices, dom.grid.col2.seg, state, function(){

																dom.grid.col1.seg.generate.loading(false);

															});

														}, 1);

													}, 1);


												}
											);

										}
									});

								}

							},
							// Invoices report
							{
								id:'invoiceReport',
								type:'custom',
								title:'Invoice Report',
								icon:{
									type:'file alternate',
									color:'olive'
								},
								dom:function(ui, state, draw){

									createInvoicesCollectionUI(
										ui
										, state
										, {
											collections: {
												selectedView:'table'
												, objectType: 'invoices'
												, sortCol: 'due_date'
												, sortDir: 'asc'
												, sortCsv: true
												, actions: {
													create:		false
													, downloadCSV: true
												}
												, fields:{
													event_id: {
														title:'Project id'
														, view: function(ui, obj) {

															if (ui) {

																ui.makeNode('projectId', 'div', {
																	text: '#' + obj.related_object.main_object.object_uid
																});

															} else {

																return obj.related_object.main_object.object_uid;

															}
														}
													}
													, start_date:{
														title: 'Project Start Date',
														view:function(ui, obj){

															if (ui) {

																sb.notify({
																	type: 'view-field',
																	data: {
																		type: 'date',
																		property: 'start_date',
																		ui: ui,
																		obj: obj.related_object.main_object,
																		options: {
																			dateType: 'day'
																		}
																	}
																});

															} else {

																if (obj.related_object.main_object.start_date) {

																	return moment(obj.related_object.main_object.start_date).format('l, h:mma');

																} else {

																	return ' ';

																}

															}
														}
													}
													, event_name:{
														title:'Event Name',
														view:function(ui,obj){

															var ret = '';
															var contactLink;
															if(obj){


																if ( (obj.related_object || {}).main_object ) {

																	ret = obj.related_object.main_object.name;

																	contactLink = sb.data.url.createPageURL('object-view', {
																		id: 					obj.related_object.main_object.id
																		, name: 				obj.related_object.main_object.name
																		, object_bp_type: 		'groups'
																		, type: 				'project'
																	});
																}

															}

															if(ui && contactLink){
																var text = '<span class="ui text" style="white-space:normal"><b>'+ret+'</b></span>';
																ui.makeNode('data', 'div', {
																	text: text,
																	tag:'a',
																	href:contactLink
																});
															}else{
																return ret;
															}

														}
													}
													, name: {
														title:'Invoice Name',
														view:function(ui, obj){

															var ret = 'Not set';
															if(obj.name) {
																ret = obj.name;
															}

															if(ui){
																var text = '<span class="ui text" style="white-space:normal">'+ret+'</span>';
																ui.makeNode('invoice_name','div',{text:text});
															}

															return ret;
														}
													},
													due_date:{
														title:'Due Date',
														rangeOver:true,
														view:function(ui, obj){

															if (ui) {

																sb.notify({
																	type: 'view-field',
																	data: {
																		type: 'date',
																		property: 'due_date',
																		ui: ui,
																		obj: obj,
																		options: {
																			dateType: 'day'
																		}
																	}
																});

															} else {

																if (obj.due_date) {

																	return moment(obj.due_date).format('l');

																} else {

																	return ' ';

																}

															}
														}
													}
													, amount:{
														title:'Invoice Total',
														view:function(ui, obj){

															if(ui){

																ui.makeNode(
																	'amount'
																	, 'div'
																	, {
																		css:'ui right aligned'
																		, text:'$'+ ((obj.amount)/100).formatMoney(2)
																	}
																);

															} else {

																return '$'+ ((obj.amount)/100).formatMoney(2);

															}

														}
													}
													, total_payments:{
														title:'Total Payments',
														view:function(ui, obj){

															var amount = obj.amount;
															var balance = obj.balance;
															var paid = amount - balance;

															if(ui){

																ui.makeNode(
																	'amount'
																	, 'div'
																	, {
																		css:'ui right aligned'
																		, text:'$'+ ((paid)/100).formatMoney(2)
																	}
																);

															} else {

																return '$'+ ((paid)/100).formatMoney(2);

															}

														}
													}
													, balance:{
														title:'Invoice Balance',
														view:function(ui, invoice){


															var balance = invoice.balance;

															if(ui){

																ui.makeNode(
																	'amount'
																	, 'div'
																	, {
																		css:'ui right aligned'
																		, text:'$'+ ((balance)/100).formatMoney(2)
																	}
																);

															} else {

																return '$'+ ((balance)/100).formatMoney(2);

															}

														}
													}
													, contact:{
														title:'Contact',
														view:function(ui,obj){

															var ret = 'No contact selected';
															var url = '';

															if ((obj.related_object || {}).main_object) {
																ret = obj.related_object.main_object.main_contact.fname +' '+ obj.related_object.main_object.main_contact.lname;
																url = sb.data.url.createPageURL('object', {
																	id: 					obj.related_object.main_object.main_contact.id
																	, name: 				obj.related_object.main_object.main_contact.fname +' '+ obj.related_object.main_object.main_contact.lname
																	, object_bp_type: 		'contacts'
																	, type: 				'contacts'
																});
															}

															if(ui){
																ui.makeNode('data', 'div', {text: ret, href:url, tag:'a', css:'ui blue text', style:'text-decoration:underline;'});
															}else{
																return ret;
															}

														}
													}
                                                    , sent: {
                                                            title:'Last Sent'
                                                            , view: function(ui, obj){
                                                                var sentBy = '';

                                                                if ( (obj.sent_by || {}).object_bp_type == 'users' ) {
                                                                    sentBy = 'by ' + obj.sent_by.fname + ' ' + obj.sent_by.lname;
                                                                } else if (  (obj.sent_by || {}).object_bp_type == 'instances' ){
                                                                    sentBy = '<i class="square envelope icon"></i>Automated Reminder';
                                                                }

                                                                var sentOn = obj.sent_on  ? 'on ' + moment(obj.sent_on).local().format('M/D/YYYY - h:mm a') : ' ';

                                                                if ( ui ){
                                                                    ui.makeNode('by', 'div', {text: sentBy, id: 'sentBy'});
                                                                    ui.makeNode('sent', 'div', {text: sentOn, id: 'sentOn', style: 'margin-left: 3px;'});
                                                                } else {
                                                                    return sentBy;
                                                                }
                                                            }
                                                    }
													, send_button:{
														title:'Sent On',
														view:function(ui, obj) {

                                                            var sentOn = obj.sent_on  ? moment(obj.sent_on).local().format('M/D/YYYY h:mm a') : ' ';

															if (ui) {

                                                                var btnText =  obj.sent_on  ? 'Re-send Invoice' : 'Send Invoice';
                                                                var btnCSS =  obj.sent_on ? 'ui violet basic button' : 'ui violet button';
                                                                // - Last Sent on '+ moment(inv.sent_on).local().format('M/D/YYYY h:mm a')
																ui.makeNode('send-'+obj.id, 'div', {css: btnCSS, text:btnText, id:obj.id}).notify('click', {
																	type:'invoicesRun',
																	data:{
																		run: function() {

																			$('#'+obj.id).addClass('loading');

																			sb.data.db.service("InvoicesService", "sendSingleInvoices", {invoice: obj.id}, function (res) {

                                                                                sb.data.db.obj.update('invoices', {id: obj.id, sent: 'Yes', sent_on: moment().format(), sent_by: +sb.data.cookie.userId}, function(updated){
                                                                                    $('#'+obj.id).removeClass('loading').text('Re-send Invoice');

                                                                                    $('#sentOn').text( moment(updated.sent_on).local().format('M/D/YYYY h:mm a') );
                                                                                    $('#sentBy').text( 'By: ' + updated.sent_by.fname + updated.sent_by.lname);
                                                                                    sb.dom.alerts.alert('Invoice Sent!', "This invoice has been sent to the project's main contact.", 'success');

                                                                                }, 1);

																			});
																		}
																	}
																});

															} else {
																return sentOn;
															}
														}
													}
												}
												, subviews: {
													table: {
														hideSelectionBoxes: true
														, hideRowActions: true
													}
												}
												, where: {
													childObjs: {
														name: true
														, due_date: true
														, amount: true
														, balance: true
														, payments: {
															amount: true
														}
														, main_contact: true
														, related_object: {
															main_object: {
																object_uid: true
																, start_date: true
																, end_date: true
																, name: true
																, state: true
																, main_contact: {
																	id: true
																	, fname: true
																	, lname: true
																}
																, type: {
																	name: true
																}
															}
														}
													}
													, paged: {
														count: true,
														page: 0,
														pageLength: 50,
													}
												}
												, query: function(data, callback) {

													//callback(data);
													sb.data.db.service("InvoicesService", "getInvoicesForInvoicesReport", {data}, function (res) {

														data.data = res.data;
														data.recordsFiltered = res.recordsFiltered;
														data.recordsTotal = res.recordsTotal;

														callback(data);

													});
												}
											}
										}

									);

								}
							},
							// Accounts aging report
							{
								id:'accountsAgingReport',
								type:'custom',
								title:'Accounts Aging Report',
								icon:{
									type:'file alternate',
									color:'olive'
								},
								dom:function(dom, state, draw){

									var options = {
										header:'Accounts Aging Report',
										icon:{
											type:'chart area',
											color:'yellow'
										},
										state:state
									};

									accountsAgingReport(dom, options, draw);

								}
							},
							// Payments By Client Report
							{
								id: 'paymentsByClient'
								, type: 'custom'
								, title: 'Payments By Client Report'
								, icon: {
									type: 'building'
									, color: 'black'
								}
								, dom: function(ui, state, draw) {

									createPaymentsCollectionUI (
										ui
										, state
										, {
											collections: {
												selectedView:'table'
												, actions: {
													create:		false
													, downloadCSV: true
												}
												, groupings: {
													main_client: 'Client'
												}
												, filterBy: {
													owner: 'Managers'
												}
												, menuSubViews: false
									            , fields: {
									                    txn: {
									                        title: 'Transaction ID'
									                        , view: function(dom, obj) {

										                        var paymentId = obj.object_uid;

										                        if ( !_.isEmpty(obj.details.id) ) {
											                        paymentId = obj.details.id;
										                        }

																if(dom)	{
																	dom.makeNode('amount', 'div', {css:'ui right aligned', text:paymentId});
									                            }

									                            return paymentId;

									                        }
									                    }
									                    , date_created: {
									                        title: 'Payment Date'
									                        , type: 'date'
									                        , options: {
										                        showRelative:false
										                        , dateType: 'day'
										                        , style: 'font-weight: normal !important;'
													    }
									                    }
									                    , amount: {
									                        title: 'Payment Amount'
									                        , view: function(dom, obj) {

																if(dom){
																	dom.makeNode(
																		'amount'
																		, 'div'
																		, {
																			css:'ui right aligned'
																			, text:'$'+ (obj.amount/100).formatMoney(2)
																		}
																	);
																}

																return '$'+ (obj.amount/100).formatMoney(2);

									                        }
									                    }
									                    , fees: {
									                        title: 'Processing Fees'
									                        , view: function(dom, obj) {

																var amt = 0;
																if(obj.details.id){
																	amt = (obj.fee);
																}

																if(dom){
																	dom.makeNode(
																		'amount'
																		, 'div'
																		, {
																			css:'ui right aligned'
																			, text:'$'+ (amt/100).formatMoney(2)
																		}
																	);
																}

																return '$'+ (amt/100).formatMoney(2);


									                        }
									                    }
									                    , total: {
									                        title: 'Total Payment'
									                        , view: function(dom, obj) {

																if (obj.fee === undefined) {
																	obj.fee = 0;
																}

																if(dom){
																	dom.makeNode(
																		'amount'
																		, 'div'
																		, {
																			css:'ui right aligned'
																			, text:'$'+ ((obj.amount+obj.fee)/100).formatMoney(2)
																		}
																	);
																}

																return '$'+ ((obj.amount+obj.fee)/100).formatMoney(2);

									                        }
									                    }
									                    , main_client: {
										                    isSearchable:	true
										                    , isHidden: 	true
										                    , view: function(dom, obj) {
											                    if(obj.main_client){
											                    	return obj.main_client.fname;
											                    }else{
												                    return '';
											                    }
										                    }
									                    }
									                    , tags: {
															view: function() { return ''; }
															, isHidden: true
														}
									                }
												, subviews: {
													timeRangeOptions:{
														reports:true
													}
													, table: {
														groupBy: {
															main_client: 'Client'
														}
														, hideSelectionBoxes:true
	 													, hideRowActions:  true

													}
												}
												, metrics:{
													sum:{
														title:'Total'
														, fields:{
															amount:function(ui, data, rawValue, datum){

																var totalAmount = 0;

																if(_.isArray(datum)){

																	_.each(datum, function(inv){

																		totalAmount += inv.amount;

																	});

																}else{

																	totalAmount = data;

																}


																if(!_.isUndefined(ui) && !_.isNull(ui)){

																	ui.empty();
																	ui.makeNode('total', 'div', {css: 'ui right aligned', text:'<strong>$'+ (totalAmount/100).formatMoney() + '</strong>'});
																	ui.patch();

																}


																return (rawValue) ? totalAmount : '$'+ (totalAmount/100).formatMoney();

															},
															fees:function(ui, data, rawValue, datum){

																var totalAmount = 0;

																if(_.isArray(datum)){

																	_.each(datum, function(inv){

																		if (inv.fee === undefined) {
																			obj.fee = 0;
																		}

																		totalAmount += inv.fee;

																	});

																}else{

																	totalAmount = data;

																}


																if(!_.isUndefined(ui) && !_.isNull(ui)){

																	ui.empty();
																	ui.makeNode('total', 'div', {css: 'ui right aligned', text:'<strong>$'+ (totalAmount/100).formatMoney() + '</strong>'});
																	ui.patch();

																}


																return (rawValue) ? totalAmount : '$'+ (totalAmount/100).formatMoney();

															},
															total:function(ui, data, rawValue, datum){

																var totalAmount = 0;

																if(_.isArray(datum)){

																	_.each(datum, function(inv){

																		if (inv.fee === undefined) {
																			obj.fee = 0;
																		}

																		totalAmount += inv.fee;
																		totalAmount += inv.amount;

																	});

																}else{

																	totalAmount = data;

																}

																if(!_.isUndefined(ui) && !_.isNull(ui)){

																	ui.empty();
																	ui.makeNode('total', 'div', {css: 'ui right aligned', text:'<strong>$'+ (totalAmount/100).formatMoney() + '</strong>'});
																	ui.patch();

																}


																return (rawValue) ? totalAmount : '$'+ (totalAmount/100).formatMoney();

															}
														}
													}
												}
												, where: {
													test_payment: false
													, childObjs: {
														stripe_payment_id:  true
														, owner: 			true
														, main_client: 	true
														, amount: 		true
														, details: 		true
														, object_uid: 		true
														, fee: true
														, invoice: {
															main_contact:	true
															, name: 		true
															, owner: 		true
															, id: 		true
														}
													}
												}
											}
										}
									);

								}
							},
							// Payments Report AccountsRecievable
							{
								id: 'paymentsReportAccountsRecievable'
								, type: 'custom'
								, title: 'Payments Report (Accounts Receivable)'
								, icon: {
									type: 'building'
									, color: 'black'
								}
								, dom: function(ui, state, draw) {

									createPaymentsCollectionUI (
										ui
										, state
										, {
											collections: {
												selectedView:'table'
												, actions: {
													create:		false
													, downloadCSV: true
												}
												, filterBy: {
													owner: 'Managers'
												}
												, menuSubViews: false
												, fields: {
													event_id: {
														title: 'Event Id'
														, view: function(ui, obj) {

															if (obj.invoice && obj.hasOwnProperty('id')) {

																if (ui) {

																	ui.makeNode('eventId', 'div', {
																		text: '#' + obj.invoice.related_object.main_object.object_uid
																	});

																} else {

																	return obj.invoice.related_object.main_object.object_uid;

																}
															}
														}
													}
												    , contact: {
													   isSearchable:	true
													   , title:'Contact'
													   , view: function(dom, obj) {

														   if(obj.invoice){

															   if(
															   	dom
															   	&& obj.invoice.related_object.main_object.hasOwnProperty('id')
															   ) {

																   var url = sb.data.url.createPageURL(
																	   'object',
																	   {
																		   type: 'contacts',
																		   id: obj.invoice.related_object.main_object.main_contact.id,
																		   name: obj.invoice.related_object.main_object.main_contact.fname +' '+obj.invoice.related_object.main_object.main_contact.lname
																	   }
																   );

																   dom.makeNode('datum', 'div', {tag:'a', href:url, text:'<b><u>'+obj.invoice.related_object.main_object.main_contact.fname+' '+ obj.invoice.related_object.main_object.main_contact.lname +'</u></b>'});

																   return obj.invoice.related_object.main_object.main_contact.fname +' '+obj.invoice.related_object.main_object.main_contact.lname;

															   }
															   else {

																	return obj.invoice.related_object.main_object.main_contact.fname +' '+obj.invoice.related_object.main_object.main_contact.lname;

															   }

														   } else {

															   return '';

														   }
													   }
												    }
												    , company: {
														  isSearchable:	true
														  , title:'Company'
														  , view: function(dom, obj) {

															  if(obj.invoice){

																  if(
																  	dom
																  	&& obj.invoice.related_object.main_object.hasOwnProperty('id')
																  )	{

																	  var url = sb.data.url.createPageURL(
																			 'object',
																			 {
																				 type:'companies',
																				 id:obj.invoice.related_object.main_object.main_contact.company.id,
																				 name:obj.invoice.related_object.main_object.main_contact.company.name
																			 }
																		 );

																		 dom.makeNode('datum', 'div', {tag:'a', href:url, text:'<b><u>'+obj.invoice.related_object.main_object.main_contact.company.name+'</u></b>'});

																	  return obj.invoice.related_object.main_object.main_contact.company.name;

																  }
																  else {

																	return obj.invoice.related_object.main_object.main_contact.company.name;

																  }

															  }else{
																  return '';
															  }
														  }
													}
												    , project: {
														  isSearchable:	false
														  , title:'Project'
														  , view: function(dom, obj) {
															  if(obj.invoice){
																  if(
																  	dom
																  	&& obj.invoice.related_object.main_object.hasOwnProperty('id')
																  )	{

																	  var url = sb.data.url.createPageURL(
																		   'object',
																		   {
																			   type:'project',
																			   id:obj.invoice.related_object.main_object.id,
																			   name:obj.invoice.related_object.main_object.name
																		   }
																	   );

																	   dom.makeNode('datum', 'div', {tag:'a', href:url, text:'<b><u>'+obj.invoice.related_object.main_object.name+'</u></b>'});

																	   return obj.invoice.related_object.main_object.name;

																  }
																  else {

																	return obj.invoice.related_object.main_object.name;

																  }
															  }else{
																  return '';
															  }
														  }
													}
													, txn: {
														isSearchable:	true
														, title: 'Transaction ID'
														, view: function(dom, obj) {

															var paymentId = obj.object_uid;

															if (
																obj.hasOwnProperty('stripeId')
																&& !_.isEmpty(obj.stripeId)
															) {

																paymentId = obj.stripeId;

															} else if (
																obj.hasOwnProperty('icg_payment_id')
																&& !_.isEmpty(obj.icg_payment_id)
															) {

																paymentId = obj.icg_payment_id;

															}

															if(dom)	{
																dom.makeNode('amount', 'div', {css:'ui right aligned', text:paymentId});
															}

															return paymentId;

														}
													}
													, transaction_type: {
														title: 'Payment Type'
														, view: function(ui, obj) {

															if (ui) {

																ui.makeNode('type', 'div', {text: obj.type});

															}

															return obj.type;

														}
													}
													, date_created: {
														title: 'Payment Date'
														, type: 'date'
														, options: {
															showRelative:false
															, dateType: 'day'
															, style: 'font-weight: normal !important;'
														}
													}
													, amount: {
														title: 'Payment Amount'
														, view: function(dom, obj) {

															if(dom){
																dom.makeNode(
																	'amount'
																	, 'div'
																	, {
																		css:'ui right aligned'
																		, text:'$'+ (obj.amount/100).formatMoney(2)
																	}
																);
															}

															return '$'+ (obj.amount/100).formatMoney(2);

														}
													}
													, fees: {
														title: 'Processing Fees'
														, view: function(dom, obj) {

															var amt = 0;

															if (
																!_.isArray(obj.details)
																&& obj.details.hasOwnProperty('stripePI')
															) {

																amt = (obj.fee);

															}

															if(dom){
																dom.makeNode(
																	'amount'
																	, 'div'
																	, {
																		css:'ui right aligned'
																		, text:'$'+ (amt/100).formatMoney(2)
																	}
																);
															}

															return '$'+ (amt/100).formatMoney(2);


														}
													}
													, total: {
														title: 'Total Payment'
														, view: function(dom, obj) {

															if (obj.fee === undefined) {
																obj.fee = 0;
															}

															if(dom){
																dom.makeNode(
																	'amount'
																	, 'div'
																	, {
																		css:'ui right aligned'
																		, text:'$'+ ((obj.amount+obj.fee)/100).formatMoney(2)
																	}
																);
															}

															return '$'+ ((obj.amount+obj.fee)/100).formatMoney(2);

														}
													}
												}
												, subviews: {
													timeRangeOptions:{
														reports:true
													}
													, table: {
														groupBy: {
															main_client: 'Client'
														}
														, hideSelectionBoxes:true
														 , hideRowActions:  true

													}
												}
												, metrics:{
													sum:{
														title:'Total'
														, fields:{
															amount:function(ui, data, rawValue, datum){

																var totalAmount = 0;

																if(_.isArray(datum)){

																	_.each(datum, function(inv){

																		totalAmount += inv.amount;

																	});

																}else{

																	totalAmount = data;

																}


																if(!_.isUndefined(ui) && !_.isNull(ui)){

																	ui.empty();
																	ui.makeNode('total', 'div', {css: 'ui right aligned', text:'<strong>$'+ (totalAmount/100).formatMoney() + '</strong>'});
																	ui.patch();

																}


																return (rawValue) ? totalAmount : '$'+ (totalAmount/100).formatMoney();

															},
															fees:function(ui, data, rawValue, datum){

																var totalAmount = 0;

																if(_.isArray(datum)){

																	_.each(datum, function(inv){

																		totalAmount += inv.fee;

																	});

																}else{

																	totalAmount = data;

																}


																if(!_.isUndefined(ui) && !_.isNull(ui)){

																	ui.empty();
																	ui.makeNode('total', 'div', {css: 'ui right aligned', text:'<strong>$'+ (totalAmount/100).formatMoney() + '</strong>'});
																	ui.patch();

																}


																return (rawValue) ? totalAmount : '$'+ (totalAmount/100).formatMoney();

															},
															total:function(ui, data, rawValue, datum){

																var totalAmount = 0;

																if(_.isArray(datum)){

																	_.each(datum, function(inv){

																		totalAmount += inv.fee + inv.amount;

																	});

																}else{

																	totalAmount = data;

																}


																if(!_.isUndefined(ui) && !_.isNull(ui)){

																	ui.empty();
																	ui.makeNode('total', 'div', {css: 'ui right aligned', text:'<strong>$'+ (totalAmount/100).formatMoney() + '</strong>'});
																	ui.patch();

																}


																return (rawValue) ? totalAmount : '$'+ (totalAmount/100).formatMoney();

															}
														}
													}
												}
												, where: {
													childObjs: {}
													, paged: {
														sortCol: 'date_created',
														sortDir: 'desc',
														count: true,
														page: 0,
														pageLength: 50,
													}
												}
												, query: function(data, callback) {

													//callback(data);
													sb.data.db.service("PaymentsService", "getPaymentsForPaymentsReportAccountsReceivable", {data}, function (res) {

														data.data = res.data;
														data.recordsFiltered = res.recordsFiltered;
														data.recordsTotal = res.recordsTotal;

														callback(data);

													});
												}
											}
										}
									);

								}
							},
							// Payments Report Accounting
							{
								id: 'paymentsReportAccounting'
								, type: 'custom'
								, title: 'Payments Report (Accounting)'
								, icon: {
									type: 'building'
									, color: 'black'
								}
								, dom: function(ui, state, draw) {

									createPaymentsCollectionUI (
										ui
										, state
										, {
											collections: {
												selectedView:'table'
												,objectType: 'payments'
												, actions: {
													create:		false
													, downloadCSV: true
												}
												, filterBy: {
													owner: 'Managers'
												}
												, menuSubViews: false
												, fields: {
													event_id: {
														title: 'Event Id'
														, view: function(ui, obj) {

																if (obj.invoice && obj.hasOwnProperty('id')) {

																	if (ui) {

																		ui.makeNode('eventId', 'div', {
																			text: '#' + obj.invoice.related_object.main_object.object_uid
																		});

																	} else {

																		//return obj.main_object.main_object.object_uid;

																		return obj.invoice.related_object.main_object.object_uid;

																	}

																}

														}
													}
													, event_date: {
														type: 'date'
														, title: 'Event Date'
														, view: function(ui, obj) {

															if (ui) {

																sb.notify({
																	type: 'view-field',
																	data: {
																		type: 'date',
																		property: 'start_date',
																		ui: ui,
																		obj: obj.main_object.main_object,
																		options: {
																			dateType: 'day'
																			,showRelative: false
																		}
																	}
																});

															} else {

																return obj.main_object.main_object.start_date;

															}

														}
													}
													, event_name: {
														title: 'Event Name'
														, type: 'title'
														, view: function(ui, obj) {

																if (obj.invoice) {

																	if (ui) {

																		sb.notify({
																			type: 'view-field',
																			data: {
																				type: 'title',
																				property: 'name',
																				ui: ui,
																				obj: obj.invoice.related_object.main_object,
																				options: {}
																			}
																		});

																	} else {

																		if (obj.invoice.related_object.main_object.name) {

																			return obj.invoice.related_object.main_object.name;

																		} else {

																			return ' ';

																		}

																	}

																}

														}
													}
													, locations: {
														type: 'locations'
														, title: 'Event Location(s)'
														, view: function(ui, obj) {

																if (obj.invoice) {

																	if (ui) {

																		sb.notify({
																			type: 'view-field',
																			data: {
																				type: 'locations',
																				property: 'locations',
																				ui: ui,
																				obj: obj.invoice.related_object.main_object,
																				options: {}
																			}
																		});

																	} else {

																		if (!_.isEmpty(obj.invoice.related_object.main_object.locations)) {

																			var locationNames = [];

																			_.each(obj.invoice.related_object.main_object.locations, function(obj, i) {

																				locationNames.push(obj.name);

																			});

																			return locationNames.join(', ');

																		} else {

																			return ' ';

																		}

																	}

																}

														}
													}
													, head_count: {
														title: 'Head Count'
														, view: function(ui, obj) {

																if (obj.invoice) {

																	sb.notify({
																		type: 'view-field',
																		data: {
																			type: 'quantity',
																			property: 'head_count',
																			ui: ui,
																			obj: obj.invoice.related_object.main_object,
																			options: {}
																		}
																	});
																}
														}
													}
													, invoice_total: {
														title: 'Invoice Amount'
														, view: function(dom, obj) {

															if(dom){

																dom.makeNode(
																	'amount'
																	, 'div'
																	, {
																		css:'ui right aligned'
																		, text:'$'+ ((obj.amount)/100).formatMoney(2)
																	}
																);
															} else {

																return '$'+ ((obj.amount)/100).formatMoney(2);

															}

														}
													}
													, amount: {
														title: 'Payment Amount'
														, view: function(dom, obj) {

															if(dom){

																dom.makeNode(
																	'fee'
																	, 'div'
																	, {
																		css:'ui right aligned'
																		, text:'$'+ ((obj.amount)/100).formatMoney(2)
																	}
																);
															} else {

																return '$'+ ((obj.amount)/100).formatMoney(2);

															}

														}
													}
													, fee: {
														title: 'Processing Fees'
														, view: function(dom, obj) {

															if(dom){

																dom.makeNode(
																	'fee'
																	, 'div'
																	, {
																		css:'ui right aligned'
																		, text:'$'+ ((obj.fee)/100).formatMoney(2)
																	}
																);
															} else {

																return '$'+ ((obj.fee)/100).formatMoney(2);

															}

														}
													}
													, payment_total: {
														title: 'Payment Total'
														, view: function(dom, obj) {

															if(dom){

																dom.makeNode(
																	'fee'
																	, 'div'
																	, {
																		css:'ui right aligned'
																		, text:'$'+ ((obj.amount + obj.fee)/100).formatMoney(2)
																	}
																);
															} else {

																return '$'+ ((obj.amount + obj.fee)/100).formatMoney(2);

															}

														}
													}
													, date_created: {
														type: 'date'
														, title: 'Payment Date'
														, view: function(ui, obj) {

															if (ui) {

																sb.notify({
																	type: 'view-field',
																	data: {
																		type: 'date',
																		property: 'date_created',
																		ui: ui,
																		obj: obj,
																		options: {
																			dateType: 'day'
																			,showRelative: false
																		}
																	}
																});

															} else {

																return obj.date_created;

															}

														}
													}
												}
												, subviews: {
													timeRangeOptions:{
														reports:true
													}
													, table: {
														groupBy: {
															main_client: 'Client'
														}
														, hideSelectionBoxes: true
														 , hideRowActions:  true

													}
												}
												, metrics:{
													sum:{
														title:'Total'
														, fields:{
															amount:function(ui, data, rawValue, datum){

																var totalAmount = 0;

																if(_.isArray(datum)){

																	_.each(datum, function(inv){

																		totalAmount += inv.amount;

																	});

																}else{

																	totalAmount = data;

																}


																if(!_.isUndefined(ui) && !_.isNull(ui)){

																	ui.empty();
																	ui.makeNode('total', 'div', {css: 'ui right aligned', text:'<strong>$'+ (totalAmount/100).formatMoney() + '</strong>'});
																	ui.patch();

																}


																return (rawValue) ? totalAmount : '$'+ (totalAmount/100).formatMoney();

															},
															fees:function(ui, data, rawValue, datum){

																var totalAmount = 0;

																if(_.isArray(datum)){

																	_.each(datum, function(inv){

																		totalAmount += inv.fee;

																	});

																}else{

																	totalAmount = data;

																}


																if(!_.isUndefined(ui) && !_.isNull(ui)){

																	ui.empty();
																	ui.makeNode('total', 'div', {css: 'ui right aligned', text:'<strong>$'+ (totalAmount/100).formatMoney() + '</strong>'});
																	ui.patch();

																}


																return (rawValue) ? totalAmount : '$'+ (totalAmount/100).formatMoney();

															},
															total:function(ui, data, rawValue, datum){

																var totalAmount = 0;

																if(_.isArray(datum)){

																	_.each(datum, function(inv){

																		totalAmount += inv.fee + inv.amount;

																	});

																}else{

																	totalAmount = data;

																}


																if(!_.isUndefined(ui) && !_.isNull(ui)){

																	ui.empty();
																	ui.makeNode('total', 'div', {css: 'ui right aligned', text:'<strong>$'+ (totalAmount/100).formatMoney() + '</strong>'});
																	ui.patch();

																}


																return (rawValue) ? totalAmount : '$'+ (totalAmount/100).formatMoney();

															}
														}
													}
												}
												, where: {
													childObjs: {
														owner: 			true
														, main_client: 	true
														, amount: 		true
														, details: 		true
														, object_uid: 		true
														, fee: true
														, invoice: {
															related_object: {
																name:true
																, main_object:{
																	locations:true,
																	name:true,
																	head_count: true,
																	main_contact:{
																		name:true,
																		fname:true,
																		lname:true,
																		company:{
																			name:true
																		}
																	},
																	start_date: true,
																	group_type: true,
																	object_uid: true
																}
															}
															, name: 		true
															, owner: 		true
															, id: 		true
														}
														, main_object: {
															main_object: true
														}
														, status: true
													}
													, paged: {
														sortCol: 'date_created',
														sortDir: 'desc',
														count: true,
														page: 0,
														pageLength: 50,
													}
												}
												, query: function(data, callback) {

													//callback(data);
													sb.data.db.service("PaymentsService", "getPaymentsForReports", {data}, function (res) {

														data.data = res.data;
														data.recordsFiltered = res.recordsFiltered;
														data.recordsTotal = res.recordsTotal;

														callback(data);

													});
												}
											}
										}
									);

								}
							},
							// Payments By Project
							{
								id: 'paymentsByProject'
								, type: 'custom'
								, title: 'Payments By Project'
								, icon: {
									type: 'usd'
									, color: 'black'
								}
								, dom: function(ui, state, draw) {

									createPaymentsCollectionUI (
										ui
										, state
										, {
											collections: {
												selectedView:'table'
												, objectType: 'groups'
												, sortCol: 'start_date'
												, sortDir: 'desc'
												, sortCast: 'date'
												, actions: {
													create:		false
													, downloadCSV: true
												}
												, filterBy: {
												}
												, menuSubViews: false
												, fields: {
													uid: {
														title: 'Event Id'
														, view: function(ui, obj) {

																if (obj) {

																	if (ui) {

																		ui.makeNode('eventId', 'div', {
																		 text: '#' + obj.object_uid
																	 });

																	} else {

																		return obj.object_uid;

																	}

																}

														}
													}
													, start_date: {
														type: 'date'
														, title: 'Start Date'
														, view: function(ui, obj) {

															 if (obj) {

																 if (ui) {

																	 sb.notify({
																		 type: 'view-field',
																		 data: {
																			 type: 'date',
																			 property: 'start_date',
																			 ui: ui,
																			 obj: obj,
																			 options: {
																				 dateType: 'day',
																				 showRelative: false
																			 }
																		 }
																	 });

																 } else {

																	 if (obj.start_date) {

																		 return obj.start_date;

																	 } else {

																		 return 'No Set Start Date';

																	 }

																 }

															 }

														}
													}
													, event_name: {
														title: 'Event Name'
														, type: 'title'
														, view: function(ui, obj) {

															if (obj) {
																if(ui) {

																	var url = sb.data.url.createPageURL(
																		'object',
																		{
																			type:'project',
																			id:obj.id,
																			name:obj.name
																		}
																	);

																	ui.makeNode('datum', 'div', {tag:'a', href:url, text:'<b><u>'+obj.name+'</u></b>'});

																	return obj.name;

																} else {

																	return obj.name;

																}
															} else {
																return '';
															}
														}
													}
													, event_locations: {
														type: 'locations'
														, title: 'Event Location(s)'
														, view: function(ui, obj) {

															 if (obj) {

																 if (ui) {

																	 sb.notify({
																		 type: 'view-field',
																		 data: {
																			 type: 'locations',
																			 property: 'locations',
																			 ui: ui,
																			 obj: obj,
																			 options: {}
																		 }
																	 });

																 } else {

																	 if (!_.isEmpty(obj.locations)) {

																		 var locationNames = [];

																		 _.each(obj.locations, function(obj, i) {

																			 locationNames.push(obj.name);

																		 });

																		 return locationNames.join(', ');

																	 } else {

																		 return ' ';

																	 }

																 }

															 }

														}
													}
													, invoice_value: {
														title: 'Project Total'
														, view: function(ui, obj) {

															if(ui){

																ui.makeNode(
																	'total'
																	, 'div'
																	, {
																		css:'ui right aligned'
																		, text:'$'+ ((obj.invoice_value)/100).formatMoney(2)
																	}
																);
															} else {

																return '$'+ ((obj.invoice_value)/100).formatMoney(2);

															}

														}
													}
													, invoice_payments: {
														title: 'Invoice Payments'
														, view: function(ui, obj) {

															if(ui){

																ui.makeNode(
																	'total'
																	, 'div'
																	, {
																		css:'ui right aligned'
																		, text:'$'+ ((obj.paid)/100).formatMoney(2)
																	}
																);
															} else {

																return '$'+ ((obj.paid)/100).formatMoney(2);

															}

														}
													}
													, invoice_payment_fees: {
														title: 'Invoice Payment Fees'
														, view: function(ui, obj) {

															if(ui){

																ui.makeNode(
																	'total'
																	, 'div'
																	, {
																		css:'ui right aligned'
																		, text:'$'+ ((obj.feesPaid)/100).formatMoney(2)
																	}
																);
															} else {

																return '$'+ ((obj.feesPaid)/100).formatMoney(2);

															}

														}
													}
													, total_Paid: {
														title: 'Total Paid'
														, view: function(ui, obj) {

															var totalPaid = obj.feesPaid + obj.paid;

															if(ui){

																ui.makeNode(
																	'total'
																	, 'div'
																	, {
																		css:'ui right aligned'
																		, text:'$'+ ((totalPaid)/100).formatMoney(2)
																	}
																);
															} else {

																return '$'+ ((totalPaid)/100).formatMoney(2);

															}

														}
													}
												}
												, subviews: {
													timeRangeOptions:{
														reports:true
													}
													, table: {
														groupBy: {
															main_client: 'Client'
														}
														, hideSelectionBoxes:true
														 , hideRowActions:  true

													}
												}
												, metrics:{
												}
												, where: {
													childObjs: {}
													,paged: {
														sortCol: 'start_date'
														,sortDir: 'desc'
														,count: true
														,page: 0
														,pageLength: 50
													}
												}
												, query: function(data, callback, query, subview, range, types) {

														sb.data.db.service("ProjectService", "getDataForPaymetsByProejctReport", {data}, function (res) {

															data.data = res.data;
															data.recordsFiltered = res.recordsFiltered;
															data.recordsTotal = res.recordsTotal;

															callback(data);

														});
												},
											}
										}
									);
								}
							},
							// Invoices by client report
							{
								id: 'invoicesByClient',
								type: 'custom',
								title: 'Invoices By Client Report',
								icon: {
									type: 'building',
									color: 'blue'
								},
								dom: function(ui, state, draw) {

									var collectionsBody = undefined;
									var collectionsData = [];

									build_collections(ui, state, {
										collections: {
											selectedView: 'table',
											menu: {
												subviews: false
								        	},
											actions: {
												create:		false,
												downloadPDF: true
											},
											fields: {
/*
												main_client:{
													title:'Client',
													view: function (ui, obj) {

														var name = '';
														var url = '';

														if(obj.main_client){

															var name = obj.main_client.name;
															var url = sb.data.url.createPageURL(
																	'object',
																	{
																		type:'companies',
																		id:obj.main_client..id,
																		name:obj.main_client.name
																	}
																);

														}

														if(ui){

														}else{
															return name;
														}


													}
												},
*/
												name: {
													title:'Name',
													options: {
														style: 'font-weight: normal !important;'
													},
													view: function (ui, obj) {

														var name = getInvoiceName(obj);
														ui.makeNode(
															'n'
															, 'div'
															, {text: name}
														);

													}
												},
												due_date: {
													title:'Due Date',
													type: 'date',
													rangeOver: true,
													options: {
														showRelative:false,
										                dateType: 'day',
										                style: 'font-weight: normal !important;'
													}
												},
												amount: {
													title:'Total Amount',
													type: 'currency',
													options: {
														alignRight: true
													}
												},
												balance: {
													title:'Balance Due',
													type: 'currency',
													options: {
														alignRight: true
													}
												},
												tags: {
													view: function() { return false; },
													isHidden: true
												},
												owner: {
													title: 'Owner',
													isHidden: true
												}/*
,
												main_client: {
													title: 'Client',
													isHidden: true,
													isSearchable: true
												}
*/
											},
											groupings: {
												main_client: 'main_client'
											},
											filterBy: {
												owner: 'Managers'
											},
											metrics: {
												sum: {
													title: 'Total',
													fields: {
														amount: function(ui, data, rawValue) {

															var totalAmount = 0;

															if(_.isArray(data)){

																_.each(data, function(inv){

																	totalAmount += inv.amount;

																});

															}else{

																totalAmount = data;

															}


															if(!_.isUndefined(ui) && !_.isNull(ui)){

																ui.empty();
																ui.makeNode('total', 'div', {css: 'ui right aligned', text:'<strong>$'+ (totalAmount/100).formatMoney() +'</strong>'});
																ui.patch();

															}


															return (rawValue) ? totalAmount : '$'+ (totalAmount/100).formatMoney();

														},
														balance:function(ui, data, rawValue){

															var balanceTotal = 0;

															if(_.isArray(data)){

																if(!_.isEmpty(data)){

																	_.map(data, function(invoice){

																		var bal = 0;
																		if(!_.isNull(invoice.balance) && !_.isUndefined(invoice.balance)){
																			bal = invoice.balance;
																		}

																		balanceTotal += bal;

																	});
																}
															} else {

																balanceTotal = data;
															}


															if(!_.isUndefined(ui) && !_.isNull(ui)){

																ui.empty();
																ui.makeNode('balance', 'div', {css: 'ui right aligned', text:'<strong>$'+ (balanceTotal/100).formatMoney() +'</strong>'});
																ui.patch();

															}

															return (rawValue) ? balanceTotal : '$'+ (balanceTotal/100).formatMoney();

														}
													}
												}
											},
											subviews: {
												timeRangeOptions: {
													reports: true
												},
												table: {
													groupBy: {
														defaultTo: 'main_client'
													},
													hideSelectionBoxes: true,
													hideRowActions:  true
												}
											},
											where: {
												childObjs: {
													name: true,
													main_client: true,
													main_contact: {
														company: true
													},
													type: true,
													amount: true,
													balance: true,
													due_date: true,
													owner: true,
													related_object: {
														main_object: {
															name: true
														}
													}
												}
											}
										}
									});

								}
							},
							// Chart of accounts report
							{
								id: 	'chartOfAccounts',
								type: 	'custom',
								title: 	'Chart of Accounts',
								icon: 	{
									type: 'building',
									color: 'blue'
								},
								dom: 	chartOfAccountsReport
							},
							// Invoices by value
							{
								id: 'invoicesByValue',
								type: 'custom',
								title: 'Invoices By Value Report',
								icon: {
									type: 'chart bar',
									color: 'blue'
								},
								dom: function(ui, state, draw) {

									build_collections(ui, state, {
										collections: {
											selectedView: 'chart',
											menu: false,
											search: false,
											actions: {
												create:		false,
												downloadPDF: true
											},
											fields: {
												name: {
													title:'Name',
													options: {
														style: 'font-weight: normal !important;'
													}
												},
												due_date: {
													title:'Due Date',
													type: 'date',
													rangeOver: true,
													options: {
														showRelative:false,
										                dateType: 'day',
										                style: 'font-weight: normal !important;'
													}
												},
												amount: {
													title:'Total Amount',
													type: 'currency',
													options: {
														alignRight: true
													}
												},
												balance: {
													title:'Balance Due',
													type: 'currency',
													options: {
														alignRight: true
													}
												},
												tags: {
													view: function() { return false; },
													isHidden: true
												},
												owner: {
													title: 'Owner',
													isHidden: true
												},
												main_client: {
													title: 'Client',
													isHidden: true,
													isSearchable: true
												}
											},
											groupings: {
												main_client: 'main_client'
											},
											filterBy: {
												owner: 'Managers'
											},
											metrics: {
												sum: {
													title: 'Total',
													fields: {
														amount: function(ui, data, rawValue) {

															var totalAmount = 0;

															if(_.isArray(data)){

																_.each(data, function(inv){

																	totalAmount += inv.amount;

																});

															}else{

																totalAmount = data;

															}


															if(!_.isUndefined(ui) && !_.isNull(ui)){

																ui.empty();
																ui.makeNode('total', 'div', {css: 'ui right aligned', text:'<strong>$'+ (totalAmount/100).formatMoney() +'</strong>'});
																ui.patch();

															}


															return (rawValue) ? totalAmount : '$'+ (totalAmount/100).formatMoney();

														},
														balance:function(ui, data, rawValue){

															var balanceTotal = 0;

															if(_.isArray(data)){

																if(!_.isEmpty(data)){

																	_.map(data, function(invoice){

																		var bal = 0;
																		if(!_.isNull(invoice.balance) && !_.isUndefined(invoice.balance)){
																			bal = invoice.balance;
																		}

																		balanceTotal += bal;

																	});
																}
															} else {

																balanceTotal = data;
															}


															if(!_.isUndefined(ui) && !_.isNull(ui)){

																ui.empty();
																ui.makeNode('balance', 'div', {css: 'ui right aligned', text:'<strong>$'+ (balanceTotal/100).formatMoney() +'</strong>'});
																ui.patch();

															}

															return (rawValue) ? balanceTotal : '$'+ (balanceTotal/100).formatMoney();

														}
													}
												}
											},
											subviews: {
												chart: {
													range: {
														defaultTo: 	'range'
														, not: 		['all_time']
													}
													, defaults: {
														type: 		'bar_byValue'
													}
													, line: false
													, bar: false
													, pie: false
												}
											},
											where: {
												childObjs: {
													name: true,
													main_client: true,
													main_contact: {
														company: true
													},
													type: true,
													amount: true,
													balance: true,
													due_date: true,
													owner: true,
													amount: true
												}
											}
										}
									});

								}
							},
							{
								id: 'chartOfAccountsReport'
								, type: 'tool'
								, icon: {
									type: 'building'
								}
								, name: 'Chart of Account Report'
								, tip: ''
								, display: false
								, mainViews: [
									{
										dom: function(dom, state, draw) {

											chartOfAccountsReport (dom, state, draw);

										}
									}
								]
							}
						]
					}
				}
			});

			// Invoice Report (General)
			sb.notify({
				type:'register-report',
				data:{
					id:'invoiceReport',
					icon:'chart bar',
					header:'Invoice Report',
					subheader:'Invoice totals, payments, and balances',
					type:'Accounting'
				}
			});

			// Payments By Client Report
			sb.notify({
				type:'register-report',
				data:{
					id:'paymentsByClient',
					icon:'chart bar',
					header:'Payments By Client',
					subheader:'All payments made by each of your clients.',
					type:'Accounting'
				}
			});

			// Payments Report (Accounts Recievable)
			sb.notify({
				type:'register-report',
				data:{
					id:'paymentsReportAccountsRecievable',
					icon:'usd',
					header:'Payments Report (Accounts Recievable)',
					subheader:'All payments made.',
					type:'Accounting'
				}
			});

			if (
				appConfig.instance === 'infinity'
				|| appConfig.instance === 'nlp'
				|| appConfig.instance === 'rickyvoltz'
			) {

				// Payments Report (Infinity Accounting Team)
				sb.notify({
					type:'register-report',
					data:{
						id:'paymentsReportAccounting',
						icon:'usd',
						header:'Payments Report (Accounting)',
						subheader:'All payments made.',
						type:'Accounting'
					}
				});

				// Payments By Project Report
				sb.notify({
					type:'register-report',
					data:{
						id:'paymentsByProject',
						icon:'usd',
						header:'Payments By Project',
						subheader:'All payments made by project.',
						type:'Accounting'
					}
				});

			}

			// Accounts Aging Report
			sb.notify({
				type:'register-report',
				data:{
					id:'accountsAgingReport',
					icon:'chart area',
					header:'Accounts Aging Report',
					subheader:'Track unpaid customer invoices.',
					type:'Accounting'
				}
			});

			// Invoices by Client
			sb.notify({
				type: 'register-report',
				data: {
					id: 'invoicesByClient',
					icon: 'building',
					header: 'Invoices By Client Report',
					subheader: 'Client invoice totals, payments, and balances',
					type: 'Accounting'
				}
			});

			// Chart of Accounts Report
			sb.notify({
				type: 'register-report',
				data: {
					id: 		'chartOfAccounts',
					icon: 		'building',
					header: 	'Chart of Accounts',
					subheader: 	'Chart of accounts by company',
					type: 		'Accounting'
				}
			});

			// Invoices by value
			sb.notify({
				type: 'register-report',
				data: {
					id: 		'invoicesByValue',
					icon: 		'chart bar',
					header: 	'Invoices By Value',
					subheader: 	'Invoices by date and value.',
					type: 		'Accounting'
				}
			});

			sb.listen({
				'add-balance-to-invoice':this.addBalance,
				'balance-project-invoices':this.balanceProjectInvoices,
				'begin-invoice-payment':this.beginPayment,
				'choose-add-invoice':this.chooseAdd,
				'choose-payment-method':this.choosePaymentMethod,
				'close-invoice-preview':this.closePreview,
				'create-balance-invoice':this.createBalance,
				'create-initial-object-invoices':this.selectPaymentSchedule,
				'download-invoice-pdf':this.download,
				'edit-invoice':this.edit,
				'generate-object-statement':this.generateObjectStatement,
				'invoicesRun':this.run,
				'invoice-payment-completed':this.paymentCompleted,
				'invoices-table-view-loaded':this.showBalanceUI,
				'save-new-object-invoice-from-form':this.createFromForm,
				'create-object-invoice':this.createObjectInvoiceForm,
				'delete-all-invoices':this.deleteAllInvoices,
				'destroy-invoices': this.destroy,
				'email-invoice':this.emailInvoice,
				'invoice-payment-received':this.paymentReceived,
				'object-invoice-created':this.updateTable,
				'pay-multiple-invoices':this.payMultiple,
				'payment-schedule-selected': this.createInvoices,
				'save-invoice':this.save,
				'show-all-invoices':this.showAll,
				'show-contact-view':this.showContactView,
				'show-contact-invoice-list': this.showContactInvoices,
				'show-menu-invoice-list':this.showMenuInvoiceList,
				'show-object-invoice-list': this.showObjectInvoiceTable,
				'submit-invoice-payment':this.submitPayment,
				'update-invoices':this.updateInvoices,
				'update-invoice-due-dates':this.updateInvoiceDueDates,
				'update-menu-section-dates': this.updateMenuSectionDates,
				'view-all-invoices':this.viewAll,
				'view-all-invoices2':this.viewAll2,
				'view-invoice-payments':this.viewPayments,
				'view-invoice-preview':this.viewPreview,
				'view-single-invoice':this.viewSingleInvoice
			});

		},

		addBalance: function(data){

			data.modals.makeNode('add', 'modal', {});

			data.modals.add.body.makeNode('header', 'headerText', {size:'small', text:'Choose an invoice'});

			data.modals.add.body.makeNode('table', 'container', {});

			data.modals.add.body.table.makeNode(
				'table',
				'table',
				{
					css: 'table-hover table-condensed',
					columns: {
						name: 'Name',
						amount: 'Amount',
						balance: 'Balance',
						btns: ''
					}
				}
			);

			_.each(invoiceList, function(inv){

				data.modals.add.body.table.table.makeRow(
					'invoice-'+inv.id,
					[inv.name, inv.amount, inv.balance, '']
				);

				data.modals.add.body.table.table.body['invoice-'+inv.id].btns.makeNode('choose', 'button', {text:'Choose'}).notify('click', {
					type:'choose-add-invoice',
					data:{
						object:inv,
						balance:data.balance,
						type:data.type
					}
				}, sb.moduleId);

			});

			data.modals.patch();

			data.modals.add.show();

		},

		balanceProjectInvoices: function(data){

			sb.data.db.controller('balanceProjectInvoices', {projectId: data.state.project.id}, function(invoices){

				data.callback(invoices);

			}, false, true);

		},

		beginPayment: function(data){

			data.modals.makeNode('pay', 'modal', {});

			data.modals.pay.body.makeNode('header', 'headerText', {size:'small', css:'text-center', text:'Choose a payment method'});

			data.modals.pay.body.makeNode('buttons', 'buttonGroup', {});

			data.modals.pay.body.buttons.makeNode('credit', 'button', {text:'<i class="fa fa-credit-card"></i><br />Pay with credit/debit card<br /><small>(+3% Convenience Fee)</small>', css:''}).notify('click', {
				type:'choose-payment-method',
				data:{
					modal:data.modals.pay,
					object:data.object,
					type:'card'
				}
			}, sb.moduleId);

			data.modals.pay.body.buttons.makeNode('bank', 'button', {text:'<i class="fa fa-university"></i><br />Pay with bank account (ACH)<br /><small>(No Convenience Fee)</small>', css:''}).notify('click', {
				type:'choose-payment-method',
				data:{
					modal:data.modals.pay,
					object:data.object,
					type:'bank'
				}
			}, sb.moduleId);

			data.modals.patch();

			data.modals.pay.show();

		},

		chooseAdd: function(data){

			sb.dom.alerts.ask({
				title: 'Are you sure?',
				text: ''
			}, function(resp){

				if(resp){

					swal.disableButtons();

					sb.data.db.obj.getById('invoices', data.object.id, function(inv){

						switch(data.type){

							case 'add':

								inv.amount = inv.amount + (data.balance * -1);
								inv.balance = inv.balance + (data.balance * -1);

								break;

							case 'subtract':

								inv.amount = inv.amount - data.balance;
								inv.balance = inv.balance - data.balance;

								break;

						}

						sb.data.db.obj.update('invoices', inv, function(updated){

							if(updated){

								sb.notify({
									type:'object-invoice-created',
									data:{
										object:updated
									}
								});

								sb.dom.alerts.alert('Success!', '', 'success');

							}

						});

					});

				}

			});

		},

		choosePaymentMethod: function(data){

			data.modal.body.makeNode('formBreak', 'lineBreak', {spaces:1});

			data.modal.body.makeNode('form', 'form', createPaymentForm(data.type, data.object));

			data.modal.footer.makeNode('button', 'button', {text:'Submit Payment <i class="fa fa-arrow-right"></i>'}).notify('click', {
				type:'submit-invoice-payment',
				data:{
					type:data.type,
					object:data.object,
					modal:data.modal,
					form:data.modal.body.form
				}
			}, sb.moduleId);

			data.modal.body.patch();
			data.modal.footer.patch();

		},

		closePreview: function(data){

			delete data.domObj.break;
			delete data.domObj.invoiceViewer;
			delete data.domObj.btnGroup;

			data.domObj.patch();

		},

		createBalance: function(data){

			sb.data.db.obj.getAll('invoice_type', function(invoiceTypes){

				var formObj = {
						type:{
							type:'select',
							name:'type',
							label:'Type',
							options:[]
						},
						name:{
							type:'string',
							name:'name',
							label:'Name'
						},
						dueDate:{
							type:'date',
							name:'dueDate',
							label:'Due Date'
						},
						memo:{
							type:'textbox',
							name:'memo',
							label:'Memo'
						},
						amount:{
							type:'hidden',
							name:'amount',
							value:(data.balance * -1)
						},
						paid:{
							type:'hidden',
							name:'paid',
							value:0
						},
						type_id:{
							type:'hidden',
							name:'type_id',
							value:data.type_id
						}
					};

				_.each(invoiceTypes, function(type){

					formObj.type.options.push({
						name:type.invoice_type,
						value:type.id
					});

				});

				data.modals.makeNode('create', 'modal', {});

				data.modals.create.body.makeNode('form', 'form', formObj);

				data.modals.create.footer.makeNode('button', 'button', {text:'Save <i class="fa fa-floppy-o"></i>'}).notify('click', {
					type:'save-invoice',
					data:{
						form:data.modals.create.body.form,
						object:data.object,
						modal:data.modals.create,
						type:'balance'
					}
				}, sb.moduleId);

				data.modals.patch();

				data.modals.create.show();

			}, 1);

		},

		createFromForm: function(data){

			var formData = data.form.process(),
				saveObj = {};

			_.each(formData.fields, function(obj, k){

				if(k == 'invoice_type_list'){
					obj.value = +obj.value;
				}

				saveObj[k] = obj.value;

			});

			saveObj.balance = saveObj.amount;
			saveObj.locked = 'not-locked';

			sb.data.db.obj.create('invoices', saveObj, function(newObj){

				if(data.hasOwnProperty('modal')){

					data.modal.hide();

				}

				sb.notify({
					type:'object-invoice-created',
					data:{
						object:newObj
					}
				});

			}, 1);

		},

		createInvoices: function(data){

			var invoices = [],
				pricing = Object.assign({}, price),
				hashCode = function(s){
					return s.split("").reduce(function(a,b){a=((a<<5)-a)+b.charCodeAt(0);return a&a},0);
				},
				i = 100;

			_.each(_.sortBy(data.object.templates, 'payment_type'), function(temp){

				var idHash = CryptoJS.MD5( (objectId+i).toString() ).toString(CryptoJS.enc.Hex);

				i++;

				var invoiceObj = {
							amount:0,
							balance:0,
							due_date: moment(),
							invoice_type_list:0,
							name:'',
							template:temp,
							type:'',
							type_id:objectId,
							id_hash:idHash,
							locked:'not-locked'
						},
					catTotals = 0;

				_.each(temp.menu_item_category, function(cat){

					catTotals += pricing[cat.id];

				});

				invoiceObj.name = temp.invoice_type.invoice_type;
				invoiceObj.invoice_type_list = temp.invoice_type.id;
				invoiceObj.type = temp.invoice_type.invoice_type;

				// set amount and balance
				switch(temp.payment_type){

					case 'flatRate':

						invoiceObj.amount = +temp.flat_rate;
						invoiceObj.balance = +temp.flat_rate;

						pricing[temp.menu_item_category[0].id] = pricing[temp.menu_item_category[0].id] - +temp.flat_rate;

						break;

					case 'percentOfTotal':

						invoiceObj.amount = catTotals * (+temp.percent_of_total / 100);
						invoiceObj.balance = catTotals * (+temp.percent_of_total / 100);

						_.each(temp.menu_item_category, function(cat){

							pricing[cat.id] = pricing[cat.id] - (pricing[cat.id] * (+temp.percent_of_total / 100));

						});

						break;

					case 'remainingBalance':

						var curTotal = 0,
							catInvoices = [];

						invoiceObj.amount = catTotals;
						invoiceObj.balance = catTotals;

						_.each(temp.menu_item_category, function(cat){

							pricing[cat.id] = pricing[cat.id] - catTotals;

						});

						break;
				}

				// set due date
				switch(temp.before_after){

					case 'after':

						invoiceObj.due_date = moment(dueDate).add(temp.due_date, 'day').format();

						break;

					case 'before':

						invoiceObj.due_date = moment(dueDate).subtract(temp.due_date, 'day').format();

						break;

				}

				invoices.push(invoiceObj);

			});

			sb.data.db.obj.create('invoices', invoices, function(newInvoices){

				setTimeout(function(){

					sb.notify({
						type: 'show-object-invoice-list',
						data: {
							domObj: sb.dom.make(domObj.selector),
							objectId: objectId,
							dueDate:dueDate,
							price:price
						}
					});

				}, 0);

			});

		},

		createObjectInvoiceForm: function(data){

			sb.data.db.obj.getBlueprint('invoices', function(bp){

				var formFields = {
						invoice_type_list:{
							type:'select',
							name:'invoice_type_list',
							label:'Invoice Type',
							options:[]
						},
						name:{
							type:'string',
							name:'name',
							label:'Name'
						},
						amount:{
							type:'usd',
							name:'amount',
							label:'Amount'
						},
						due_date:{
							type:'date',
							name:'due_date',
							label:'Due date'
						},
						memo:{
							type:'textbox',
							name:'memo',
							label:'Memo'
						},
						type_id:{
							type:'hidden',
							name:'type_id',
							value:data.objectId
						}

					};

				_.each(bp.invoice_type_list.options, function(v, k){

					formFields.invoice_type_list.options.push({
						name:v,
						value:k
					});

				});

				data.modal.makeNode('create', 'modal', {}).body.makeNode('form', 'form', formFields);

				data.modal.create.footer.makeNode('button', 'button', {text:'Create <i class="fa fa-plus"></i>'}).notify('click', {
					type:'save-new-object-invoice-from-form',
					data:{
						form:data.modal.create.body.form,
						modal:data.modal.create
					}
				}, sb.moduleId);

				data.modal.patch();

				data.modal.create.show();

			}, 1);

		},

		deleteAllInvoices: function(data){

			var payments = _.pluck(invoiceList, 'payments'),
				paid = false;

			_.each(payments, function(pymt){

				if(pymt > 0){
					paid = true;
				}

			});

			if(paid == true){

				sb.dom.alerts.alert('', 'You cannot delete all invoices because payments have already been made. Please make adjustments to individual invoices if you need to make changes.', 'error');

			}else{

				sb.dom.alerts.ask({
					title: 'Are you sure?',
					text: 'This will delete the current invoices.',
					closeOnConfirm:true
				}, function(resp){

					if(resp){

						sb.data.db.obj.getWhere('invoices', {type_id:objectId}, function(invoices){

							var i = 0;
							_.each(invoices, function(inv){

								sb.data.db.obj.erase('invoices', inv.id, function(done){

									i++;

									if(i == invoices.length){

										sb.notify({
						                    type:'create-initial-object-invoices',
						                    data:{}
					                    });

									}

								});

							});


						});

					}

				});

			}

		},

		download: function(data){

			sb.data.makePDF(createHTMLString(data.object, data.event, data.contact), 'D');

		},

		edit: function(data){

			sb.data.db.obj.getAll('invoice_type', function(invoiceTypes){

				var formObj = {
						type:{
							type:'select',
							name:'type',
							label:'Type',
							options:[]
						},
						name:{
							type:'string',
							name:'name',
							label:'Name',
							value:data.object.name
						},
						amount:{
							type:'usd',
							name:'amount',
							label:'Amount',
							value:data.object.amount
						},
						dueDate:{
							type:'date',
							name:'dueDate',
							label:'Due Date',
							value:data.object.due_date
						},
						memo:{
							type:'textbox',
							name:'memo',
							label:'Memo',
							value:data.object.memo
						}
					};

				_.each(invoiceTypes, function(type){

					var opt = {
							name:type.invoice_type,
							value:type.id
						};

					if(data.object.type == type.invoice_type){
						opt.selected = true;
					}

					formObj.type.options.push(opt);

				});

				data.modals.makeNode('edit', 'modal', {});

				data.modals.edit.body.makeNode('form', 'form', formObj);

				data.modals.edit.footer.makeNode('button', 'button', {text:'Save <i class="fa fa-floppy-o"></i>'}).notify('click', {
					type:'save-invoice',
					data:{
						form:data.modals.edit.body.form,
						object:data.object,
						modal:data.modals.edit,
						type:'update'
					}
				}, sb.moduleId);

				data.modals.patch();

				data.modals.edit.show();

			}, 1);

		},

		emailInvoice: function(data){

			var invoiceLink = 'https://pagoda.voltz.software/app/'+ appConfig.instance +'/invoices/'+ data.object.id_hash;

			sb.data.db.obj.getById('events', objectId, function(obj){

				sb.data.db.obj.getWhere('contacts', {id:obj.main_contact, childObjs:1}, function(contact){

					var email = _.where( contact[0].contact_info, {is_primary:'yes', title:'Email'} )[0].info;

					comps.sendEmail = sb.createComponent('EmailComponent');
					comps.sendEmail.notify({
							type: 'get-send-email-modal',
							data: {
								container: data.modals,
								subject: 'New Invoice: '+ data.object.name,
								to: email,
								message: 'Here is a link to your invoice. Click this link to view, pay and download a copy of your invoice. '+invoiceLink,
								objectType: 'event',
								objectId: obj.main_contact,
								replyType: 'new'
							}
						}
					);

				});

			});

		},

		generateObjectStatement: function(data){

			build_statement(data.selected, data.ui, data.state, data.onComplete);

		},

		paymentCompleted: function(data){

			data.action(function(dom, obj){

				var totalPaid = 0;

				if(obj.paid > 0){
					totalPaid = obj.paid;
				}

				if(!obj.payments){
					obj.payments = [];
				}

				if(!obj.payments.length){
					obj.payments = [];
				}

				totalPaid += +data.payment.amount;

				obj.payments.push(data.payment.id);

				var newBalance = obj.amount - totalPaid;
				var totalFees = data.fees + obj.fees;

				var newMemo = obj.memo + '<br />Payment for $'+ (data.payment.amount/100).formatMoney(2) +' was made on '+ moment().format('M/D/YYYY @ h:mm a') +'.';

				var noteBody = 'Payment for $'+ (data.payment.amount/100).formatMoney(2) +' was made on '+ moment().format('M/D/YYYY @ h:mm a') +'.';

				sb.data.db.controller('updateObject&api_webform=true&pagodaAPIKey=invoices', {objectType:'invoices', objectData:{id:obj.id, payments:obj.payments, paid:totalPaid, balance:newBalance, fees:obj.fees, memo:newMemo, childObjs:4}}, function(updatedInv){

					sb.data.db.controller('getObjectById&api_webform=true&pagodaAPIKey='+ appConfig.instance, {value:updatedInv.id, type:'invoices', childObjs:4}, function(updated){

						var objectId = updated.main_contact.id,
							emailObjectType = 'contacts',
							emailAddress = '';

						if(updated.main_contact){
							if(updated.main_contact.contact_info){

								var count = 0;
								_.each(updated.main_contact.contact_info, function(i){

									if(i.type.data_type == 'email' && i.is_primary == 'yes'){

										if(count > 0){

											emailAddress += ', '+ i.info;

										}else{

											emailAddress += i.info;

										}

										count++;

									}

								});

							}
						}

						var noteObj = {
								type_id: updated.id,
								type: 'invoices',
								note: noteBody,
								record_type:'log',
								author: sb.data.cookie.get('uid'),
								notifyUsers: []
							};

						sb.data.db.controller('createNewObject&api_webform=true&pagodaAPIKey='+ appConfig.instance, {objectType:'notes', objectData:noteObj}, function(newNote){

							var emailObj = {
									newThread:true,
									to:emailAddress,
									from: appConfig.emailFrom,
									subject: appConfig.systemName + ' payment receipt',
									mergevars: {
										TITLE: appConfig.systemName + ' payment receipt',
										BODY: 'Thank you for your payment of $'+ (data.payment.amount/100).formatMoney(2) +'<br /><br />Please click the link below to view and download a copy of the invoice for your records.<br /><br /><a href="https://pagoda.voltz.software/app/invoices/#?&i='+ appConfig.instance +'&iid='+ updated.id +'" target="_blank">https://pagoda.voltz.software/app/invoices/#?&i='+ appConfig.instance +'&iid='+ updated.id +'</a>',
										BUTTON: ''
									}, emailtags: [
										'Invoice Payment Receipt'
									],
									type: emailObjectType,
									typeId: objectId,
								};

							sb.dom.alerts.alert('Success!', '', 'success');

							dom.modals.modal.hide();

							sb.notify({
								type:'app-redraw',
								data:{}
							});

							if(!_.isEmpty(paymentCompletion)){

								paymentCompletion.data.invoice = updated;
								paymentCompletion.data.payment = data.payment;

								sb.notify({
									type:paymentCompletion.type,
									data:paymentCompletion.data
								});

							}

							sb.comm.sendEmail(emailObj, function(response){});

						}, adminURL+'/api/_getAdmin.php?do=');

					}, adminURL+'/api/_getAdmin.php?do=');

				}, adminURL+'/api/_getAdmin.php?do=');

			});

		},

		paymentReceived: function(data){

			if(comps.hasOwnProperty('table')){

				comps.table.notify({
					type:'show-object-invoice-list',
					data:{}
				});

			}

			sb.dom.alerts.alert('Success!', 'Your payment has been successfully processed. A receipt will be emailed in a few minutes.', 'success');

		},

		run: function(data){data.run();},

		save: function(data){

			if(data.hasOwnProperty('modal')){

				data.modal.footer.makeNode('button', 'button', {text:'Saving <i class="fa fa-circle-o-notch fa-spin"></i>'});

				data.modal.footer.patch();

			}

			var formInfo = data.form.process();

			switch(data.type){

				case 'balance':

					var dbCall = 'create',
						update = {
							type_id:formInfo.fields.type_id.value,
							amount:formInfo.fields.amount.value,
							balance:formInfo.fields.amount.value,
							paid:0,
							due_date:formInfo.fields.dueDate.value,
							memo:formInfo.fields.memo.value,
							name:formInfo.fields.name.value,
							invoice_type_list: +formInfo.fields.type.value
						};

					break;

				default:

					var dbCall = 'update',
						update = {
							id:data.object.id,
							amount:formInfo.fields.amount.value,
							due_date:formInfo.fields.dueDate.value,
							memo:formInfo.fields.memo.value,
							name:formInfo.fields.name.value,
							invoice_type_list: +formInfo.fields.type.value
						};

					update.balance = update.amount - data.object.paid;

			}

			sb.data.db.obj[dbCall]('invoices', update, function(updated){

				if(data.hasOwnProperty('modal')){

					data.modal.hide();

				}

				sb.notify({
					type:'object-invoice-created',
					data:{
						object:updated
					}
				});

			});

		},

		selectPaymentSchedule: function(data){

			delete domObj.info;
			delete domObj.table.btnGroup;

			domObj.table.makeNode('container', 'panel', {header:'Choose a payment template', css:'pda-panel-primary'});

			domObj.table.container.body.makeNode('tableContainer', 'container', {});

			domObj.build();

			comps.table = sb.createComponent('crudPaged');
			comps.table.notify({
				type: 'display-crud-table-paged',
				data: {
					domObj: domObj.table.container.body.tableContainer,
					objectType:'payment_schedule_template',
					visibleCols: ['name', 'total_invoices'],
					data: function(callback, paged){

						var where = {
								paged:paged,
								childObjs:1
							};

						sb.data.db.obj.getAll('payment_schedule_template', function(objs){

							callback(objs);

						}, 1, paged);



					},
					cols:{
						name:'Payment Template Name',
						total_invoices:'Invoice Details'
					},
					cells: {
						total_invoices: function(obj){

							var invoiceDetails = '';
							_.each(obj.templates, function(temp){

								var amount = '',
									categories = '';

								var i = 0;
								_.each(temp.menu_item_category, function(cat){

									if(i > 0){
										categories += ', '+cat.menu_item_category;
									}else{
										categories += ' '+cat.menu_item_category;
									}

									i++;

								});

								switch(temp.payment_type){

									case 'flatRate':

										amount = '$'+(temp.flat_rate/100).toFixed(2);

										break;

									case 'percentOfTotal':

										amount = temp.percent_of_total +'%';

										break;

									case 'remainingBalance':

										amount = 'Remaining balance';

										break;

								}

								invoiceDetails += '<span class="text-bold">'+ temp.invoice_type.invoice_type +':</span> '+ amount +' of'+' '+ categories +' '+' due '+ temp.due_date +' day(s) '+ temp.before_after +' start<br />';

							});

							return invoiceDetails;

							return obj.templates.length;
						}
					},
					buttons: {
						edit:false,
						erase:false,
						hideHeaderButtons:true,
						continue:{
							type:'payment-schedule-selected',
							data:{}
						}
					}
				}
			});

		},

		showAll: function(data){

			if(data.simple){
				setupOptions.simple = data.simple;
			}

			if(data.hasOwnProperty('objectId')){
				objectId = data.objectId;
			}

			if(data.hasOwnProperty('mainContact')){
				mainContactId = data.mainContact;
				contactId = data.mainContact
			}

			if(data.hasOwnProperty('dueDate')){
				dueDate = moment(data.dueDate);
			}

			if(data.hasOwnProperty('settingsView')){
				setupOptions.settingsView = data.settingsView;
			}

			if(data.settings){

				_.each(data.settings, function(v,k){

					setupOptions[k] = v;

				});

			}

			if(data.hasOwnProperty('price')){

				price = data.price;

				priceTotal = totalPrice(price);

			}

			buildUI(data.domObj);

			tableUI.state();

			tableUI.state.show();

		},

		showBalanceUI: function(data){

			if(mainContactId > 0 && balanceUI.state.show){

				sb.data.db.obj.getWhere('invoices', {related_object:objectId, childObjs:2}, function(ret){

					invoiceList = ret;

					balanceUI.state.show(invoiceList);

				});

			}

		},

		showContactView: function(data){

			if(data.objectId){
				objectId = data.objectId;
			}

			if(data.main_contact){
				mainContactId = data.main_contact;
			}

			if(data.simple){
				setupOptions.simple = data.simple;
			}

			buildContactUI(data.domObj);

			contactUI.state();

			contactUI.state.show(mainContactId);

		},

		showContactInvoices: function(data){

			comps.table = sb.createComponent('crudPaged');

			if(data){

				if(data.domObj){
					domObj = sb.dom.make(data.domObj.selector);
				}

				if(data.hasOwnProperty('objectId')){
					objectId = data.objectId;
				}

				if(data.hasOwnProperty('dueDate')){
					dueDate = moment(data.dueDate);
				}

			}

			domObj.makeNode('modals', 'container', {});
			domObj.makeNode('info', 'container', {css:'pull-left'});
			domObj.makeNode('table', 'column', {width:12});
			domObj.makeNode('preview', 'column', {width:12});
			domObj.makeNode('finalBreak', 'lineBreak', {spaces:1});

			domObj.build();

			comps.table.notify({
				type: 'display-crud-table-paged',
				data: {
					domObj: domObj.table,
					objectType:'invoices',
					visibleCols: ['name', 'invoice_type_list', 'due_date', 'amount', 'paid', 'balance', 'memo', 'sent_info'],
					data: function(callback, paged){

						sb.data.db.obj.getWhere('invoices', {type_id:objectId, childObjs:1, paged:paged}, function(invoices){

							callback(invoices);

						});

					},
					cols:{
						amount:'Total',
						sent_info:'Sent?'
					},
					cells: {
						amount: function(obj){
							return '$'+(obj.amount / 100).toFixed(2);
						},
						paid: function(obj){
							return '$'+(obj.paid / 100).toFixed(2);
						},
						balance: function(obj){

							var balance = ( (obj.amount - obj.paid) / 100);

							if(balance == 0){

								var ret = '<span class="text-success text-bold">$'+ Math.abs(balance).toFixed(2) +'</span>';

							}

							if(balance < 0){

								var ret = '<span class="text-warning text-bold">$'+ Math.abs(balance).toFixed(2) +'</span>';

							}

							if(balance > 0){

								var ret = '<span class="text-danger text-bold">($'+ Math.abs(balance).toFixed(2) +')</span>';

							}

							return ret;
						},
						due_date:function(obj){
							return moment(obj.due_date).format('MMMM Do YYYY');
						},
						memo:function(obj){
							if(obj.memo){
								return obj.memo;
							}else{
								return 'No memo';
							}
						},
						sent_info:function(obj){

							if(_.isEmpty(obj.sent_by)){
								return 'Not sent'
							}else{
								return 'Sent on '+ moment(obj.sent_on).format('MMMM Do YYYY') +' by '+ obj.sent_by.fname +' '+ obj.sent_by.lname;
							}

							return 'test';
						}
					},
					headerButtons:[
						{
							text: 'Pay Multiple Invoices',
			                action: function ( e, dt, node, config ) {
			                    sb.notify({
									type:'begin-invoice-payment',
									data:{
										modals:domObj.modals,
										object:invoiceList
									}
								});
			                }
						},
						{
							text: 'Choose a New Schedule',
			                action: function ( e, dt, node, config ) {
			                    sb.notify({
				                    type:'delete-all-invoices',
				                    data:{}
			                    });
			                }
						}
					],
					buttons: {
						create: {
							type:'create-object-invoice',
							data:{
								objectId:objectId,
								modal:domObj.modals
							}
						},
						view: {
							type:'view-invoice-preview',
							data:{
								domObj:domObj.preview,
								objectId:objectId,
								modals:domObj.modals
							}
						},
						email: {
							type:'email-invoice',
							data:{
								domObj:domObj,
								modals:domObj.modals
							}
						},
						pay: function(obj){

							if(obj.balance > 0){

								return {
										type:'begin-invoice-payment',
										data:{
											modals:domObj.modals
										}
									};

							}

						},
						edit: {
							type:'edit-invoice',
							data:{
								modals:domObj.modals
							}
						},
						erase:  function(obj){

							if(obj.paid == 0){

								return true;

							}

						},
						colVis:false,
						print:false,
						excel:false,
						csv:false,
						filter:false
					}
				}
			});

		},

		showMenuInvoiceList: function(data){

			function tableLoading(data, off){

				if(!off){

					$(data.dom.addInvoiceCont.cont.manage.selector).addClass('disabled');
					//$(data.dom.addInvoiceCont.cont.addInvoice.selector).addClass('disabled');

					_.each(_.sortBy(data.setup.invoices, 'due_date'), function(inv){

						$(data.dom['payment-'+inv.id].selector).addClass('disabled');

					});

				}else{

					$(data.dom.addInvoiceCont.cont.manage.selector).removeClass('disabled');
					//$(data.dom.addInvoiceCont.cont.addInvoice.selector).removeClass('disabled');

					_.each(_.sortBy(data.setup.invoices, 'due_date'), function(inv){

						$(data.dom['payment-'+inv.id].selector).removeClass('disabled');

					});

				}



			}

			function printTable(data){

				data.dom.empty();

				var totalBalance = 0;
				var totalPaid = 0;
				var totalAmount = 0;
				var paymentIds = [];
				var relatedObject = 0;

				_.each(data.setup.invoices, function(inv){

					paymentIds = paymentIds.concat(inv.payments);

				});

				if (!_.isEmpty(data.setup.invoices)) {

					if(data.setup.invoices[0].related_object){
						relatedObject = data.setup.invoices[0].related_object;
					}

				}

				var queries = [
						{
							responseName: 'invoicePayments'
							, table: 'payments'
							, query: {
								id: {
									type: 'or'
									, values: paymentIds
								}
							}
							, childObjs: 0
						}
						, {
							responseName: 'allPayments'
							, table: 'payments'
							, query: {
								main_object: relatedObject
								, status: {
									'type': 'not_equal'
									, 'value': 'Returned'
								}
							}
							, childObjs: 0
						}
					];

				sb.data.db.service("DataRepository", "get", queries, function (response) {

					var invoicePayments = response.invoicePayments;
					var allPayments = response.allPayments;

					var paym = [];

					if ( invoicePayments !== null )
						paym = paym.concat(invoicePayments);

					if ( allPayments !== null )
						paym = paym.concat(allPayments);

					allPayments = allPayments.concat(invoicePayments);
					allPayments = _.uniq(allPayments, 'id');
					allPayments = _.compact(allPayments);

					paym = _.uniq(paym, 'id');

					_.each(paym, function(payment){

						if (payment !== null && payment.status !== 'Returned') {
							totalPaid += payment.amount;
						}

					});

					_.each(_.sortBy(data.setup.invoices, 'due_date'), function(inv){

						var subTitle = '';

						var rowColor = '';

						if(moment(inv.due_date) < moment().subtract(1, 'day')){
							rowColor = 'red';
						}

						if(inv.balance <= 0){
							rowColor = 'positive';
						}

						var lockedIcon = '';
						if(inv.locked == 'locked'){
							lockedIcon = '<i class="ui fitted lock icon"></i> ';
						}

						if(inv.invoice_template && data.setup.object.invoice_template > 0){
							lockedIcon = '<i class="ui fitted lock icon"></i> ';
						}

						if(inv.invoice_template && data.setup.object.invoice_template > 0){
							//rowColor = 'red';
							//subTitle = '<p class="ui small sub header" style="text-transform:inherit;">Locked - Part of a payment schedule template.</p>';
						}

						totalBalance = totalBalance + inv.balance;
						totalAmount += inv.amount;

						data.dom.makeNode('payment-'+inv.id, 'div'
							, {
								tag:'tr'
								, css:rowColor
							}
						);

						data.dom['payment-'+inv.id].makeNode('nameCell', 'div', {tag:'td'});
						data.dom['payment-'+inv.id].nameCell.makeNode('name', 'div', {text:lockedIcon + inv.name +' due on '+ moment(inv.due_date).format('MM/DD/YYYY') + subTitle});
						data.dom['payment-'+inv.id].nameCell.makeNode('invoiceNumber', 'div', {text: 'Invoice # ' + inv.related_object + '-' +inv.id});
						data.dom['payment-'+inv.id].nameCell.makeNode('btns', 'div', {css:'ui mini compact buttons'});
						data.dom['payment-'+inv.id].makeNode('paid', 'div', {tag:'td', style:'text-align:right;', text:'<b>Paid $'+ ((inv.amount - inv.balance)/100).formatMoney() +' of $'+ (inv.amount/100).formatMoney() +'</b>'});

						if(data.setup.object.invoice_template == 0){

							data.dom['payment-'+inv.id].nameCell.btns.makeNode('edit', 'div', {css:'ui orange button', text:'Edit'});
							data.dom['payment-'+inv.id].nameCell.btns.edit.notify('click', {
								type:'invoicesRun',
								data:{
									run:function(selectedInvoice){

										data.dom.makeNode('modal', 'modal', {
											onShow:function(){

												$(data.dom.modal.body.selector).addClass();

												data.dom.modal.body.makeNode('loading', 'div', {css:'ui basic padded segment'})
													.makeNode('loading', 'div', {css:'ui active inverted dimmer'})
													.makeNode('loading', 'div', {css:'ui text loader', text:'Loading'});

												data.dom.modal.body.patch();

												sb.data.db.obj.getBlueprint('invoices', function(bp){

													data.clientId = data.setup.object.main_object.main_contact.company;
													data.objectType = 'proposal';
													data.objectId = data.setup.object.id;
													data.contactId = data.setup.object.main_object.main_contact.id;
													data.project = data.setup.object.main_object;

													editInvoice.call(data, bp, data.dom.modal.body, selectedInvoice, function(dom){

														data.dom.modal.body.patch();

													}, function(newObj){

														tableLoading(data);

														data.dom.modal.hide();

														if(data.onUpdate && newObj){

															//data.setup.invoices.push(newObj);

															data.onUpdate(data);

															//printTable(data);

														}else{

															tableLoading(data, true);

														}

													});

												}, 1);

											}
										});

										data.dom.patch();

										data.dom.modal.show();

									}.bind(null, inv)
								}
							});

							var lockedBtnString = '';
							var lockedCurrently;
							if(inv.locked == 'locked'){
								lockedBtnString = 'Unlock';
								lockedUpdateString = 'not-locked';
							}else{
								lockedBtnString = 'Lock';
								lockedUpdateString = 'locked';
							}

							data.dom['payment-'+inv.id].nameCell.btns.makeNode('lock', 'div', {css:'ui blue button', text:lockedBtnString});
							data.dom['payment-'+inv.id].nameCell.btns.lock.notify('click', {
								type:'invoicesRun',
								data:{
									run:function(selectedInvoice, lockedUpdateString){

										$(data.dom['payment-'+inv.id].nameCell.btns.lock.selector).addClass('loading');

										sb.data.db.obj.update('invoices',{id:selectedInvoice.id, locked:lockedUpdateString}, function(newObj){

											if(data.onUpdate && newObj){

												data.onUpdate(data);

											}else{

												tableLoading(data, true);

											}

										});

									}.bind(null, inv, lockedUpdateString)
								}
							});

						}

						//console.log(appConfig.instance);

						if (inv["balance"] !== 0){

							data.dom['payment-'+inv.id].nameCell.btns.makeNode('portal', 'div', {
								tag:'a',
								href:sb.url+'/app/invoices#?&i='+ appConfig.instance +'&iid='+ inv.id,
								css:'ui teal button',
								text:'Pay Now',
								target:'_blank'
							});

							//#2366 - 800 | Add 'Send Email' button to NLP and Dream
							if (appConfig.instance === 'infinity' || appConfig.instance === 'dreamcatering' || appConfig.instance === 'nlp' || appConfig.instance === 'rickyvoltz') {

								var btnText =  inv.sent_on ? 'Re-send Email - Last Sent on '+ moment(inv.sent_on).local().format('M/D/YYYY h:mm a') : 'Send Email';

								// send invoice button
								data.dom['payment-'+inv.id].nameCell.btns.makeNode('sendInvoice', 'div', {css:'ui violet button', text: btnText});
								data.dom['payment-'+inv.id].nameCell.btns.sendInvoice.notify('click', {
									type:'invoicesRun',
									data:{
										run:function(selectedInvoice){

											selectedInvoice.tagged_with = [data.state.project.id];

											$(data.dom['payment-'+inv.id].nameCell.btns.sendInvoice.selector).addClass('loading');

											sb.data.db.service("InvoicesService", "sendSingleInvoices", {invoice: selectedInvoice.id}, function (res) {

												sb.data.db.obj.update('invoices', {id: inv.id, sent: 'Yes', sent_on: moment().format(), sent_by: +sb.data.cookie.userId}, function(updated){

													$(data.dom['payment-'+inv.id].nameCell.btns.sendInvoice.selector).removeClass('loading').text('Re-send Email - Last Sent on '+ moment(updated.sent_on).local().format('M/D/YYYY h:mm a'));

													sb.dom.alerts.alert('Invoice Sent!', "This invoice has been sent to the project's main contact.", 'success');

												});

											});

										}.bind(null, inv)
									}
								});
							}
						}
					});

					data.dom.makeNode('total', 'div'
						, {
							tag:'tr'
							, text:'<td class="right aligned"></td>'+
								'<td class="right aligned"><b>Total Amount $'+ (totalAmount/100).formatMoney() +'</b></td>'
						});

					data.dom.makeNode('paid', 'div'
						, {
							tag:'tr'
							, text:'<td class="right aligned"></td>'+
								'<td class="right aligned"><b>Total Paid $'+ (totalPaid/100).formatMoney() +'</b></td>'
						});

					var balanceText = 'Balance';
					if(totalAmount - totalPaid < 0){
						balanceText = 'Credit';
					}

					data.dom.makeNode('balance', 'div'
						, {
							tag:'tr'
							, text:'<td class="right aligned"></td>'+
								'<td class="right aligned"><b>'+ balanceText +' $'+ ((totalAmount - totalPaid)/100).formatMoney() +'</b></td>'
						});

					data.dom.makeNode('addInvoiceCont', 'div', {css:'', tag:'tr'})
						.makeNode('addInvoiceSpacer', 'div', {tag:'td'});

					data.dom.addInvoiceCont.makeNode('cont', 'div', {tag:'td', css:'right aligned'})

					if(data.setup.object.invoice_template > 0){

						data.dom.addInvoiceCont.cont.makeNode('removeTemplate', 'div', {css:'ui compact mini red basic button', text:'Disable Template'})
							.notify('click', {
								type:'invoicesRun',
								data:{
									run:function(){

										sb.dom.alerts.ask({
											title: 'Are you sure?',
											text: 'Turning off the template will allow you to edit the payment schedules.'
										}, function(resp){

											if(resp){

												swal.disableButtons();

												//tableLoading(data);

												sb.data.db.obj.update('proposals', {id:data.setup.object.id, invoice_template:0}, function(updatedProposal){

													data.setup.object.invoice_template = 0;

													sb.dom.alerts.ask({
														title: 'Delete current invoices?',
														text: 'Do you want to delete the current invoices or keep them? If you keep them, you\'ll be able to edit them once the template is turned off.'
													}, function(resp){

														if(resp){

															swal.disableButtons();

															sb.data.db.obj.erase('invoices', _.pluck(data.setup.invoices, 'id'), function(){

																if(data.onUpdate){

																	data.setup.invoices = [];

																	data.onUpdate(data);

																}

																swal.close();

															});

														}else{

															var invoicesToUpdate = [];

															_.each(data.setup.invoices, function(inv){
																invoicesToUpdate.push({
																	id:inv.id,
																	locked:'locked'
																});
															});

															sb.data.db.obj.update('invoices', invoicesToUpdate, function(){

																swal.close();

																if(data.onUpdate){

																	data.onUpdate(data);

																}

															});

														}

													});

												});

											}

										});

									}

								}

							});

					}

					data.dom.addInvoiceCont.cont.makeNode('applyTemplate', 'div', {css:'ui compact mini teal button', text:'Apply Template'})
						.notify('click', {
							type:'invoicesRun',
							data:{
								run:function(){

									data.dom.makeNode('modal', 'modal', {
										onShow:function(){

											data.dom.modal.body.makeNode('loading', 'div', {css:'ui basic padded segment'})
												.makeNode('loading', 'div', {css:'ui active inverted dimmer'})
												.makeNode('loading', 'div', {css:'ui text loader', text:'Loading'});

											data.dom.modal.body.patch();

											sb.data.db.obj.getAll('payment_schedule_template', function(templates){

												data.dom.modal.body.empty();

												data.dom.modal.body.makeNode('table', 'table', {
													css: 'ui single line striped basic table',
													clearCSS:true,
													columns: {
														name: 'Template Name',
														total_invoices: 'Invoices that will be created',
														btns: ''
													}
												});

												_.each(templates, function(obj){

													var invoiceDetails = '<i>No invoices associated with this template</i>';

													if(obj.templates.length > 0){
														invoiceDetails = '';
													}

													_.each(obj.templates, function(temp){

														var amount = '',
															categories = '';

														var i = 0;
														_.each(temp.menu_item_category, function(cat){

															if(i > 0){
																categories += ', '+cat.menu_item_category;
															}else{
																categories += ' '+cat.menu_item_category;
															}

															i++;

														});

														switch(temp.payment_type){

															case 'flatRate':

																amount = '$'+(temp.flat_rate/100).toFixed(2);

																break;

															case 'percentOfTotal':

																amount = temp.percent_of_total +'%';

																break;

															case 'remainingBalance':

																amount = 'Remaining balance';

																break;

														}

														var invoiceType = '<i>Type not selected</i>';
														if(temp.invoice_type){
															invoiceType = temp.invoice_type.invoice_type;
														}

														invoiceDetails += '<span class="text-bold">'+ temp.name +':</span> '+ amount +' of'+' '+ categories +' '+' due '+ temp.due_date +' day(s) '+ temp.before_after +' start<br />';

													});

													data.dom.modal.body.table.makeRow(
														'template-' + obj.id,
														[obj.name, invoiceDetails, '']
													);

													data.dom.modal.body.table.body['template-' + obj.id].btns.makeNode('select', 'button', {text:'Select and continue <i class="fa fa-arrow-right"></i>', css:'pda-btn-primary'}).notify('click', {
														type:'invoicesRun',
														data:{
															run:function(template){

																var alertText = 'This will delete current invoice(s)';

																_.each(data.setup.invoices, function(inv){

																	if (inv.payments && inv.payments.length > 0)
																		alertText = 'This will delete any payments that have already been applied to any invoices.';

																	return;
																});

																sb.dom.alerts.ask({
																	title: 'Are you sure?',
																	text: alertText
																}, function(resp){

																	if(resp){

																		swal.disableButtons();

																		tableLoading(data);

																		data.dom.modal.body.table.body['template-' + obj.id].btns.select.loading();

																		applyPaymentTemplate(data.setup.object, template, function(done){

																			data.setup.object.invoice_template = template.id;

																			sb.data.db.controller('balanceProjectInvoices', {projectId:data.state.project.id}, function(invoices){

																				data.dom.modal.hide();

																				swal.close();

																				if(data.onUpdate){

																					data.setup.invoices = invoices;

																					data.onUpdate(data);

																				}

																			}, false, true);

																		});

																	}

																});

															}.bind({}, obj)
														}
													}, sb.moduleId);

													data.dom.modal.body.patch();

												});

											});

										},
										onClose:function(){

											sb.data.db.obj.getWhere('invoices', {related_object:data.setup.object.id}, function(updatedInvoices){

												data.setup.invoices = updatedInvoices;

												printTable(data);

											});

										}

									});

									data.dom.patch();

									data.dom.modal.show();

								}
							}
						}, sb.moduleId);

					data.dom.addInvoiceCont.cont.makeNode('manage', 'div', {css:'ui compact mini teal button', text:'Manage Payments'})
						.notify('click', {
							type:'invoicesRun',
							data:{
								run:function(){

									data.dom.makeNode('modal', 'modal', {
										onShow:function(){

											tableLoading(data);
											// !@TODO show-payment-button
											sb.notify({
												type:'show-payment-button',
												data:{
													dom:data.dom.modal.body,
													notification:'invoice-payment-completed',
													admin:true,
													invoices:data.setup.invoices,
													completeCallback:function(updatedInvoices){

														data.setup.invoices = updatedInvoices;

														printTable(data);

													}
												}
											});

										},
										onClose:function(){

											sb.data.db.obj.getById('invoices', _.pluck(data.setup.invoices, 'id'), function(updatedInvoices){

												data.setup.invoices = updatedInvoices;

												printTable(data);

											});

										}

									});

									data.dom.patch();

									data.dom.modal.show();

								}
							}
						}, sb.moduleId);

					if(data.setup.object.invoice_template > 0){}else{

						data.dom.addInvoiceCont.cont.makeNode('addInvoice', 'div', {css:'ui compact mini teal button', text:'Add Schedule'})
							.notify('click', {
								type:'invoicesRun',
								data:{
									run:function(){

										data.dom.makeNode('modal', 'modal', {
											onShow:function(){

												data.dom.modal.body.makeNode('loading', 'div', {css:'ui basic padded segment'})
													.makeNode('loading', 'div', {css:'ui active inverted dimmer'})
													.makeNode('loading', 'div', {css:'ui text loader', text:'Loading'});

												data.dom.modal.body.patch();

												sb.data.db.obj.getBlueprint('invoices', function(bp){

													data.clientId = data.setup.object.main_object.main_contact.company;
													data.objectType = 'proposal';
													data.objectId = data.setup.object.id;
													data.contactId = data.setup.object.main_object.main_contact.id;
													data.project = data.setup.object.main_object;

													createInvoice.call(data, bp, data.dom.modal.body, data, function(dom){

														data.dom.modal.body.patch();

													}, function(newObj){

														tableLoading(data);

														data.dom.modal.hide();

														if(data.onUpdate){

															data.setup.invoices.push(newObj);

															data.onUpdate(data);

														}

													});

												}, 1);

											}
										});

										data.dom.patch();

										data.dom.modal.show();



									}
								}
							}, sb.moduleId);

					}

					data.dom.patch();

				});

		// 		sb.data.db.obj.getById('payments', paymentIds, function(invoicePayments){
		//
		// 			var relatedObject = 0;
		//
		// 			if (!_.isEmpty(data.setup.invoices)) {
		//
		// 				if(data.setup.invoices[0].related_object){
		// 					relatedObject = data.setup.invoices[0].related_object;
		// 				}
		//
		// 			}
		//
		// 			sb.data.db.obj.getWhere('payments'
		// 				, {
		// 					main_object:relatedObject
		// 					, status: {
		// 						'type':'not_equal',
		// 						'value': 'Returned'
		// 					}
		// 				}
		// 				, function(allPayments){
		//
		// 				var paym = [];
		//
		// 				if ( invoicePayments !== null )
		// 					paym = paym.concat(invoicePayments);
		//
		// 				if ( allPayments !== null )
		// 					paym = paym.concat(allPayments);
		//
		// 				allPayments = allPayments.concat(invoicePayments);
		// 				allPayments = _.uniq(allPayments, 'id');
		// 				allPayments = _.compact(allPayments);
		//
		// 				paym = _.uniq(paym, 'id');
		//
		// 				_.each(paym, function(payment){
		//
		// 					if (payment !== null)
		// 						totalPaid += payment.amount;
		//
		// 				});
		//
		// 				_.each(_.sortBy(data.setup.invoices, 'due_date'), function(inv){
		//
		// 					var subTitle = '';
		//
		// 					var rowColor = '';
		//
		// 					if(moment(inv.due_date) < moment().subtract(1, 'day')){
		// 						rowColor = 'red';
		// 					}
		//
		// 					if(inv.balance <= 0){
		// 						rowColor = 'positive';
		// 					}
		//
		// 					var lockedIcon = '';
		// 					if(inv.locked == 'locked'){
		// 						lockedIcon = '<i class="ui fitted lock icon"></i> ';
		// 					}
		//
		// 					if(inv.invoice_template && data.setup.object.invoice_template > 0){
		// 						lockedIcon = '<i class="ui fitted lock icon"></i> ';
		// 					}
		//
		// 					if(inv.invoice_template && data.setup.object.invoice_template > 0){
		// 						//rowColor = 'red';
		// 						//subTitle = '<p class="ui small sub header" style="text-transform:inherit;">Locked - Part of a payment schedule template.</p>';
		// 					}
		//
		// 					totalBalance = totalBalance + inv.balance;
		// 					totalAmount += inv.amount;
		//
		// 					data.dom.makeNode('payment-'+inv.id, 'div'
		// 						, {
		// 							tag:'tr'
		// 							, css:rowColor
		// 						}
		// 					);
		//
		// 					data.dom['payment-'+inv.id].makeNode('nameCell', 'div', {tag:'td'});
		// 					data.dom['payment-'+inv.id].nameCell.makeNode('name', 'div', {text:lockedIcon + inv.name +' due on '+ moment(inv.due_date).format('MM/DD/YYYY') + subTitle});
		// 					data.dom['payment-'+inv.id].nameCell.makeNode('invoiceNumber', 'div', {text: 'Invoice # ' + inv.related_object + '-' +inv.id});
		// 					data.dom['payment-'+inv.id].nameCell.makeNode('btns', 'div', {css:'ui mini compact buttons'});
		// 					data.dom['payment-'+inv.id].makeNode('paid', 'div', {tag:'td', style:'text-align:right;', text:'<b>Paid $'+ ((inv.amount - inv.balance)/100).formatMoney() +' of $'+ (inv.amount/100).formatMoney() +'</b>'});
		//
		// 					if(data.setup.object.invoice_template == 0){
		//
		// 						data.dom['payment-'+inv.id].nameCell.btns.makeNode('edit', 'div', {css:'ui orange button', text:'Edit'});
		// 						data.dom['payment-'+inv.id].nameCell.btns.edit.notify('click', {
		// 							type:'invoicesRun',
		// 							data:{
		// 								run:function(selectedInvoice){
		//
		// 									data.dom.makeNode('modal', 'modal', {
		// 										onShow:function(){
		//
		// 											$(data.dom.modal.body.selector).addClass();
		//
		// 											data.dom.modal.body.makeNode('loading', 'div', {css:'ui basic padded segment'})
		// 												.makeNode('loading', 'div', {css:'ui active inverted dimmer'})
		// 													.makeNode('loading', 'div', {css:'ui text loader', text:'Loading'});
		//
		// 											data.dom.modal.body.patch();
		//
		// 											sb.data.db.obj.getBlueprint('invoices', function(bp){
		//
		// 												data.clientId = data.setup.object.main_object.main_contact.company;
		// 												data.objectType = 'proposal';
		// 												data.objectId = data.setup.object.id;
		// 												data.contactId = data.setup.object.main_object.main_contact.id;
		// 												data.project = data.setup.object.main_object;
		//
		// 												editInvoice.call(data, bp, data.dom.modal.body, selectedInvoice, function(dom){
		//
		// 												data.dom.modal.body.patch();
		//
		// 											}, function(newObj){
		//
		// 												tableLoading(data);
		//
		// 												data.dom.modal.hide();
		//
		// 												if(data.onUpdate && newObj){
		//
		// 													//data.setup.invoices.push(newObj);
		//
		// 													data.onUpdate(data);
		//
		// 													//printTable(data);
		//
		// 												}else{
		//
		// 													tableLoading(data, true);
		//
		// 												}
		//
		// 											});
		//
		// 										}, 1);
		//
		// 										}
		// 									});
		//
		// 									data.dom.patch();
		//
		// 									data.dom.modal.show();
		//
		// 								}.bind(null, inv)
		// 							}
		// 						});
		//
		// 						var lockedBtnString = '';
		// 						var lockedCurrently;
		// 						if(inv.locked == 'locked'){
		// 							lockedBtnString = 'Unlock';
		// 							lockedUpdateString = 'not-locked';
		// 						}else{
		// 							lockedBtnString = 'Lock';
		// 							lockedUpdateString = 'locked';
		// 						}
		//
		// 						data.dom['payment-'+inv.id].nameCell.btns.makeNode('lock', 'div', {css:'ui blue button', text:lockedBtnString});
		// 						data.dom['payment-'+inv.id].nameCell.btns.lock.notify('click', {
		// 							type:'invoicesRun',
		// 							data:{
		// 								run:function(selectedInvoice, lockedUpdateString){
		//
		// 									$(data.dom['payment-'+inv.id].nameCell.btns.lock.selector).addClass('loading');
		//
		// 									sb.data.db.obj.update('invoices',{id:selectedInvoice.id, locked:lockedUpdateString}, function(newObj){
		//
		// 										if(data.onUpdate && newObj){
		//
		// 											data.onUpdate(data);
		//
		// 										}else{
		//
		// 											tableLoading(data, true);
		//
		// 										}
		//
		// 									});
		//
		// 								}.bind(null, inv, lockedUpdateString)
		// 							}
		// 						});
		//
		// 					}
		//
		// 					if (inv["balance"] !== 0){
		//
		// 						data.dom['payment-'+inv.id].nameCell.btns.makeNode('portal', 'div', {
		// 							tag:'a',
		// 							href:sb.url+'/app/invoices#?&i='+ appConfig.instance +'&iid='+ inv.id,
		// 							css:'ui teal button',
		// 							text:'Pay Now',
		// 							target:'_blank'
		// 						});
		//
		// 						if (appConfig.instance === 'infinity' || appConfig.instance === 'nlp' || appConfig.instance === 'rickyvoltz') {
		//
		// 							var btnText =  inv.sent_on ? 'Re-send Email - Last Sent on '+ moment(inv.sent_on).local().format('M/D/YYYY h:mm a') : 'Send Email';
		//
		// 							// send invoice button
		// 							data.dom['payment-'+inv.id].nameCell.btns.makeNode('sendInvoice', 'div', {css:'ui violet button', text: btnText});
		// 							data.dom['payment-'+inv.id].nameCell.btns.sendInvoice.notify('click', {
		// 								type:'invoicesRun',
		// 								data:{
		// 									run:function(selectedInvoice){
		//
		// 										selectedInvoice.tagged_with = [data.state.project.id];
		//
		// 										$(data.dom['payment-'+inv.id].nameCell.btns.sendInvoice.selector).addClass('loading');
		//
		// 										sb.data.db.service("InvoicesService", "sendSingleInvoices", {invoice: selectedInvoice.id}, function (res) {
		//
		// 											sb.data.db.obj.update('invoices', {id: inv.id, sent: 'Yes', sent_on: moment().format(), sent_by: +sb.data.cookie.userId}, function(updated){
		//
		// 												$(data.dom['payment-'+inv.id].nameCell.btns.sendInvoice.selector).removeClass('loading').text('Re-send Email - Last Sent on '+ moment(updated.sent_on).local().format('M/D/YYYY h:mm a'));
		//
		// 												sb.dom.alerts.alert('Invoice Sent!', "This invoice has been sent to the project's main contact.", 'success');
		//
		// 											});
		//
		// 										});
		//
		// 									}.bind(null, inv)
		// 								}
		// 							});
		// 						}
		// 					}
		// 				});
		//
		// 				data.dom.makeNode('total', 'div'
		// 				, {
		// 					tag:'tr'
		// 					, text:'<td class="right aligned"></td>'+
		// 							'<td class="right aligned"><b>Total Amount $'+ (totalAmount/100).formatMoney() +'</b></td>'
		// 				});
		//
		// 				data.dom.makeNode('paid', 'div'
		// 				, {
		// 					tag:'tr'
		// 					, text:'<td class="right aligned"></td>'+
		// 							'<td class="right aligned"><b>Total Paid $'+ (totalPaid/100).formatMoney() +'</b></td>'
		// 				});
		//
		// 				var balanceText = 'Balance';
		// 				if(totalAmount - totalPaid < 0){
		// 					balanceText = 'Credit';
		// 				}
		//
		// 				data.dom.makeNode('balance', 'div'
		// 				, {
		// 					tag:'tr'
		// 					, text:'<td class="right aligned"></td>'+
		// 							'<td class="right aligned"><b>'+ balanceText +' $'+ ((totalAmount - totalPaid)/100).formatMoney() +'</b></td>'
		// 				});
		//
		// 				data.dom.makeNode('addInvoiceCont', 'div', {css:'', tag:'tr'})
		// 					.makeNode('addInvoiceSpacer', 'div', {tag:'td'});
		//
		// 				data.dom.addInvoiceCont.makeNode('cont', 'div', {tag:'td', css:'right aligned'})
		//
		// 				if(data.setup.object.invoice_template > 0){
		//
		// 					data.dom.addInvoiceCont.cont.makeNode('removeTemplate', 'div', {css:'ui compact mini red basic button', text:'Disable Template'})
		// 						.notify('click', {
		// 							type:'invoicesRun',
		// 							data:{
		// 								run:function(){
		//
		// 									sb.dom.alerts.ask({
		// 										title: 'Are you sure?',
		// 										text: 'Turning off the template will allow you to edit the payment schedules.'
		// 									}, function(resp){
		//
		// 										if(resp){
		//
		// 											swal.disableButtons();
		//
		// 											//tableLoading(data);
		//
		// 											sb.data.db.obj.update('proposals', {id:data.setup.object.id, invoice_template:0}, function(updatedProposal){
		//
		// 												data.setup.object.invoice_template = 0;
		//
		// 												sb.dom.alerts.ask({
		// 													title: 'Delete current invoices?',
		// 													text: 'Do you want to delete the current invoices or keep them? If you keep them, you\'ll be able to edit them once the template is turned off.'
		// 												}, function(resp){
		//
		// 													if(resp){
		//
		// 														swal.disableButtons();
		//
		// 														sb.data.db.obj.erase('invoices', _.pluck(data.setup.invoices, 'id'), function(){
		//
		// 															if(data.onUpdate){
		//
		// 																data.setup.invoices = [];
		//
		// 																data.onUpdate(data);
		//
		// 															}
		//
		// 															swal.close();
		//
		// 														});
		//
		// 													}else{
		//
		// 														var invoicesToUpdate = [];
		//
		// 														_.each(data.setup.invoices, function(inv){
		// 															invoicesToUpdate.push({
		// 																id:inv.id,
		// 																locked:'locked'
		// 															});
		// 														});
		//
		// 														sb.data.db.obj.update('invoices', invoicesToUpdate, function(){
		//
		// 															swal.close();
		//
		// 															if(data.onUpdate){
		//
		// 																data.onUpdate(data);
		//
		// 															}
		//
		// 														});
		//
		// 													}
		//
		// 												});
		//
		// 											});
		//
		// 										}
		//
		// 									});
		//
		// 								}
		//
		// 							}
		//
		// 						});
		//
		// 				}
		//
		// 				data.dom.addInvoiceCont.cont.makeNode('applyTemplate', 'div', {css:'ui compact mini teal button', text:'Apply Template'})
		// 						.notify('click', {
		// 							type:'invoicesRun',
		// 							data:{
		// 								run:function(){
		//
		// 									data.dom.makeNode('modal', 'modal', {
		// 										onShow:function(){
		//
		// 											data.dom.modal.body.makeNode('loading', 'div', {css:'ui basic padded segment'})
		// 												.makeNode('loading', 'div', {css:'ui active inverted dimmer'})
		// 													.makeNode('loading', 'div', {css:'ui text loader', text:'Loading'});
		//
		// 											data.dom.modal.body.patch();
		//
		// 											sb.data.db.obj.getAll('payment_schedule_template', function(templates){
		//
		// 												data.dom.modal.body.empty();
		//
		// 												data.dom.modal.body.makeNode('table', 'table', {
		// 													css: 'ui single line striped basic table',
		// 													clearCSS:true,
		// 													columns: {
		// 														name: 'Template Name',
		// 														total_invoices: 'Invoices that will be created',
		// 														btns: ''
		// 													}
		// 												});
		//
		// 												_.each(templates, function(obj){
		//
		// 													var invoiceDetails = '<i>No invoices associated with this template</i>';
		//
		// 													if(obj.templates.length > 0){
		// 														invoiceDetails = '';
		// 													}
		//
		// 													_.each(obj.templates, function(temp){
		//
		// 														var amount = '',
		// 															categories = '';
		//
		// 														var i = 0;
		// 														_.each(temp.menu_item_category, function(cat){
		//
		// 															if(i > 0){
		// 																categories += ', '+cat.menu_item_category;
		// 															}else{
		// 																categories += ' '+cat.menu_item_category;
		// 															}
		//
		// 															i++;
		//
		// 														});
		//
		// 														switch(temp.payment_type){
		//
		// 															case 'flatRate':
		//
		// 																amount = '$'+(temp.flat_rate/100).toFixed(2);
		//
		// 																break;
		//
		// 															case 'percentOfTotal':
		//
		// 																amount = temp.percent_of_total +'%';
		//
		// 																break;
		//
		// 															case 'remainingBalance':
		//
		// 																amount = 'Remaining balance';
		//
		// 																break;
		//
		// 														}
		//
		// 														var invoiceType = '<i>Type not selected</i>';
		// 														if(temp.invoice_type){
		// 															invoiceType = temp.invoice_type.invoice_type;
		// 														}
		//
		// 														invoiceDetails += '<span class="text-bold">'+ temp.name +':</span> '+ amount +' of'+' '+ categories +' '+' due '+ temp.due_date +' day(s) '+ temp.before_after +' start<br />';
		//
		// 													});
		//
		// 													data.dom.modal.body.table.makeRow(
		// 														'template-' + obj.id,
		// 														[obj.name, invoiceDetails, '']
		// 													);
		//
		// 													data.dom.modal.body.table.body['template-' + obj.id].btns.makeNode('select', 'button', {text:'Select and continue <i class="fa fa-arrow-right"></i>', css:'pda-btn-primary'}).notify('click', {
		// 														type:'invoicesRun',
		// 														data:{
		// 															run:function(template){
		//
		// 																var alertText = 'This will delete current invoice(s)';
		//
		// 																_.each(data.setup.invoices, function(inv){
		//
		// 																	if (inv.payments && inv.payments.length > 0)
		// 																		alertText = 'This will delete any payments that have already been applied to any invoices.';
		//
		// 																	 return;
		// 																});
		//
		// 																sb.dom.alerts.ask({
		// 																	title: 'Are you sure?',
		// 																	text: alertText
		// 																}, function(resp){
		//
		// 																	if(resp){
		//
		// 																		swal.disableButtons();
		//
		// 																		tableLoading(data);
		//
		// 																		data.dom.modal.body.table.body['template-' + obj.id].btns.select.loading();
		//
		// 																		applyPaymentTemplate(data.setup.object, template, function(done){
		//
		// 																			data.setup.object.invoice_template = template.id;
		//
		// 																			sb.data.db.controller('balanceProjectInvoices', {projectId:data.state.project.id}, function(invoices){
		//
		// 																				data.dom.modal.hide();
		//
		// 																				swal.close();
		//
		// 																				if(data.onUpdate){
		//
		// 																					data.setup.invoices = invoices;
		//
		// 																					data.onUpdate(data);
		//
		// 																				}
		//
		// 																			}, false, true);
		//
		// 																		});
		//
		// 																	}
		//
		// 																});
		//
		// 															}.bind({}, obj)
		// 														}
		// 													}, sb.moduleId);
		//
		// 													data.dom.modal.body.patch();
		//
		// 												});
		//
		// 											});
		//
		// 										},
		// 										onClose:function(){
		//
		// 											sb.data.db.obj.getWhere('invoices', {related_object:data.setup.object.id}, function(updatedInvoices){
		//
		// 												data.setup.invoices = updatedInvoices;
		//
		// 												printTable(data);
		//
		// 											});
		//
		// 										}
		//
		// 									});
		//
		// 									data.dom.patch();
		//
		// 									data.dom.modal.show();
		//
		// 								}
		// 							}
		// 						}, sb.moduleId);
		//
		// 				data.dom.addInvoiceCont.cont.makeNode('manage', 'div', {css:'ui compact mini teal button', text:'Manage Payments'})
		// 					.notify('click', {
		// 						type:'invoicesRun',
		// 						data:{
		// 							run:function(){
		//
		// 								data.dom.makeNode('modal', 'modal', {
		// 									onShow:function(){
		//
		// 										tableLoading(data);
		// // !@TODO show-payment-button
		// 										sb.notify({
		// 											type:'show-payment-button',
		// 											data:{
		// 												dom:data.dom.modal.body,
		// 												notification:'invoice-payment-completed',
		// 												admin:true,
		// 												invoices:data.setup.invoices,
		// 												completeCallback:function(updatedInvoices){
		//
		// 													data.setup.invoices = updatedInvoices;
		//
		// 													printTable(data);
		//
		// 												}
		// 											}
		// 										});
		//
		// 									},
		// 									onClose:function(){
		//
		// 										sb.data.db.obj.getById('invoices', _.pluck(data.setup.invoices, 'id'), function(updatedInvoices){
		//
		// 											data.setup.invoices = updatedInvoices;
		//
		// 											printTable(data);
		//
		// 										});
		//
		// 									}
		//
		// 								});
		//
		// 								data.dom.patch();
		//
		// 								data.dom.modal.show();
		//
		// 							}
		// 						}
		// 					}, sb.moduleId);
		//
		// 				if(data.setup.object.invoice_template > 0){}else{
		//
		// 					data.dom.addInvoiceCont.cont.makeNode('addInvoice', 'div', {css:'ui compact mini teal button', text:'Add Schedule'})
		// 						.notify('click', {
		// 							type:'invoicesRun',
		// 							data:{
		// 								run:function(){
		//
		// 									data.dom.makeNode('modal', 'modal', {
		// 										onShow:function(){
		//
		// 											data.dom.modal.body.makeNode('loading', 'div', {css:'ui basic padded segment'})
		// 												.makeNode('loading', 'div', {css:'ui active inverted dimmer'})
		// 													.makeNode('loading', 'div', {css:'ui text loader', text:'Loading'});
		//
		// 											data.dom.modal.body.patch();
		//
		// 											sb.data.db.obj.getBlueprint('invoices', function(bp){
		//
		// 												data.clientId = data.setup.object.main_object.main_contact.company;
		// 												data.objectType = 'proposal';
		// 												data.objectId = data.setup.object.id;
		// 												data.contactId = data.setup.object.main_object.main_contact.id;
		// 												data.project = data.setup.object.main_object;
		//
		// 												createInvoice.call(data, bp, data.dom.modal.body, data, function(dom){
		//
		// 													data.dom.modal.body.patch();
		//
		// 													}, function(newObj){
		//
		// 														tableLoading(data);
		//
		// 														data.dom.modal.hide();
		//
		// 														if(data.onUpdate){
		//
		// 															data.setup.invoices.push(newObj);
		//
		// 															data.onUpdate(data);
		//
		// 														}
		//
		// 												});
		//
		// 											}, 1);
		//
		// 										}
		// 									});
		//
		// 									data.dom.patch();
		//
		// 									data.dom.modal.show();
		//
		//
		//
		// 								}
		// 							}
		// 						}, sb.moduleId);
		//
		// 				}
		//
		// 				data.dom.patch();
		//
		//
		// 			});
		//
		// 		});

			}

			printTable(data);

		},

		showObjectInvoiceTable: function(data){

			if(data){

				if(data.domObj){
					domObj = sb.dom.make(data.domObj.selector);
				}

				if(data.hasOwnProperty('objectId')){
					objectId = data.objectId;
				}

				if(data.hasOwnProperty('dueDate')){
					dueDate = moment(data.dueDate);
				}

				if(data.hasOwnProperty('price')){

					price = data.price;

					priceTotal = totalPrice(price);

				}

			}

			domObj.makeNode('modals', 'container', {});
			domObj.makeNode('info', 'container', {css:'pull-left'});
			domObj.makeNode('table', 'column', {width:12});
			domObj.makeNode('preview', 'column', {width:12});
			domObj.makeNode('finalBreak', 'lineBreak', {spaces:1});

			domObj.build();

			comps.table.notify({
				type: 'display-crud-table-paged',
				data: {
					domObj: domObj.table,
					objectType:'invoices',
					visibleCols: ['name', 'invoice_type_list', 'due_date', 'amount', 'paid', 'balance', 'memo', 'sent_info'],
					data: function(callback, paged){

						sb.data.db.obj.getWhere('invoices', {type_id:objectId, childObjs:1, paged:paged}, function(invoices){

							if(invoices.hasOwnProperty('data')){
								invoiceList = JSON.parse(JSON.stringify(invoices.data));
							}else{
								invoiceList = [];
							}

							if(invoices.length == 0){

								sb.notify({
				                    type:'create-initial-object-invoices',
				                    data:{}
			                    });

								return;
							}

							invoicesTotal = totalInvoices(invoices.data);

							if(invoicesTotal != priceTotal){

								var balance = ((invoicesTotal - priceTotal) / 100);

								if(balance < 0){
									var icon = 'under';
								}else{
									var icon = 'over';
								}

								domObj.info.makeNode('balanceLabel', 'headerText', {size:'x-small', css:'pull-left'}).makeNode('balance', 'label', {size:'', color:'danger', text:'Invoice list '+ icon +' by $'+ Math.abs(balance).toFixed(2) }).notify('click', {
									type:'balance-object-invoices',
									data:{}
								}, sb.moduleId);

								domObj.info.balanceLabel.makeNode('pop', 'popover', {parent: domObj.info.balanceLabel.balance});

								domObj.info.balanceLabel.pop.makeNode('btnGroup', 'buttonGroup', {});

								if(balance > 0){

									domObj.info.balanceLabel.pop['btnGroup'].makeNode('add', 'button', {text: 'Subtract From Invoice', css: 'btn-primary'}).notify('click', {
										type:'add-balance-to-invoice',
										data:{
											balance:invoicesTotal - priceTotal,
											type_id:objectId,
											modals:domObj.modals,
											type:'subtract'
										}
									}, sb.moduleId);

								}else{

									domObj.info.balanceLabel.pop['btnGroup'].makeNode('create', 'button', {text: 'Create New Invoice', css: 'btn-primary'}).notify('click', {
										type:'create-balance-invoice',
										data:{
											balance:invoicesTotal - priceTotal,
											type_id:objectId,
											modals:domObj.modals
										}
									}, sb.moduleId);

									domObj.info.balanceLabel.pop['btnGroup'].makeNode('add', 'button', {text: 'Add to Invoice', css: 'btn-primary'}).notify('click', {
										type:'add-balance-to-invoice',
										data:{
											balance:invoicesTotal - priceTotal,
											type_id:objectId,
											modals:domObj.modals,
											type:'add'
										}
									}, sb.moduleId);

								}

								domObj.info.patch();

							}

							callback(invoices);

						});

					},
					cols:{
						amount:'Total',
						sent_info:'Sent?'
					},
					cells: {
						amount: function(obj){
							return '$'+(obj.amount / 100).toFixed(2);
						},
						paid: function(obj){
							return '$'+(obj.paid / 100).toFixed(2);
						},
						balance: function(obj){

							var balance = ( (obj.amount - obj.paid) / 100);

							if(balance == 0){

								var ret = '<span class="text-success text-bold">$'+ Math.abs(balance).toFixed(2) +'</span>';

							}

							if(balance < 0){

								var ret = '<span class="text-warning text-bold">$'+ Math.abs(balance).toFixed(2) +'</span>';

							}

							if(balance > 0){

								var ret = '<span class="text-danger text-bold">($'+ Math.abs(balance).toFixed(2) +')</span>';

							}

							return ret;
						},
						due_date:function(obj){
							return moment(obj.due_date).format('MMMM Do YYYY');
						},
						memo:function(obj){
							if(obj.memo){
								return obj.memo;
							}else{
								return 'No memo';
							}
						},
						sent_info:function(obj){

							if(_.isEmpty(obj.sent_by)){
								return 'Not sent'
							}else{
								return 'Sent on '+ moment(obj.sent_on).format('MMMM Do YYYY') +' by '+ obj.sent_by.fname +' '+ obj.sent_by.lname;
							}

							return 'test';
						}
					},
					headerButtons:[
						{
							text: 'Pay Multiple Invoices',
			                action: function ( e, dt, node, config ) {
			                    sb.notify({
									type:'begin-invoice-payment',
									data:{
										modals:domObj.modals,
										object:invoiceList
									}
								});
			                }
						},
						{
							text: 'Choose a New Schedule',
			                action: function ( e, dt, node, config ) {
			                    sb.notify({
				                    type:'delete-all-invoices',
				                    data:{}
			                    });
			                }
						}
					],
					buttons: {
						create: {
							type:'create-object-invoice',
							data:{
								objectId:objectId,
								modal:domObj.modals
							}
						},
						view: {
							type:'view-invoice-preview',
							data:{
								domObj:domObj.preview,
								objectId:objectId,
								modals:domObj.modals
							}
						},
						email: {
							type:'email-invoice',
							data:{
								domObj:domObj,
								modals:domObj.modals
							}
						},
						pay: function(obj){

							if(obj.balance > 0){

								return {
										type:'begin-invoice-payment',
										data:{
											modals:domObj.modals
										}
									};

							}

						},
						edit: {
							type:'edit-invoice',
							data:{
								modals:domObj.modals
							}
						},
						erase:  function(obj){

							if(obj.paid == 0){

								return true;

							}

						},
						colVis:false,
						print:false,
						excel:false,
						csv:false,
						filter:false
					}
				}
			});

		},

		submitPayment: function(data){

			var invoice = data.object,
				formData = data.form.process(),
				paymentType = data.type;

			data.modal.footer.makeNode('button', 'button', {text:'Processing <i class="fa fa-circle-o-notch fa-spin"></i>'});

			data.modal.footer.patch();

			var paymentSuccess = true;

			if(formData.completed == true){

				sb.dom.alerts.alert('', 'All fields are required.', 'error');

			}else{

				var paymentObjs = [];

				_.each(invoiceList, function(o){

					if(formData.fields.hasOwnProperty('i-'+ o.id)){

						if(formData.fields['i-'+o.id].value > o.balance){

							sb.dom.alerts.alert('', 'You can\'t pay more than the invoice balance.', 'error');

						}else{

							var ccFee = 0;

							if(formData.fields.paymentType.value == 'card'){

								ccFee = formData.fields['i-'+o.id].value * .03;

							}

							if(paymentSuccess == true){

								o.balance = o.balance - formData.fields['i-'+o.id].value;
								o.paid = o.paid + formData.fields['i-'+o.id].value;

								var paymentObj = {
										amount:formData.fields['i-'+o.id].value,
										invoice:o.id,
										transaction_details:{}
									};

								_.each(formData.fields, function(fieldObj, fieldKey){

									paymentObj.transaction_details[fieldKey] = fieldObj.value;

								});

								paymentObj.transaction_details.ccFee = ccFee;

								paymentObjs.push(paymentObj);

							}

						}

					}

				});

				var i = 0;
				_.each(paymentObjs, function(payment){

// !@TODO.create('payments'

					sb.data.db.obj.create('payments', payment, function(p){

						i++;

						if(!Array.isArray(_.where(invoiceList, {id:p.invoice})[0].payments)){

							_.where(invoiceList, {id:p.invoice})[0].payments = [];

						}

						_.where(invoiceList, {id:p.invoice})[0].payments.push(p.id);

						if(i == paymentObjs.length){

							sb.data.db.obj.update('invoices', invoiceList, function(updated){

								if(updated){

									invoiceList = updated;

									sb.notify({
										type:'invoice-payment-received',
										data:{
											object:invoiceList
										}
									});

								}

							});

						}

					});

				});

			}

		},

		updateInvoices: function(data) {

			// Set variables
			var dom = data.dom;
			var project = data.project;
			var proposal = data.project.proposal;
			var startDate = data.startDate;
			var endDate = data.endDate;
			var callback = data.callback;

			function completeUpdate(didUpdateInvoices) {

				// Hide loader
				$("#loader").fadeOut();

				// Hide alert
				swal.close();

				// Show success
				if (didUpdateInvoices) {
					sb.dom.alerts.alert('Success', 'Invoice dates have been updated!', 'success');
				}

				// Callback
				callback();

			}

			function updateSectionDates(didUpdateInvoices) {

				sb.dom.alerts.ask({
					title: 'Update section dates?',
					text: 'Do you want to update the invoice section dates?'
				}, function(resp){

					if (resp) {

						// Set variables
						var datePropertyText = '';
						if (startDate) {
							datePropertyText = 'start date';
						} else if (endDate) {
							datePropertyText = 'end date';
						}

						sb.dom.alerts.ask({
							title: 'How should we update the section dates?',
							text: 'Do you want to shift the ' + datePropertyText + ' of each section relative to the new date or make them all the same?',
							primaryButtonText: 'Make the Same',
							secondaryButtonText: 'Shift Dates'
						}, function(primaryButtonClicked, secondaryButtonClicked, value) {

							if (primaryButtonClicked || secondaryButtonClicked) {

								// Show loader
								$("#loader").fadeIn();

								// Disable buttons
								swal.disableButtons();

								// Set the proposal menu id
								var proposalMenuID = proposal.menu.hasOwnProperty('id') ? proposal.menu.id : proposal.menu;

								if (proposalMenuID) {

									sb.data.db.obj.getWhere('inventory_menu', {'id': proposalMenuID}, function(inventoryMenus) {

										if (inventoryMenus.length > 0) {

											_.each(inventoryMenus, function(inventoryMenu) {

												sb.notify({
													type:'update-menu-section-dates',
													data:{
														obj: inventoryMenu,
														project: project,
														startDate: startDate,
														endDate: endDate,
														isRelative: secondaryButtonClicked,
														callback:function(updated) {

															completeUpdate(true);

														}
													}
												});

											});

										} else {

											completeUpdate(didUpdateInvoices);

										}

									});

								} else {

									completeUpdate(didUpdateInvoices);

								}

							} else {

								completeUpdate(didUpdateInvoices);

							}

						});

					} else {

						completeUpdate(didUpdateInvoices);

					}

				});

			}

			if (!proposal.invoice_template) {

				sb.dom.alerts.ask({
					title: 'Update invoices?',
					text: 'Do you want to update the due dates of all unpaid invoices?'
				}, function(resp) {

					if (resp) {

						// Show loader
						$("#loader").fadeIn();

						// Disable buttons
						swal.disableButtons();

						sb.notify({
							type:'update-invoice-due-dates',
							data:{
								proposalId: proposal.id,
								callback:function(updated) {

									sb.data.db.obj.getById('groups', project.id, function(updatedProject) {

										sb.notify({
											type:'get-updated-menu-pricing-breakout',
											data:{
												obj: project,
												onComplete: function(price) {

													sb.notify({
														type:'balance-project-invoices',
														data:{
															state:{
																objectId: updatedProject.proposal.id,
																project: updatedProject,
																price: price,
																clientId: project.main_contact.company,
																contactId: project.main_contact.id,
																object: updatedProject.proposal
															},
															callback:function(invData) {

																// Hide loader
																$("#loader").fadeOut();

																// Update section dates
																updateSectionDates(true);

															}
														}
													});

												}
											}
										});

									}, 3, false, true);

								}
							}
						});

					} else {

						// Update section dates
						updateSectionDates();

					}

				});

			} else {

				// Update section dates
				updateSectionDates();

			}

		},

		updateInvoiceDueDates: function(data){

			sb.data.db.obj.getWhere('invoices', {
				related_object:data.proposalId,
				paid:0,
				childObjs:{
					id:true
				}
			}, function(invoices) {

				sb.data.db.obj.erase('invoices', _.pluck(invoices, 'id'), function(done) {

					if (data.callback) {

						data.callback(done);

					}

				});

			});

		},

		updateMenuSectionDates: function(data) {

			// Set variables
			var menu = data.obj;
			var currentStartDate = data.project.start_date;
			var currentEndDate = data.project.end_date;
			var newStartDate = data.startDate;
			var newEndDate = data.endDate;
			var isRelative = data.isRelative;

			if (isRelative) {

				// Update dates to be relative (shift) to what was entered
				if (newStartDate) {

					var currentStartDate = moment(currentStartDate, 'YYYY-MM-DD HH:mm:ss +SSSS');
					var newStartDate = moment(newStartDate, 'YYYY-MM-DD HH:mm:ss +SSSS');
					var diff = newStartDate.diff(currentStartDate, 'seconds');
					var diffType = 'add';

					if(diff < 0){
						diffType = 'subtract';
						diff = diff * -1;
					}

				} else if (newEndDate) {

					var currentEndDate = moment(currentEndDate, 'YYYY-MM-DD HH:mm:ss +SSSS');
					var newEndDate = moment(newEndDate, 'YYYY-MM-DD HH:mm:ss +SSSS');
					var endDiff = newEndDate.diff(currentEndDate, 'seconds');
					var endDiffType = 'add';

					if(endDiff < 0){
						endDiffType = 'subtract';
						endDiff = endDiff * -1;
					}

				}

				_.each(menu.sections, function(section, i) {

					var secDiff = 0;
					var secDiffType = 'add';

					if (endDiff) {

						if (section.to) {

							if (moment(section.to, 'YYYY-MM-DD HH:mm:ss +SSSS').format('YYYY-MM-DD HH:mm:ss') == currentEndDate.format('YYYY-MM-DD HH:mm:ss')) {

								menu.sections[i].to = moment(section.to, 'YYYY-MM-DD HH:mm:ss +SSSS').clone()[endDiffType](endDiff, 'seconds').format('YYYY-MM-DD HH:mm:ss');

								if (section.from) {
									menu.sections[i].from = moment(section.from).format();
								}

							}

						}

					} else {

						if (section.from) {

							if (section.to) {
								secDiff = moment(section.to).diff(moment(section.from), 'seconds');
							}

							menu.sections[i].from = moment(moment(section.from)[diffType](diff, 'seconds').format());

							if(section.to){
								menu.sections[i].to = moment(menu.sections[i].from.clone().add(secDiff, 'seconds').format());
							}

						}

					}

				});

			} else {

				// Update dates to be exactly the same as what was entered
				_.each(menu.sections, function(section, i) {

					if (newStartDate) {

						menu.sections[i].from = moment(newStartDate).format();
						menu.sections[i].to = moment(currentEndDate).format();

					} else if (newEndDate) {

						menu.sections[i].from = moment(currentStartDate).format();
						menu.sections[i].to = moment(newEndDate).format();

					} else {

						menu.sections[i].from = moment(currentStartDate).format();
						menu.sections[i].to = moment(currentEndDate).format();

					}

				});

			}

			sb.data.db.controller(
				'updateMenuSections'
				, {
					menuId: menu.id,
					sections: menu.sections
				}
				, function (response) {

					data.callback(response);

					// menu_ui.prepare(menu);
					// menu_ui.patch();

				}
			);

		},

		updateTable: function(data){

			if(comps.hasOwnProperty('table')){

				comps.table.notify({
					type:'show-object-invoice-list',
					data:{}
				});

			}

		},

		view: function(data){

			domObj = sb.dom.make(data.domObj.selector);

			domObj.makeNode('col', 'column', {width:9, offset:3}).makeNode('cont', 'container', {css:'pda-container'}).makeNode('loader', 'text', {text:'Loading your invoice...<br />'+sb.dom.loadingGIF});

			domObj.makeNode('modals', 'container', {});

			domObj.build();

			sb.data.db.obj.getById('invoices', data.object.id, function(inv){
				sb.data.obj.getById('events', inv.object_id, function(event){
					sb.data.obj.getWhere('contacts', {id:event.main_contact, childObjs:1}, function(contact){

						delete domObj.col.cont.loader;

						domObj.col.cont.makeNode('buttons', 'buttonGroup', {});

						domObj.col.cont.buttons.makeNode('pay', 'button', {text:'Pay Now', css:'btn-lg'}).notify('click', {
							type:'begin-invoice-payment',
							data:{
								object:inv,
								modal:domObj.modals
							}
						}, sb.moduleId);

						domObj.col.cont.makeNode('invoice', 'text', {text:createHTMLString(inv, event, contact)});

						domObj.col.cont.makeNode('buttonsLower', 'buttonGroup', {});

						domObj.col.cont.buttonsLower.makeNode('pay', 'button', {text:'Pay Now', css:'btn-lg'}).notify('click', {
							type:'begin-invoice-payment',
							data:{
								object:inv,
								modal:domObj.modals
							}
						}, sb.moduleId);

						domObj.col.cont.patch();

					});
				});

			});

		},

		viewAll: function(data){

			buildUI(data.domObj, data.objectType, data.objectId, data.contactId);

			tableUI.state(data.objectType, data.objectId, data.contactId, data.price, data.dueDate, data);

			tableUI.state.show();

		},

		viewAll2: function(data){

			data.dom.empty();

			var balanceDom = data.dom.makeNode('balance', 'div', {});
			var tableDom = data.dom.makeNode('table', 'div', {});

			data.dom.patch();

			data.state.project = data.project;

			sb.data.db.controller('balanceProjectInvoices', {projectId:data.state.project.id}, function(invoices){

				createCollectionUI(tableDom, data.state, data.draw);

				if(data.paymentsDom){

					data.state.invoices = invoices;

					createPaymentsCollectionUI(data.paymentsDom, data.state);

				}

			}, false, true);


		},

		viewPayments: function(data){

			data.modals.makeNode('payments', 'modal', {});

			data.modals.payments.body.makeNode('header', 'headerText', {text:'Invoice Payments', size:'small'});

			data.modals.payments.body.makeNode('table', 'container', {});

			data.modals.patch();

			data.modals.payments.show();

			comps.paymentsTable = sb.createComponent('crudPaged');

			comps.paymentsTable.notify({
				type: 'display-crud-table-paged',
				data: {
					domObj: data.modals.payments.body.table,
					objectType:'payments',
					visibleCols: ['date_created', 'amount', 'id'],
					data: data.object.payments,
					cols:{},
					cells: {
						date_created:function(obj){
							return moment(obj.date_created).format('MM/DD/YYYY h:mm a');
						},
						amount:function(obj){
							return '$'+(obj.amount/100).formatMoney(2);
						}
					},
					headerButtons:[],
					buttons: {
						create: false,
						view: false,
						edit: false,
						erase: false,
						colVis:false,
						print:false,
						excel:false,
						csv:false,
						filter:false
					}
				}
			});

		},

		viewPreview: function(data){

			data.domObj.makeNode('break', 'lineBreak', {spaces:1});

			data.domObj.makeNode('loader', 'text', {text:sb.dom.loadingGIF});

			data.domObj.patch();

			sb.data.db.obj.getById('', data.objectId, function(obj){

				sb.data.db.obj.getById('', obj.main_contact, function(contact){

					data.domObj.makeNode('btnGroup', 'buttonGroup', {});

					data.domObj.btnGroup.makeNode('close', 'button', {text:'Close Preview <i class="fa fa-times"></i>'}).notify('click', {
						type:'close-invoice-preview',
						data:{
							domObj:data.domObj,
							object:data.object
						}
					}, sb.moduleId);

					data.domObj.btnGroup.makeNode('download', 'button', {text:'Download PDF <i class="fa fa-download"></i>'}).notify('click', {
						type:'download-invoice-pdf',
						data:{
							domObj:data.domObj,
							object:data.object,
							event:obj,
							contact:contact
						}
					}, sb.moduleId);

					data.domObj.btnGroup.makeNode('send', 'button', {text:'Send via Email <i class="fa fa-paper-plane"></i>'}).notify('click', {
						type:'email-invoice',
						data:{
							domObj:data.domObj,
							object:data.object,
							modals:data.modals
						}
					}, sb.moduleId);

					if(data.object.balance > 0){

						data.domObj.btnGroup.makeNode('pay', 'button', {text:'Pay Now <i class="fa fa-credit-card"></i>'}).notify('click', {
							type:'begin-invoice-payment',
							data:{
								object:data.object,
								modals:data.modals
							}
						}, sb.moduleId);

					}

					if(data.object.payments != null){

						data.domObj.btnGroup.makeNode('payments', 'button', {text:'View Payments <i class="fa fa-usd"></i>'}).notify('click', {
							type:'view-invoice-payments',
							data:{
								object:data.object,
								modals:data.modals
							}
						}, sb.moduleId);

					}

					data.domObj.makeNode('invoiceViewer', 'container', {css:'pda-Panel'});

					data.domObj.invoiceViewer.makeNode('cont', 'container', {css:'pda-container'});

					data.domObj.invoiceViewer.cont.makeNode('text', 'text', {text:createHTMLString(data.object, obj, contact)});

					delete data.domObj.loader;

					data.domObj.patch();

				}, 1);

			}, 1);

		},

		viewSingleInvoice: function(data){

			//comps.payment = sb.createComponent('paymentMethods');
			//comps.emails = sb.createComponent('emails');
			//comps.notes = sb.createComponent('notes2');

			if(data.hasOwnProperty('paymentCompletion')){
				paymentCompletion = data.paymentCompletion;
			}

			singleInvoice(data.invoice, data.domObj, data.backButton, function(domObj){

				domObj.dom.patch();
				domObj.after(domObj);

			});

		}
	}

});
